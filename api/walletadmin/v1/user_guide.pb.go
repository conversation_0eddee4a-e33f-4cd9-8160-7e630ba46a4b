// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/user_guide.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户指南内容
type UserGuideContentInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 正文文本
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// 图片URL
	PhotoUrl string `protobuf:"bytes,2,opt,name=photo_url,json=photoUrl,proto3" json:"photo_url,omitempty"`
	// 是否展示
	Display       bool `protobuf:"varint,3,opt,name=display,proto3" json:"display,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserGuideContentInfo) Reset() {
	*x = UserGuideContentInfo{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserGuideContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGuideContentInfo) ProtoMessage() {}

func (x *UserGuideContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGuideContentInfo.ProtoReflect.Descriptor instead.
func (*UserGuideContentInfo) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{0}
}

func (x *UserGuideContentInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UserGuideContentInfo) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

func (x *UserGuideContentInfo) GetDisplay() bool {
	if x != nil {
		return x.Display
	}
	return false
}

// 创建用户指南请求
type CreateUserGuideReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID
	CategoryId uint64 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 语言
	Language string `protobuf:"bytes,2,opt,name=language,proto3" json:"language,omitempty"`
	// 标题
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// 简介
	Summary string `protobuf:"bytes,4,opt,name=summary,proto3" json:"summary,omitempty"`
	// 是否展示
	Display bool `protobuf:"varint,5,opt,name=display,proto3" json:"display,omitempty"`
	// 内容列表
	Contents      []*UserGuideContentInfo `protobuf:"bytes,6,rep,name=contents,proto3" json:"contents,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserGuideReq) Reset() {
	*x = CreateUserGuideReq{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserGuideReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserGuideReq) ProtoMessage() {}

func (x *CreateUserGuideReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserGuideReq.ProtoReflect.Descriptor instead.
func (*CreateUserGuideReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{1}
}

func (x *CreateUserGuideReq) GetCategoryId() uint64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CreateUserGuideReq) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *CreateUserGuideReq) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateUserGuideReq) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *CreateUserGuideReq) GetDisplay() bool {
	if x != nil {
		return x.Display
	}
	return false
}

func (x *CreateUserGuideReq) GetContents() []*UserGuideContentInfo {
	if x != nil {
		return x.Contents
	}
	return nil
}

// 创建用户指南响应
type CreateUserGuideReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserGuideReply) Reset() {
	*x = CreateUserGuideReply{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserGuideReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserGuideReply) ProtoMessage() {}

func (x *CreateUserGuideReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserGuideReply.ProtoReflect.Descriptor instead.
func (*CreateUserGuideReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{2}
}

// 更新用户指南请求
type UpdateUserGuideReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户指南ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 分类ID
	CategoryId uint64 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 语言
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	// 标题
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// 简介
	Summary string `protobuf:"bytes,5,opt,name=summary,proto3" json:"summary,omitempty"`
	// 是否展示
	Display bool `protobuf:"varint,6,opt,name=display,proto3" json:"display,omitempty"`
	// 内容列表
	Contents      []*UserGuideContentInfo `protobuf:"bytes,7,rep,name=contents,proto3" json:"contents,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserGuideReq) Reset() {
	*x = UpdateUserGuideReq{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserGuideReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserGuideReq) ProtoMessage() {}

func (x *UpdateUserGuideReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserGuideReq.ProtoReflect.Descriptor instead.
func (*UpdateUserGuideReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateUserGuideReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserGuideReq) GetCategoryId() uint64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *UpdateUserGuideReq) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UpdateUserGuideReq) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UpdateUserGuideReq) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *UpdateUserGuideReq) GetDisplay() bool {
	if x != nil {
		return x.Display
	}
	return false
}

func (x *UpdateUserGuideReq) GetContents() []*UserGuideContentInfo {
	if x != nil {
		return x.Contents
	}
	return nil
}

// 删除用户指南请求
type DeleteUserGuideReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户指南ID
	Id            uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserGuideReq) Reset() {
	*x = DeleteUserGuideReq{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserGuideReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserGuideReq) ProtoMessage() {}

func (x *DeleteUserGuideReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserGuideReq.ProtoReflect.Descriptor instead.
func (*DeleteUserGuideReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteUserGuideReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 用户指南列表请求
type ListUserGuidesReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID筛选
	CategoryId uint64 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 语言筛选
	Language string `protobuf:"bytes,2,opt,name=language,proto3" json:"language,omitempty"`
	// 页码
	Page int64 `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	// 每页数量
	PageSize      int64 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserGuidesReq) Reset() {
	*x = ListUserGuidesReq{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserGuidesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserGuidesReq) ProtoMessage() {}

func (x *ListUserGuidesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserGuidesReq.ProtoReflect.Descriptor instead.
func (*ListUserGuidesReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{5}
}

func (x *ListUserGuidesReq) GetCategoryId() uint64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ListUserGuidesReq) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ListUserGuidesReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListUserGuidesReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 用户指南列表响应
type ListUserGuidesReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户指南列表
	List []*UserGuideInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserGuidesReply) Reset() {
	*x = ListUserGuidesReply{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserGuidesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserGuidesReply) ProtoMessage() {}

func (x *ListUserGuidesReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserGuidesReply.ProtoReflect.Descriptor instead.
func (*ListUserGuidesReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{6}
}

func (x *ListUserGuidesReply) GetList() []*UserGuideInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListUserGuidesReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// 获取用户指南详情请求
type GetUserGuideReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户指南ID
	Id            uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserGuideReq) Reset() {
	*x = GetUserGuideReq{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserGuideReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGuideReq) ProtoMessage() {}

func (x *GetUserGuideReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGuideReq.ProtoReflect.Descriptor instead.
func (*GetUserGuideReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserGuideReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 用户指南信息
type UserGuideInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户指南ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 分类ID
	CategoryId uint64 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// 语言
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	// 标题
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// 简介
	Summary string `protobuf:"bytes,5,opt,name=summary,proto3" json:"summary,omitempty"`
	// 是否展示
	Display bool `protobuf:"varint,6,opt,name=display,proto3" json:"display,omitempty"`
	// 内容列表
	Contents []*UserGuideContentInfo `protobuf:"bytes,7,rep,name=contents,proto3" json:"contents,omitempty"`
	// 分类
	Category      string `protobuf:"bytes,8,opt,name=category,proto3" json:"category,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserGuideInfo) Reset() {
	*x = UserGuideInfo{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserGuideInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGuideInfo) ProtoMessage() {}

func (x *UserGuideInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGuideInfo.ProtoReflect.Descriptor instead.
func (*UserGuideInfo) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{8}
}

func (x *UserGuideInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserGuideInfo) GetCategoryId() uint64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *UserGuideInfo) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UserGuideInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UserGuideInfo) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *UserGuideInfo) GetDisplay() bool {
	if x != nil {
		return x.Display
	}
	return false
}

func (x *UserGuideInfo) GetContents() []*UserGuideContentInfo {
	if x != nil {
		return x.Contents
	}
	return nil
}

func (x *UserGuideInfo) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

// 创建用户指南分类请求
type CreateUserGuideCategoryReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 语言
	Language string `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 图标URL
	LogoUrl string `protobuf:"bytes,3,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 是否展示
	Display       bool `protobuf:"varint,4,opt,name=display,proto3" json:"display,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserGuideCategoryReq) Reset() {
	*x = CreateUserGuideCategoryReq{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserGuideCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserGuideCategoryReq) ProtoMessage() {}

func (x *CreateUserGuideCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserGuideCategoryReq.ProtoReflect.Descriptor instead.
func (*CreateUserGuideCategoryReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{9}
}

func (x *CreateUserGuideCategoryReq) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *CreateUserGuideCategoryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateUserGuideCategoryReq) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *CreateUserGuideCategoryReq) GetDisplay() bool {
	if x != nil {
		return x.Display
	}
	return false
}

// 创建用户指南分类响应
type CreateUserGuideCategoryReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserGuideCategoryReply) Reset() {
	*x = CreateUserGuideCategoryReply{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserGuideCategoryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserGuideCategoryReply) ProtoMessage() {}

func (x *CreateUserGuideCategoryReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserGuideCategoryReply.ProtoReflect.Descriptor instead.
func (*CreateUserGuideCategoryReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{10}
}

// 更新用户指南分类请求
type UpdateUserGuideCategoryReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 语言
	Language string `protobuf:"bytes,2,opt,name=language,proto3" json:"language,omitempty"`
	// 名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 图标URL
	LogoUrl string `protobuf:"bytes,4,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 是否展示
	Display       bool `protobuf:"varint,5,opt,name=display,proto3" json:"display,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserGuideCategoryReq) Reset() {
	*x = UpdateUserGuideCategoryReq{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserGuideCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserGuideCategoryReq) ProtoMessage() {}

func (x *UpdateUserGuideCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserGuideCategoryReq.ProtoReflect.Descriptor instead.
func (*UpdateUserGuideCategoryReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateUserGuideCategoryReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserGuideCategoryReq) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UpdateUserGuideCategoryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateUserGuideCategoryReq) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *UpdateUserGuideCategoryReq) GetDisplay() bool {
	if x != nil {
		return x.Display
	}
	return false
}

// 删除用户指南分类请求
type DeleteUserGuideCategoryReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID
	Id            uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserGuideCategoryReq) Reset() {
	*x = DeleteUserGuideCategoryReq{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserGuideCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserGuideCategoryReq) ProtoMessage() {}

func (x *DeleteUserGuideCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserGuideCategoryReq.ProtoReflect.Descriptor instead.
func (*DeleteUserGuideCategoryReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteUserGuideCategoryReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 用户指南分类列表请求
type ListUserGuideCategoriesReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 语言筛选
	Language string `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`
	// 页码
	Page int64 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	// 每页数量
	PageSize      int64 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserGuideCategoriesReq) Reset() {
	*x = ListUserGuideCategoriesReq{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserGuideCategoriesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserGuideCategoriesReq) ProtoMessage() {}

func (x *ListUserGuideCategoriesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserGuideCategoriesReq.ProtoReflect.Descriptor instead.
func (*ListUserGuideCategoriesReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{13}
}

func (x *ListUserGuideCategoriesReq) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ListUserGuideCategoriesReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListUserGuideCategoriesReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 用户指南分类列表响应
type ListUserGuideCategoriesReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类列表
	List []*UserGuideCategoryInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserGuideCategoriesReply) Reset() {
	*x = ListUserGuideCategoriesReply{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserGuideCategoriesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserGuideCategoriesReply) ProtoMessage() {}

func (x *ListUserGuideCategoriesReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserGuideCategoriesReply.ProtoReflect.Descriptor instead.
func (*ListUserGuideCategoriesReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{14}
}

func (x *ListUserGuideCategoriesReply) GetList() []*UserGuideCategoryInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListUserGuideCategoriesReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// 获取用户指南分类详情请求
type GetUserGuideCategoryReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID
	Id            uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserGuideCategoryReq) Reset() {
	*x = GetUserGuideCategoryReq{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserGuideCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserGuideCategoryReq) ProtoMessage() {}

func (x *GetUserGuideCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserGuideCategoryReq.ProtoReflect.Descriptor instead.
func (*GetUserGuideCategoryReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{15}
}

func (x *GetUserGuideCategoryReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 用户指南分类信息
type UserGuideCategoryInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 语言
	Language string `protobuf:"bytes,2,opt,name=language,proto3" json:"language,omitempty"`
	// 名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 图标URL
	LogoUrl string `protobuf:"bytes,4,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 是否展示
	Display bool `protobuf:"varint,5,opt,name=display,proto3" json:"display,omitempty"`
	// 文章数量
	GuideCount    int64 `protobuf:"varint,6,opt,name=guide_count,json=guideCount,proto3" json:"guide_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserGuideCategoryInfo) Reset() {
	*x = UserGuideCategoryInfo{}
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserGuideCategoryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserGuideCategoryInfo) ProtoMessage() {}

func (x *UserGuideCategoryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_guide_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserGuideCategoryInfo.ProtoReflect.Descriptor instead.
func (*UserGuideCategoryInfo) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_guide_proto_rawDescGZIP(), []int{16}
}

func (x *UserGuideCategoryInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserGuideCategoryInfo) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UserGuideCategoryInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserGuideCategoryInfo) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *UserGuideCategoryInfo) GetDisplay() bool {
	if x != nil {
		return x.Display
	}
	return false
}

func (x *UserGuideCategoryInfo) GetGuideCount() int64 {
	if x != nil {
		return x.GuideCount
	}
	return 0
}

var File_api_walletadmin_v1_user_guide_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_user_guide_proto_rawDesc = "" +
	"\n" +
	"#api/walletadmin/v1/user_guide.proto\x12\x12api.walletadmin.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\"g\n" +
	"\x14UserGuideContentInfo\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\x1b\n" +
	"\tphoto_url\x18\x02 \x01(\tR\bphotoUrl\x12\x18\n" +
	"\adisplay\x18\x03 \x01(\bR\adisplay\"\xfc\x01\n" +
	"\x12CreateUserGuideReq\x12(\n" +
	"\vcategory_id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\n" +
	"categoryId\x12#\n" +
	"\blanguage\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\blanguage\x12\x1d\n" +
	"\x05title\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x05title\x12\x18\n" +
	"\asummary\x18\x04 \x01(\tR\asummary\x12\x18\n" +
	"\adisplay\x18\x05 \x01(\bR\adisplay\x12D\n" +
	"\bcontents\x18\x06 \x03(\v2(.api.walletadmin.v1.UserGuideContentInfoR\bcontents\"\x16\n" +
	"\x14CreateUserGuideReply\"\xfa\x01\n" +
	"\x12UpdateUserGuideReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\x04R\n" +
	"categoryId\x12\x1a\n" +
	"\blanguage\x18\x03 \x01(\tR\blanguage\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x18\n" +
	"\asummary\x18\x05 \x01(\tR\asummary\x12\x18\n" +
	"\adisplay\x18\x06 \x01(\bR\adisplay\x12D\n" +
	"\bcontents\x18\a \x03(\v2(.api.walletadmin.v1.UserGuideContentInfoR\bcontents\"-\n" +
	"\x12DeleteUserGuideReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"\x95\x01\n" +
	"\x11ListUserGuidesReq\x12\x1f\n" +
	"\vcategory_id\x18\x01 \x01(\x04R\n" +
	"categoryId\x12\x1a\n" +
	"\blanguage\x18\x02 \x01(\tR\blanguage\x12\x1b\n" +
	"\x04page\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02(\x01R\x04page\x12&\n" +
	"\tpage_size\x18\x04 \x01(\x03B\t\xbaH\x06\"\x04\x18d(\x01R\bpageSize\"m\n" +
	"\x13ListUserGuidesReply\x125\n" +
	"\x04list\x18\x01 \x03(\v2!.api.walletadmin.v1.UserGuideInfoR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount\"*\n" +
	"\x0fGetUserGuideReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"\x88\x02\n" +
	"\rUserGuideInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\x04R\n" +
	"categoryId\x12\x1a\n" +
	"\blanguage\x18\x03 \x01(\tR\blanguage\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x18\n" +
	"\asummary\x18\x05 \x01(\tR\asummary\x12\x18\n" +
	"\adisplay\x18\x06 \x01(\bR\adisplay\x12D\n" +
	"\bcontents\x18\a \x03(\v2(.api.walletadmin.v1.UserGuideContentInfoR\bcontents\x12\x1a\n" +
	"\bcategory\x18\b \x01(\tR\bcategory\"\x93\x01\n" +
	"\x1aCreateUserGuideCategoryReq\x12#\n" +
	"\blanguage\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\blanguage\x12\x1b\n" +
	"\x04name\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x04name\x12\x19\n" +
	"\blogo_url\x18\x03 \x01(\tR\alogoUrl\x12\x18\n" +
	"\adisplay\x18\x04 \x01(\bR\adisplay\"\x1e\n" +
	"\x1cCreateUserGuideCategoryReply\"\xac\x01\n" +
	"\x1aUpdateUserGuideCategoryReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12#\n" +
	"\blanguage\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\blanguage\x12\x1b\n" +
	"\x04name\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x04name\x12\x19\n" +
	"\blogo_url\x18\x04 \x01(\tR\alogoUrl\x12\x18\n" +
	"\adisplay\x18\x05 \x01(\bR\adisplay\"5\n" +
	"\x1aDeleteUserGuideCategoryReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"}\n" +
	"\x1aListUserGuideCategoriesReq\x12\x1a\n" +
	"\blanguage\x18\x01 \x01(\tR\blanguage\x12\x1b\n" +
	"\x04page\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02(\x01R\x04page\x12&\n" +
	"\tpage_size\x18\x03 \x01(\x03B\t\xbaH\x06\"\x04\x18d(\x01R\bpageSize\"~\n" +
	"\x1cListUserGuideCategoriesReply\x12=\n" +
	"\x04list\x18\x01 \x03(\v2).api.walletadmin.v1.UserGuideCategoryInfoR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount\"2\n" +
	"\x17GetUserGuideCategoryReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"\xad\x01\n" +
	"\x15UserGuideCategoryInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1a\n" +
	"\blanguage\x18\x02 \x01(\tR\blanguage\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x19\n" +
	"\blogo_url\x18\x04 \x01(\tR\alogoUrl\x12\x18\n" +
	"\adisplay\x18\x05 \x01(\bR\adisplay\x12\x1f\n" +
	"\vguide_count\x18\x06 \x01(\x03R\n" +
	"guideCount2\x9f\v\n" +
	"\x10UserGuideService\x12\x85\x01\n" +
	"\x0fCreateUserGuide\x12&.api.walletadmin.v1.CreateUserGuideReq\x1a(.api.walletadmin.v1.CreateUserGuideReply\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/admin/v1/user-guides\x12x\n" +
	"\x0fUpdateUserGuide\x12&.api.walletadmin.v1.UpdateUserGuideReq\x1a\x16.google.protobuf.Empty\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\x1a\x1a/admin/v1/user-guides/{id}\x12u\n" +
	"\x0fDeleteUserGuide\x12&.api.walletadmin.v1.DeleteUserGuideReq\x1a\x16.google.protobuf.Empty\"\"\x82\xd3\xe4\x93\x02\x1c*\x1a/admin/v1/user-guides/{id}\x12\x7f\n" +
	"\x0eListUserGuides\x12%.api.walletadmin.v1.ListUserGuidesReq\x1a'.api.walletadmin.v1.ListUserGuidesReply\"\x1d\x82\xd3\xe4\x93\x02\x17\x12\x15/admin/v1/user-guides\x12z\n" +
	"\fGetUserGuide\x12#.api.walletadmin.v1.GetUserGuideReq\x1a!.api.walletadmin.v1.UserGuideInfo\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/admin/v1/user-guides/{id}\x12\xa7\x01\n" +
	"\x17CreateUserGuideCategory\x12..api.walletadmin.v1.CreateUserGuideCategoryReq\x1a0.api.walletadmin.v1.CreateUserGuideCategoryReply\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/admin/v1/user-guide-categories\x12\x92\x01\n" +
	"\x17UpdateUserGuideCategory\x12..api.walletadmin.v1.UpdateUserGuideCategoryReq\x1a\x16.google.protobuf.Empty\"/\x82\xd3\xe4\x93\x02):\x01*\x1a$/admin/v1/user-guide-categories/{id}\x12\x8f\x01\n" +
	"\x17DeleteUserGuideCategory\x12..api.walletadmin.v1.DeleteUserGuideCategoryReq\x1a\x16.google.protobuf.Empty\",\x82\xd3\xe4\x93\x02&*$/admin/v1/user-guide-categories/{id}\x12\xa4\x01\n" +
	"\x17ListUserGuideCategories\x12..api.walletadmin.v1.ListUserGuideCategoriesReq\x1a0.api.walletadmin.v1.ListUserGuideCategoriesReply\"'\x82\xd3\xe4\x93\x02!\x12\x1f/admin/v1/user-guide-categories\x12\x9c\x01\n" +
	"\x14GetUserGuideCategory\x12+.api.walletadmin.v1.GetUserGuideCategoryReq\x1a).api.walletadmin.v1.UserGuideCategoryInfo\",\x82\xd3\xe4\x93\x02&\x12$/admin/v1/user-guide-categories/{id}B\xb4\x01\n" +
	"\x16com.api.walletadmin.v1B\x0eUserGuideProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_user_guide_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_user_guide_proto_rawDescData []byte
)

func file_api_walletadmin_v1_user_guide_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_user_guide_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_user_guide_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_user_guide_proto_rawDesc), len(file_api_walletadmin_v1_user_guide_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_user_guide_proto_rawDescData
}

var file_api_walletadmin_v1_user_guide_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_api_walletadmin_v1_user_guide_proto_goTypes = []any{
	(*UserGuideContentInfo)(nil),         // 0: api.walletadmin.v1.UserGuideContentInfo
	(*CreateUserGuideReq)(nil),           // 1: api.walletadmin.v1.CreateUserGuideReq
	(*CreateUserGuideReply)(nil),         // 2: api.walletadmin.v1.CreateUserGuideReply
	(*UpdateUserGuideReq)(nil),           // 3: api.walletadmin.v1.UpdateUserGuideReq
	(*DeleteUserGuideReq)(nil),           // 4: api.walletadmin.v1.DeleteUserGuideReq
	(*ListUserGuidesReq)(nil),            // 5: api.walletadmin.v1.ListUserGuidesReq
	(*ListUserGuidesReply)(nil),          // 6: api.walletadmin.v1.ListUserGuidesReply
	(*GetUserGuideReq)(nil),              // 7: api.walletadmin.v1.GetUserGuideReq
	(*UserGuideInfo)(nil),                // 8: api.walletadmin.v1.UserGuideInfo
	(*CreateUserGuideCategoryReq)(nil),   // 9: api.walletadmin.v1.CreateUserGuideCategoryReq
	(*CreateUserGuideCategoryReply)(nil), // 10: api.walletadmin.v1.CreateUserGuideCategoryReply
	(*UpdateUserGuideCategoryReq)(nil),   // 11: api.walletadmin.v1.UpdateUserGuideCategoryReq
	(*DeleteUserGuideCategoryReq)(nil),   // 12: api.walletadmin.v1.DeleteUserGuideCategoryReq
	(*ListUserGuideCategoriesReq)(nil),   // 13: api.walletadmin.v1.ListUserGuideCategoriesReq
	(*ListUserGuideCategoriesReply)(nil), // 14: api.walletadmin.v1.ListUserGuideCategoriesReply
	(*GetUserGuideCategoryReq)(nil),      // 15: api.walletadmin.v1.GetUserGuideCategoryReq
	(*UserGuideCategoryInfo)(nil),        // 16: api.walletadmin.v1.UserGuideCategoryInfo
	(*emptypb.Empty)(nil),                // 17: google.protobuf.Empty
}
var file_api_walletadmin_v1_user_guide_proto_depIdxs = []int32{
	0,  // 0: api.walletadmin.v1.CreateUserGuideReq.contents:type_name -> api.walletadmin.v1.UserGuideContentInfo
	0,  // 1: api.walletadmin.v1.UpdateUserGuideReq.contents:type_name -> api.walletadmin.v1.UserGuideContentInfo
	8,  // 2: api.walletadmin.v1.ListUserGuidesReply.list:type_name -> api.walletadmin.v1.UserGuideInfo
	0,  // 3: api.walletadmin.v1.UserGuideInfo.contents:type_name -> api.walletadmin.v1.UserGuideContentInfo
	16, // 4: api.walletadmin.v1.ListUserGuideCategoriesReply.list:type_name -> api.walletadmin.v1.UserGuideCategoryInfo
	1,  // 5: api.walletadmin.v1.UserGuideService.CreateUserGuide:input_type -> api.walletadmin.v1.CreateUserGuideReq
	3,  // 6: api.walletadmin.v1.UserGuideService.UpdateUserGuide:input_type -> api.walletadmin.v1.UpdateUserGuideReq
	4,  // 7: api.walletadmin.v1.UserGuideService.DeleteUserGuide:input_type -> api.walletadmin.v1.DeleteUserGuideReq
	5,  // 8: api.walletadmin.v1.UserGuideService.ListUserGuides:input_type -> api.walletadmin.v1.ListUserGuidesReq
	7,  // 9: api.walletadmin.v1.UserGuideService.GetUserGuide:input_type -> api.walletadmin.v1.GetUserGuideReq
	9,  // 10: api.walletadmin.v1.UserGuideService.CreateUserGuideCategory:input_type -> api.walletadmin.v1.CreateUserGuideCategoryReq
	11, // 11: api.walletadmin.v1.UserGuideService.UpdateUserGuideCategory:input_type -> api.walletadmin.v1.UpdateUserGuideCategoryReq
	12, // 12: api.walletadmin.v1.UserGuideService.DeleteUserGuideCategory:input_type -> api.walletadmin.v1.DeleteUserGuideCategoryReq
	13, // 13: api.walletadmin.v1.UserGuideService.ListUserGuideCategories:input_type -> api.walletadmin.v1.ListUserGuideCategoriesReq
	15, // 14: api.walletadmin.v1.UserGuideService.GetUserGuideCategory:input_type -> api.walletadmin.v1.GetUserGuideCategoryReq
	2,  // 15: api.walletadmin.v1.UserGuideService.CreateUserGuide:output_type -> api.walletadmin.v1.CreateUserGuideReply
	17, // 16: api.walletadmin.v1.UserGuideService.UpdateUserGuide:output_type -> google.protobuf.Empty
	17, // 17: api.walletadmin.v1.UserGuideService.DeleteUserGuide:output_type -> google.protobuf.Empty
	6,  // 18: api.walletadmin.v1.UserGuideService.ListUserGuides:output_type -> api.walletadmin.v1.ListUserGuidesReply
	8,  // 19: api.walletadmin.v1.UserGuideService.GetUserGuide:output_type -> api.walletadmin.v1.UserGuideInfo
	10, // 20: api.walletadmin.v1.UserGuideService.CreateUserGuideCategory:output_type -> api.walletadmin.v1.CreateUserGuideCategoryReply
	17, // 21: api.walletadmin.v1.UserGuideService.UpdateUserGuideCategory:output_type -> google.protobuf.Empty
	17, // 22: api.walletadmin.v1.UserGuideService.DeleteUserGuideCategory:output_type -> google.protobuf.Empty
	14, // 23: api.walletadmin.v1.UserGuideService.ListUserGuideCategories:output_type -> api.walletadmin.v1.ListUserGuideCategoriesReply
	16, // 24: api.walletadmin.v1.UserGuideService.GetUserGuideCategory:output_type -> api.walletadmin.v1.UserGuideCategoryInfo
	15, // [15:25] is the sub-list for method output_type
	5,  // [5:15] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_user_guide_proto_init() }
func file_api_walletadmin_v1_user_guide_proto_init() {
	if File_api_walletadmin_v1_user_guide_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_user_guide_proto_rawDesc), len(file_api_walletadmin_v1_user_guide_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_user_guide_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_user_guide_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_user_guide_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_user_guide_proto = out.File
	file_api_walletadmin_v1_user_guide_proto_goTypes = nil
	file_api_walletadmin_v1_user_guide_proto_depIdxs = nil
}
