// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/walletadmin/v1/user.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserSrvListUser = "/api.walletadmin.v1.UserSrv/ListUser"

type UserSrvHTTPServer interface {
	// ListUser 用户列表
	ListUser(context.Context, *ListUserReq) (*ListUserReply, error)
}

func RegisterUserSrvHTTPServer(s *http.Server, srv UserSrvHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/user/list_user", _UserSrv_ListUser0_HTTP_Handler(srv))
}

func _UserSrv_ListUser0_HTTP_Handler(srv UserSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserSrvListUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUser(ctx, req.(*ListUserReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserReply)
		return ctx.Result(200, reply)
	}
}

type UserSrvHTTPClient interface {
	ListUser(ctx context.Context, req *ListUserReq, opts ...http.CallOption) (rsp *ListUserReply, err error)
}

type UserSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewUserSrvHTTPClient(client *http.Client) UserSrvHTTPClient {
	return &UserSrvHTTPClientImpl{client}
}

func (c *UserSrvHTTPClientImpl) ListUser(ctx context.Context, in *ListUserReq, opts ...http.CallOption) (*ListUserReply, error) {
	var out ListUserReply
	pattern := "/admin/v1/user/list_user"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserSrvListUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
