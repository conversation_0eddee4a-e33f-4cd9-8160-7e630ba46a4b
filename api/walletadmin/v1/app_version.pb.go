// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/app_version.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReminderType int32

const (
	// 未指定
	ReminderType_REMINDER_TYPE_UNSPECIFIED ReminderType = 0
	// 每次打开
	ReminderType_REMINDER_TYPE_EVERY_OPEN ReminderType = 1
	// 每日
	ReminderType_REMINDER_TYPE_DAILY ReminderType = 2
	// 每周
	ReminderType_REMINDER_TYPE_WEEKLY ReminderType = 3
	// 从不
	ReminderType_REMINDER_TYPE_NEVER ReminderType = 4
)

// Enum value maps for ReminderType.
var (
	ReminderType_name = map[int32]string{
		0: "REMINDER_TYPE_UNSPECIFIED",
		1: "REMINDER_TYPE_EVERY_OPEN",
		2: "REMINDER_TYPE_DAILY",
		3: "REMINDER_TYPE_WEEKLY",
		4: "REMINDER_TYPE_NEVER",
	}
	ReminderType_value = map[string]int32{
		"REMINDER_TYPE_UNSPECIFIED": 0,
		"REMINDER_TYPE_EVERY_OPEN":  1,
		"REMINDER_TYPE_DAILY":       2,
		"REMINDER_TYPE_WEEKLY":      3,
		"REMINDER_TYPE_NEVER":       4,
	}
)

func (x ReminderType) Enum() *ReminderType {
	p := new(ReminderType)
	*p = x
	return p
}

func (x ReminderType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReminderType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_walletadmin_v1_app_version_proto_enumTypes[0].Descriptor()
}

func (ReminderType) Type() protoreflect.EnumType {
	return &file_api_walletadmin_v1_app_version_proto_enumTypes[0]
}

func (x ReminderType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReminderType.Descriptor instead.
func (ReminderType) EnumDescriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{0}
}

// 多语言描述
type AppVersionI18N struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 语言代码(zh,en,ja,es)
	Language string `protobuf:"bytes,1,opt,name=language,proto3" json:"language,omitempty"`
	// 版本描述
	Description   string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppVersionI18N) Reset() {
	*x = AppVersionI18N{}
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppVersionI18N) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppVersionI18N) ProtoMessage() {}

func (x *AppVersionI18N) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppVersionI18N.ProtoReflect.Descriptor instead.
func (*AppVersionI18N) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{0}
}

func (x *AppVersionI18N) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *AppVersionI18N) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 创建版本请求
type CreateAppVersionReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 版本号
	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	// APP类型
	AppType string `protobuf:"bytes,2,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`
	// 下载地址
	DownloadUrl string `protobuf:"bytes,3,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	// 强制更新
	ForceUpdate bool `protobuf:"varint,4,opt,name=force_update,json=forceUpdate,proto3" json:"force_update,omitempty"`
	// 多语言描述
	I18NDescriptions []*AppVersionI18N `protobuf:"bytes,5,rep,name=i18n_descriptions,json=i18nDescriptions,proto3" json:"i18n_descriptions,omitempty"`
	// 提醒类型 (0:每次打开 1:每日 2:每周 3:从不)
	ReminderType ReminderType `protobuf:"varint,6,opt,name=reminder_type,json=reminderType,proto3,enum=api.walletadmin.v1.ReminderType" json:"reminder_type,omitempty"`
	// 官方下载地址
	OfficialDownloadUrl string `protobuf:"bytes,7,opt,name=official_download_url,json=officialDownloadUrl,proto3" json:"official_download_url,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *CreateAppVersionReq) Reset() {
	*x = CreateAppVersionReq{}
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAppVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppVersionReq) ProtoMessage() {}

func (x *CreateAppVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppVersionReq.ProtoReflect.Descriptor instead.
func (*CreateAppVersionReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAppVersionReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *CreateAppVersionReq) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

func (x *CreateAppVersionReq) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *CreateAppVersionReq) GetForceUpdate() bool {
	if x != nil {
		return x.ForceUpdate
	}
	return false
}

func (x *CreateAppVersionReq) GetI18NDescriptions() []*AppVersionI18N {
	if x != nil {
		return x.I18NDescriptions
	}
	return nil
}

func (x *CreateAppVersionReq) GetReminderType() ReminderType {
	if x != nil {
		return x.ReminderType
	}
	return ReminderType_REMINDER_TYPE_UNSPECIFIED
}

func (x *CreateAppVersionReq) GetOfficialDownloadUrl() string {
	if x != nil {
		return x.OfficialDownloadUrl
	}
	return ""
}

// 创建版本响应
type CreateAppVersionReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAppVersionReply) Reset() {
	*x = CreateAppVersionReply{}
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAppVersionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAppVersionReply) ProtoMessage() {}

func (x *CreateAppVersionReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAppVersionReply.ProtoReflect.Descriptor instead.
func (*CreateAppVersionReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{2}
}

// 更新版本请求
type UpdateAppVersionReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 版本ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 多语言描述
	I18NDescriptions []*AppVersionI18N `protobuf:"bytes,2,rep,name=i18n_descriptions,json=i18nDescriptions,proto3" json:"i18n_descriptions,omitempty"`
	// 强制更新
	ForceUpdate   bool `protobuf:"varint,3,opt,name=force_update,json=forceUpdate,proto3" json:"force_update,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAppVersionReq) Reset() {
	*x = UpdateAppVersionReq{}
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAppVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppVersionReq) ProtoMessage() {}

func (x *UpdateAppVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppVersionReq.ProtoReflect.Descriptor instead.
func (*UpdateAppVersionReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateAppVersionReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAppVersionReq) GetI18NDescriptions() []*AppVersionI18N {
	if x != nil {
		return x.I18NDescriptions
	}
	return nil
}

func (x *UpdateAppVersionReq) GetForceUpdate() bool {
	if x != nil {
		return x.ForceUpdate
	}
	return false
}

// 删除版本请求
type DeleteAppVersionReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 版本ID
	Id            uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAppVersionReq) Reset() {
	*x = DeleteAppVersionReq{}
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAppVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAppVersionReq) ProtoMessage() {}

func (x *DeleteAppVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAppVersionReq.ProtoReflect.Descriptor instead.
func (*DeleteAppVersionReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteAppVersionReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 版本列表请求
type ListAppVersionsReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// APP类型筛选
	AppType string `protobuf:"bytes,1,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`
	// 页码
	Page int64 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	// 每页数量
	PageSize      int64 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAppVersionsReq) Reset() {
	*x = ListAppVersionsReq{}
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAppVersionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppVersionsReq) ProtoMessage() {}

func (x *ListAppVersionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppVersionsReq.ProtoReflect.Descriptor instead.
func (*ListAppVersionsReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{5}
}

func (x *ListAppVersionsReq) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

func (x *ListAppVersionsReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListAppVersionsReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 版本列表响应
type ListAppVersionsReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 版本列表
	List []*AppVersionInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAppVersionsReply) Reset() {
	*x = ListAppVersionsReply{}
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAppVersionsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppVersionsReply) ProtoMessage() {}

func (x *ListAppVersionsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppVersionsReply.ProtoReflect.Descriptor instead.
func (*ListAppVersionsReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{6}
}

func (x *ListAppVersionsReply) GetList() []*AppVersionInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListAppVersionsReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

// 获取版本详情请求
type GetAppVersionReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 版本ID
	Id            uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAppVersionReq) Reset() {
	*x = GetAppVersionReq{}
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAppVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAppVersionReq) ProtoMessage() {}

func (x *GetAppVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAppVersionReq.ProtoReflect.Descriptor instead.
func (*GetAppVersionReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{7}
}

func (x *GetAppVersionReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 获取已发布版本列表请求
type ListPublishedVersionsReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// APP类型筛选
	AppType       string `protobuf:"bytes,1,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPublishedVersionsReq) Reset() {
	*x = ListPublishedVersionsReq{}
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPublishedVersionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPublishedVersionsReq) ProtoMessage() {}

func (x *ListPublishedVersionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPublishedVersionsReq.ProtoReflect.Descriptor instead.
func (*ListPublishedVersionsReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{8}
}

func (x *ListPublishedVersionsReq) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

// 获取已发布版本列表响应
type ListPublishedVersionsReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 版本号列表
	List          []string `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPublishedVersionsReply) Reset() {
	*x = ListPublishedVersionsReply{}
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPublishedVersionsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPublishedVersionsReply) ProtoMessage() {}

func (x *ListPublishedVersionsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPublishedVersionsReply.ProtoReflect.Descriptor instead.
func (*ListPublishedVersionsReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{9}
}

func (x *ListPublishedVersionsReply) GetList() []string {
	if x != nil {
		return x.List
	}
	return nil
}

// 版本信息
type AppVersionInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 版本ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 版本号
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// APP类型
	AppType string `protobuf:"bytes,3,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`
	// 下载地址
	DownloadUrl string `protobuf:"bytes,4,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	// 强制更新
	ForceUpdate bool `protobuf:"varint,5,opt,name=force_update,json=forceUpdate,proto3" json:"force_update,omitempty"`
	// 多语言描述
	I18NDescriptions []*AppVersionI18N `protobuf:"bytes,6,rep,name=i18n_descriptions,json=i18nDescriptions,proto3" json:"i18n_descriptions,omitempty"`
	// 创建时间
	CreatedAt int64 `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt int64 `protobuf:"varint,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 提醒类型
	ReminderType ReminderType `protobuf:"varint,9,opt,name=reminder_type,json=reminderType,proto3,enum=api.walletadmin.v1.ReminderType" json:"reminder_type,omitempty"`
	// 官方下载地址
	OfficialDownloadUrl string `protobuf:"bytes,10,opt,name=official_download_url,json=officialDownloadUrl,proto3" json:"official_download_url,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *AppVersionInfo) Reset() {
	*x = AppVersionInfo{}
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppVersionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppVersionInfo) ProtoMessage() {}

func (x *AppVersionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_app_version_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppVersionInfo.ProtoReflect.Descriptor instead.
func (*AppVersionInfo) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_app_version_proto_rawDescGZIP(), []int{10}
}

func (x *AppVersionInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AppVersionInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AppVersionInfo) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

func (x *AppVersionInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *AppVersionInfo) GetForceUpdate() bool {
	if x != nil {
		return x.ForceUpdate
	}
	return false
}

func (x *AppVersionInfo) GetI18NDescriptions() []*AppVersionI18N {
	if x != nil {
		return x.I18NDescriptions
	}
	return nil
}

func (x *AppVersionInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AppVersionInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *AppVersionInfo) GetReminderType() ReminderType {
	if x != nil {
		return x.ReminderType
	}
	return ReminderType_REMINDER_TYPE_UNSPECIFIED
}

func (x *AppVersionInfo) GetOfficialDownloadUrl() string {
	if x != nil {
		return x.OfficialDownloadUrl
	}
	return ""
}

var File_api_walletadmin_v1_app_version_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_app_version_proto_rawDesc = "" +
	"\n" +
	"$api/walletadmin/v1/app_version.proto\x12\x12api.walletadmin.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\"N\n" +
	"\x0eAppVersionI18N\x12\x1a\n" +
	"\blanguage\x18\x01 \x01(\tR\blanguage\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\"\xee\x02\n" +
	"\x13CreateAppVersionReq\x12!\n" +
	"\aversion\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\aversion\x12\"\n" +
	"\bapp_type\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\aappType\x12!\n" +
	"\fdownload_url\x18\x03 \x01(\tR\vdownloadUrl\x12!\n" +
	"\fforce_update\x18\x04 \x01(\bR\vforceUpdate\x12O\n" +
	"\x11i18n_descriptions\x18\x05 \x03(\v2\".api.walletadmin.v1.AppVersionI18NR\x10i18nDescriptions\x12E\n" +
	"\rreminder_type\x18\x06 \x01(\x0e2 .api.walletadmin.v1.ReminderTypeR\freminderType\x122\n" +
	"\x15official_download_url\x18\a \x01(\tR\x13officialDownloadUrl\"\x17\n" +
	"\x15CreateAppVersionReply\"\xa2\x01\n" +
	"\x13UpdateAppVersionReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12O\n" +
	"\x11i18n_descriptions\x18\x02 \x03(\v2\".api.walletadmin.v1.AppVersionI18NR\x10i18nDescriptions\x12!\n" +
	"\fforce_update\x18\x03 \x01(\bR\vforceUpdate\".\n" +
	"\x13DeleteAppVersionReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"t\n" +
	"\x12ListAppVersionsReq\x12\x19\n" +
	"\bapp_type\x18\x01 \x01(\tR\aappType\x12\x1b\n" +
	"\x04page\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02(\x01R\x04page\x12&\n" +
	"\tpage_size\x18\x03 \x01(\x03B\t\xbaH\x06\"\x04\x18d(\x01R\bpageSize\"o\n" +
	"\x14ListAppVersionsReply\x126\n" +
	"\x04list\x18\x01 \x03(\v2\".api.walletadmin.v1.AppVersionInfoR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount\"+\n" +
	"\x10GetAppVersionReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"5\n" +
	"\x18ListPublishedVersionsReq\x12\x19\n" +
	"\bapp_type\x18\x01 \x01(\tR\aappType\"0\n" +
	"\x1aListPublishedVersionsReply\x12\x12\n" +
	"\x04list\x18\x01 \x03(\tR\x04list\"\xa5\x03\n" +
	"\x0eAppVersionInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x19\n" +
	"\bapp_type\x18\x03 \x01(\tR\aappType\x12!\n" +
	"\fdownload_url\x18\x04 \x01(\tR\vdownloadUrl\x12!\n" +
	"\fforce_update\x18\x05 \x01(\bR\vforceUpdate\x12O\n" +
	"\x11i18n_descriptions\x18\x06 \x03(\v2\".api.walletadmin.v1.AppVersionI18NR\x10i18nDescriptions\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\b \x01(\x03R\tupdatedAt\x12E\n" +
	"\rreminder_type\x18\t \x01(\x0e2 .api.walletadmin.v1.ReminderTypeR\freminderType\x122\n" +
	"\x15official_download_url\x18\n" +
	" \x01(\tR\x13officialDownloadUrl*\x97\x01\n" +
	"\fReminderType\x12\x1d\n" +
	"\x19REMINDER_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18REMINDER_TYPE_EVERY_OPEN\x10\x01\x12\x17\n" +
	"\x13REMINDER_TYPE_DAILY\x10\x02\x12\x18\n" +
	"\x14REMINDER_TYPE_WEEKLY\x10\x03\x12\x17\n" +
	"\x13REMINDER_TYPE_NEVER\x10\x042\xc1\x06\n" +
	"\x11AppVersionService\x12\x89\x01\n" +
	"\x10CreateAppVersion\x12'.api.walletadmin.v1.CreateAppVersionReq\x1a).api.walletadmin.v1.CreateAppVersionReply\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/admin/v1/app-versions\x12{\n" +
	"\x10UpdateAppVersion\x12'.api.walletadmin.v1.UpdateAppVersionReq\x1a\x16.google.protobuf.Empty\"&\x82\xd3\xe4\x93\x02 :\x01*\x1a\x1b/admin/v1/app-versions/{id}\x12x\n" +
	"\x10DeleteAppVersion\x12'.api.walletadmin.v1.DeleteAppVersionReq\x1a\x16.google.protobuf.Empty\"#\x82\xd3\xe4\x93\x02\x1d*\x1b/admin/v1/app-versions/{id}\x12\x83\x01\n" +
	"\x0fListAppVersions\x12&.api.walletadmin.v1.ListAppVersionsReq\x1a(.api.walletadmin.v1.ListAppVersionsReply\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/admin/v1/app-versions\x12~\n" +
	"\rGetAppVersion\x12$.api.walletadmin.v1.GetAppVersionReq\x1a\".api.walletadmin.v1.AppVersionInfo\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/admin/v1/app-versions/{id}\x12\xa2\x01\n" +
	"\x15ListPublishedVersions\x12,.api.walletadmin.v1.ListPublishedVersionsReq\x1a..api.walletadmin.v1.ListPublishedVersionsReply\"+\x82\xd3\xe4\x93\x02%:\x01*\" /admin/v1/app-versions/publishedB\xb5\x01\n" +
	"\x16com.api.walletadmin.v1B\x0fAppVersionProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_app_version_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_app_version_proto_rawDescData []byte
)

func file_api_walletadmin_v1_app_version_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_app_version_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_app_version_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_app_version_proto_rawDesc), len(file_api_walletadmin_v1_app_version_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_app_version_proto_rawDescData
}

var file_api_walletadmin_v1_app_version_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_walletadmin_v1_app_version_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_walletadmin_v1_app_version_proto_goTypes = []any{
	(ReminderType)(0),                  // 0: api.walletadmin.v1.ReminderType
	(*AppVersionI18N)(nil),             // 1: api.walletadmin.v1.AppVersionI18N
	(*CreateAppVersionReq)(nil),        // 2: api.walletadmin.v1.CreateAppVersionReq
	(*CreateAppVersionReply)(nil),      // 3: api.walletadmin.v1.CreateAppVersionReply
	(*UpdateAppVersionReq)(nil),        // 4: api.walletadmin.v1.UpdateAppVersionReq
	(*DeleteAppVersionReq)(nil),        // 5: api.walletadmin.v1.DeleteAppVersionReq
	(*ListAppVersionsReq)(nil),         // 6: api.walletadmin.v1.ListAppVersionsReq
	(*ListAppVersionsReply)(nil),       // 7: api.walletadmin.v1.ListAppVersionsReply
	(*GetAppVersionReq)(nil),           // 8: api.walletadmin.v1.GetAppVersionReq
	(*ListPublishedVersionsReq)(nil),   // 9: api.walletadmin.v1.ListPublishedVersionsReq
	(*ListPublishedVersionsReply)(nil), // 10: api.walletadmin.v1.ListPublishedVersionsReply
	(*AppVersionInfo)(nil),             // 11: api.walletadmin.v1.AppVersionInfo
	(*emptypb.Empty)(nil),              // 12: google.protobuf.Empty
}
var file_api_walletadmin_v1_app_version_proto_depIdxs = []int32{
	1,  // 0: api.walletadmin.v1.CreateAppVersionReq.i18n_descriptions:type_name -> api.walletadmin.v1.AppVersionI18N
	0,  // 1: api.walletadmin.v1.CreateAppVersionReq.reminder_type:type_name -> api.walletadmin.v1.ReminderType
	1,  // 2: api.walletadmin.v1.UpdateAppVersionReq.i18n_descriptions:type_name -> api.walletadmin.v1.AppVersionI18N
	11, // 3: api.walletadmin.v1.ListAppVersionsReply.list:type_name -> api.walletadmin.v1.AppVersionInfo
	1,  // 4: api.walletadmin.v1.AppVersionInfo.i18n_descriptions:type_name -> api.walletadmin.v1.AppVersionI18N
	0,  // 5: api.walletadmin.v1.AppVersionInfo.reminder_type:type_name -> api.walletadmin.v1.ReminderType
	2,  // 6: api.walletadmin.v1.AppVersionService.CreateAppVersion:input_type -> api.walletadmin.v1.CreateAppVersionReq
	4,  // 7: api.walletadmin.v1.AppVersionService.UpdateAppVersion:input_type -> api.walletadmin.v1.UpdateAppVersionReq
	5,  // 8: api.walletadmin.v1.AppVersionService.DeleteAppVersion:input_type -> api.walletadmin.v1.DeleteAppVersionReq
	6,  // 9: api.walletadmin.v1.AppVersionService.ListAppVersions:input_type -> api.walletadmin.v1.ListAppVersionsReq
	8,  // 10: api.walletadmin.v1.AppVersionService.GetAppVersion:input_type -> api.walletadmin.v1.GetAppVersionReq
	9,  // 11: api.walletadmin.v1.AppVersionService.ListPublishedVersions:input_type -> api.walletadmin.v1.ListPublishedVersionsReq
	3,  // 12: api.walletadmin.v1.AppVersionService.CreateAppVersion:output_type -> api.walletadmin.v1.CreateAppVersionReply
	12, // 13: api.walletadmin.v1.AppVersionService.UpdateAppVersion:output_type -> google.protobuf.Empty
	12, // 14: api.walletadmin.v1.AppVersionService.DeleteAppVersion:output_type -> google.protobuf.Empty
	7,  // 15: api.walletadmin.v1.AppVersionService.ListAppVersions:output_type -> api.walletadmin.v1.ListAppVersionsReply
	11, // 16: api.walletadmin.v1.AppVersionService.GetAppVersion:output_type -> api.walletadmin.v1.AppVersionInfo
	10, // 17: api.walletadmin.v1.AppVersionService.ListPublishedVersions:output_type -> api.walletadmin.v1.ListPublishedVersionsReply
	12, // [12:18] is the sub-list for method output_type
	6,  // [6:12] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_app_version_proto_init() }
func file_api_walletadmin_v1_app_version_proto_init() {
	if File_api_walletadmin_v1_app_version_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_app_version_proto_rawDesc), len(file_api_walletadmin_v1_app_version_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_app_version_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_app_version_proto_depIdxs,
		EnumInfos:         file_api_walletadmin_v1_app_version_proto_enumTypes,
		MessageInfos:      file_api_walletadmin_v1_app_version_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_app_version_proto = out.File
	file_api_walletadmin_v1_app_version_proto_goTypes = nil
	file_api_walletadmin_v1_app_version_proto_depIdxs = nil
}
