// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/walletadmin/v1/chain.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationChainSrvListChain = "/api.walletadmin.v1.ChainSrv/ListChain"
const OperationChainSrvUpdateChain = "/api.walletadmin.v1.ChainSrv/UpdateChain"
const OperationChainSrvUpdateChainSort = "/api.walletadmin.v1.ChainSrv/UpdateChainSort"

type ChainSrvHTTPServer interface {
	// ListChain 公链列表
	ListChain(context.Context, *ListChainReq) (*ListChainReply, error)
	// UpdateChain 更新公链记录
	UpdateChain(context.Context, *UpdateChainReq) (*emptypb.Empty, error)
	// UpdateChainSort 更新公链排序
	UpdateChainSort(context.Context, *UpdateChainSortReq) (*emptypb.Empty, error)
}

func RegisterChainSrvHTTPServer(s *http.Server, srv ChainSrvHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/chain/list_chain", _ChainSrv_ListChain0_HTTP_Handler(srv))
	r.PUT("/admin/v1/chain/update_chain", _ChainSrv_UpdateChain0_HTTP_Handler(srv))
	r.PATCH("/admin/v1/chain/update_chain_sort", _ChainSrv_UpdateChainSort0_HTTP_Handler(srv))
}

func _ChainSrv_ListChain0_HTTP_Handler(srv ChainSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListChainReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationChainSrvListChain)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListChain(ctx, req.(*ListChainReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListChainReply)
		return ctx.Result(200, reply)
	}
}

func _ChainSrv_UpdateChain0_HTTP_Handler(srv ChainSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateChainReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationChainSrvUpdateChain)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateChain(ctx, req.(*UpdateChainReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _ChainSrv_UpdateChainSort0_HTTP_Handler(srv ChainSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateChainSortReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationChainSrvUpdateChainSort)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateChainSort(ctx, req.(*UpdateChainSortReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type ChainSrvHTTPClient interface {
	ListChain(ctx context.Context, req *ListChainReq, opts ...http.CallOption) (rsp *ListChainReply, err error)
	UpdateChain(ctx context.Context, req *UpdateChainReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateChainSort(ctx context.Context, req *UpdateChainSortReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type ChainSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewChainSrvHTTPClient(client *http.Client) ChainSrvHTTPClient {
	return &ChainSrvHTTPClientImpl{client}
}

func (c *ChainSrvHTTPClientImpl) ListChain(ctx context.Context, in *ListChainReq, opts ...http.CallOption) (*ListChainReply, error) {
	var out ListChainReply
	pattern := "/admin/v1/chain/list_chain"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationChainSrvListChain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ChainSrvHTTPClientImpl) UpdateChain(ctx context.Context, in *UpdateChainReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/chain/update_chain"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationChainSrvUpdateChain))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ChainSrvHTTPClientImpl) UpdateChainSort(ctx context.Context, in *UpdateChainSortReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/chain/update_chain_sort"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationChainSrvUpdateChainSort))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
