syntax = "proto3";

package api.walletadmin.v1;

option go_package = "byd_wallet/api/walletadmin/v1;v1";

import "google/api/annotations.proto";
import "buf/validate/validate.proto";

service FileSrv {
  // GeneratePresignedRequest 生成预签名的请求
  // 相关文档: https://docs.aws.amazon.com/AmazonS3/latest/API/s3_example_s3_Scenario_PresignedUrl_section.html
  rpc GeneratePresignedRequest (GeneratePresignedRequestReq) returns (GeneratePresignedRequestReply) {
    option (google.api.http) = {
      post: "/admin/v1/presign"
      body: "*"
    };
  }
}

message GeneratePresignedRequestReq {
    // 业务类型(dapp_logo,token_logo,user_guide)
  string biz_type = 1 [(buf.validate.field).string.min_len = 1];
  // 文件后缀名：jpg,png...
  string file_suffix = 2;
}

message GeneratePresignedRequestReply {
  string url = 1;
  // 请求方法: GET/POST/PUT...
  string method = 2;
  // 请求头
  repeated Header headers = 3;
}

message Header {
  string key = 1;
  repeated string value = 2;
}