syntax = "proto3";

package api.walletadmin.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "byd_wallet/api/walletadmin/v1;v1";

service AppVersionService {
  // 创建版本
  rpc CreateAppVersion(CreateAppVersionReq) returns (CreateAppVersionReply) {
    option (google.api.http) = {
      post: "/admin/v1/app-versions"
      body: "*"
    };
  }

  // 更新版本
  rpc UpdateAppVersion(UpdateAppVersionReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/app-versions/{id}"
      body: "*"
    };
  }

  // 删除版本
  rpc DeleteAppVersion(DeleteAppVersionReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/admin/v1/app-versions/{id}"};
  }

  // 版本列表
  rpc ListAppVersions(ListAppVersionsReq) returns (ListAppVersionsReply) {
    option (google.api.http) = {get: "/admin/v1/app-versions"};
  }

  // 获取版本详情
  rpc GetAppVersion(GetAppVersionReq) returns (AppVersionInfo) {
    option (google.api.http) = {get: "/admin/v1/app-versions/{id}"};
  }

  // 获取已发布版本列表
  rpc ListPublishedVersions(ListPublishedVersionsReq) returns (ListPublishedVersionsReply) {
    option (google.api.http) = {
      post: "/admin/v1/app-versions/published"
      body: "*"
    };
  }
}

enum ReminderType {
  // 未指定
  REMINDER_TYPE_UNSPECIFIED = 0;
  // 每次打开
  REMINDER_TYPE_EVERY_OPEN = 1;
  // 每日
  REMINDER_TYPE_DAILY = 2;
  // 每周
  REMINDER_TYPE_WEEKLY = 3;
  // 从不
  REMINDER_TYPE_NEVER = 4;
}

// 多语言描述
message AppVersionI18N {
  // 语言代码(zh,en,ja,es)
  string language = 1;
  // 版本描述
  string description = 2;
}

// 创建版本请求
message CreateAppVersionReq {
  // 版本号
  string version = 1 [(buf.validate.field).string.min_len = 1];
  // APP类型
  string app_type = 2 [(buf.validate.field).string.min_len = 1];
  // 下载地址
  string download_url = 3;
  // 强制更新
  bool force_update = 4;
  // 多语言描述
  repeated AppVersionI18N i18n_descriptions = 5;
  // 提醒类型 (0:每次打开 1:每日 2:每周 3:从不)
  ReminderType reminder_type = 6;
  // 官方下载地址
  string official_download_url = 7;
}

// 创建版本响应
message CreateAppVersionReply {}

// 更新版本请求
message UpdateAppVersionReq {
  // 版本ID
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // 多语言描述
  repeated AppVersionI18N i18n_descriptions = 2;
  // 强制更新
  bool force_update = 3;
}

// 删除版本请求
message DeleteAppVersionReq {
  // 版本ID
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

// 版本列表请求
message ListAppVersionsReq {
  // APP类型筛选
  string app_type = 1;
  // 页码
  int64 page = 2 [(buf.validate.field).int64.gte = 1];
  // 每页数量
  int64 page_size = 3 [
    (buf.validate.field).int64.gte = 1,
    (buf.validate.field).int64.lte = 100
  ];
}

// 版本列表响应
message ListAppVersionsReply {
  // 版本列表
  repeated AppVersionInfo list = 1;
  // 总数
  int64 total_count = 2;
}

// 获取版本详情请求
message GetAppVersionReq {
  // 版本ID
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

// 获取已发布版本列表请求
message ListPublishedVersionsReq {
  // APP类型筛选
  string app_type = 1;
}

// 获取已发布版本列表响应
message ListPublishedVersionsReply {
  // 版本号列表
  repeated string list = 1;
}

// 版本信息
message AppVersionInfo {
  // 版本ID
  uint64 id = 1;
  // 版本号
  string version = 2;
  // APP类型
  string app_type = 3;
  // 下载地址
  string download_url = 4;
  // 强制更新
  bool force_update = 5;
  // 多语言描述
  repeated AppVersionI18N i18n_descriptions = 6;
  // 创建时间
  int64 created_at = 7;
  // 更新时间
  int64 updated_at = 8;
  // 提醒类型
  ReminderType reminder_type = 9;
  // 官方下载地址
  string official_download_url = 10;
}
