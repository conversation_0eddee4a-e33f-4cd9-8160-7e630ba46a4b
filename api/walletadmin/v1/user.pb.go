// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/user.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListUserReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 页码
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 页容量
	PageSize int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 用户ID
	Id *uint64 `protobuf:"varint,3,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// BOSS ID
	BossId        *string `protobuf:"bytes,4,opt,name=boss_id,json=bossId,proto3,oneof" json:"boss_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserReq) Reset() {
	*x = ListUserReq{}
	mi := &file_api_walletadmin_v1_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserReq) ProtoMessage() {}

func (x *ListUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserReq.ProtoReflect.Descriptor instead.
func (*ListUserReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_proto_rawDescGZIP(), []int{0}
}

func (x *ListUserReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListUserReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListUserReq) GetId() uint64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *ListUserReq) GetBossId() string {
	if x != nil && x.BossId != nil {
		return *x.BossId
	}
	return ""
}

type User struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// BOSS ID
	BossId string `protobuf:"bytes,2,opt,name=boss_id,json=bossId,proto3" json:"boss_id,omitempty"`
	// 创建时间(时间戳,单位秒)
	CreatedAt int64 `protobuf:"varint,3,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 是否可用
	Enable        bool `protobuf:"varint,4,opt,name=enable,proto3" json:"enable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_api_walletadmin_v1_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_proto_rawDescGZIP(), []int{1}
}

func (x *User) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetBossId() string {
	if x != nil {
		return x.BossId
	}
	return ""
}

func (x *User) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *User) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type ListUserReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*User                `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总记录数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserReply) Reset() {
	*x = ListUserReply{}
	mi := &file_api_walletadmin_v1_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserReply) ProtoMessage() {}

func (x *ListUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserReply.ProtoReflect.Descriptor instead.
func (*ListUserReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_user_proto_rawDescGZIP(), []int{2}
}

func (x *ListUserReply) GetList() []*User {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListUserReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

var File_api_walletadmin_v1_user_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_user_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/walletadmin/v1/user.proto\x12\x12api.walletadmin.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\"\x98\x01\n" +
	"\vListUserReq\x12\x1b\n" +
	"\x04page\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12&\n" +
	"\tpage_size\x18\x02 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\bpageSize\x12\x13\n" +
	"\x02id\x18\x03 \x01(\x04H\x00R\x02id\x88\x01\x01\x12\x1c\n" +
	"\aboss_id\x18\x04 \x01(\tH\x01R\x06bossId\x88\x01\x01B\x05\n" +
	"\x03_idB\n" +
	"\n" +
	"\b_boss_id\"f\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x17\n" +
	"\aboss_id\x18\x02 \x01(\tR\x06bossId\x12\x1d\n" +
	"\n" +
	"created_at\x18\x03 \x01(\x03R\tcreatedAt\x12\x16\n" +
	"\x06enable\x18\x04 \x01(\bR\x06enable\"^\n" +
	"\rListUserReply\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.api.walletadmin.v1.UserR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount2{\n" +
	"\aUserSrv\x12p\n" +
	"\bListUser\x12\x1f.api.walletadmin.v1.ListUserReq\x1a!.api.walletadmin.v1.ListUserReply\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/admin/v1/user/list_userB\xaf\x01\n" +
	"\x16com.api.walletadmin.v1B\tUserProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_user_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_user_proto_rawDescData []byte
)

func file_api_walletadmin_v1_user_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_user_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_user_proto_rawDesc), len(file_api_walletadmin_v1_user_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_user_proto_rawDescData
}

var file_api_walletadmin_v1_user_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_walletadmin_v1_user_proto_goTypes = []any{
	(*ListUserReq)(nil),   // 0: api.walletadmin.v1.ListUserReq
	(*User)(nil),          // 1: api.walletadmin.v1.User
	(*ListUserReply)(nil), // 2: api.walletadmin.v1.ListUserReply
}
var file_api_walletadmin_v1_user_proto_depIdxs = []int32{
	1, // 0: api.walletadmin.v1.ListUserReply.list:type_name -> api.walletadmin.v1.User
	0, // 1: api.walletadmin.v1.UserSrv.ListUser:input_type -> api.walletadmin.v1.ListUserReq
	2, // 2: api.walletadmin.v1.UserSrv.ListUser:output_type -> api.walletadmin.v1.ListUserReply
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_user_proto_init() }
func file_api_walletadmin_v1_user_proto_init() {
	if File_api_walletadmin_v1_user_proto != nil {
		return
	}
	file_api_walletadmin_v1_user_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_user_proto_rawDesc), len(file_api_walletadmin_v1_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_user_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_user_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_user_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_user_proto = out.File
	file_api_walletadmin_v1_user_proto_goTypes = nil
	file_api_walletadmin_v1_user_proto_depIdxs = nil
}
