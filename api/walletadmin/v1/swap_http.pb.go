// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/walletadmin/v1/swap.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationSwapServiceCreateHotToken = "/api.walletadmin.v1.SwapService/CreateHotToken"
const OperationSwapServiceDeleteHotToken = "/api.walletadmin.v1.SwapService/DeleteHotToken"
const OperationSwapServiceGetSwapRecord = "/api.walletadmin.v1.SwapService/GetSwapRecord"
const OperationSwapServiceListHotToken = "/api.walletadmin.v1.SwapService/ListHotToken"
const OperationSwapServiceListSwapChannel = "/api.walletadmin.v1.SwapService/ListSwapChannel"
const OperationSwapServiceListSwapRecord = "/api.walletadmin.v1.SwapService/ListSwapRecord"
const OperationSwapServiceListToken = "/api.walletadmin.v1.SwapService/ListToken"
const OperationSwapServiceSortHotToken = "/api.walletadmin.v1.SwapService/SortHotToken"
const OperationSwapServiceUpdateSwapChannel = "/api.walletadmin.v1.SwapService/UpdateSwapChannel"
const OperationSwapServiceUpdateToken = "/api.walletadmin.v1.SwapService/UpdateToken"

type SwapServiceHTTPServer interface {
	// CreateHotToken 增加热门代币
	CreateHotToken(context.Context, *CreateHotTokenRequest) (*CreateHotTokenReply, error)
	// DeleteHotToken 删除热门代币
	DeleteHotToken(context.Context, *DeleteHotTokenRequest) (*DeleteHotTokenReply, error)
	// GetSwapRecord 查询兑换记录
	GetSwapRecord(context.Context, *GetSwapRecordRequest) (*SwapRecord, error)
	// ListHotToken 查询热门代币
	ListHotToken(context.Context, *ListHotTokenRequest) (*ListHotTokenReply, error)
	// ListSwapChannel 查询兑换渠道
	ListSwapChannel(context.Context, *ListSwapChannelRequest) (*ListSwapChannelReply, error)
	// ListSwapRecord 查询兑换记录列表
	ListSwapRecord(context.Context, *ListSwapRecordRequest) (*ListSwapRecordReply, error)
	// ListToken 查询兑换的币种列表
	ListToken(context.Context, *ListTokenRequest) (*ListTokenReply, error)
	// SortHotToken 热门代币排序
	SortHotToken(context.Context, *CommonSortReq) (*CommonSortReply, error)
	// UpdateSwapChannel 更新兑换渠道
	UpdateSwapChannel(context.Context, *UpdateSwapChannelRequest) (*UpdateSwapChannelReply, error)
	// UpdateToken 更新兑换币种
	UpdateToken(context.Context, *UpdateTokenRequest) (*UpdateTokenReply, error)
}

func RegisterSwapServiceHTTPServer(s *http.Server, srv SwapServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/swap/channel", _SwapService_ListSwapChannel0_HTTP_Handler(srv))
	r.PUT("/admin/v1/swap/channel/{id}", _SwapService_UpdateSwapChannel0_HTTP_Handler(srv))
	r.GET("/admin/v1/swap/token", _SwapService_ListToken0_HTTP_Handler(srv))
	r.PUT("/admin/v1/swap/token/{id}", _SwapService_UpdateToken0_HTTP_Handler(srv))
	r.GET("/admin/v1/swap/token/hot", _SwapService_ListHotToken0_HTTP_Handler(srv))
	r.POST("/admin/v1/swap/token/hot", _SwapService_CreateHotToken0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/swap/token/hot/{id}", _SwapService_DeleteHotToken0_HTTP_Handler(srv))
	r.PUT("/admin/v1/swap/token/hot/sort", _SwapService_SortHotToken0_HTTP_Handler(srv))
	r.GET("/admin/v1/swap/record/{id}", _SwapService_GetSwapRecord0_HTTP_Handler(srv))
	r.GET("/admin/v1/swap/record", _SwapService_ListSwapRecord0_HTTP_Handler(srv))
}

func _SwapService_ListSwapChannel0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListSwapChannelRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceListSwapChannel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSwapChannel(ctx, req.(*ListSwapChannelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListSwapChannelReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_UpdateSwapChannel0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateSwapChannelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceUpdateSwapChannel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateSwapChannel(ctx, req.(*UpdateSwapChannelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateSwapChannelReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_ListToken0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListTokenRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceListToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListToken(ctx, req.(*ListTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListTokenReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_UpdateToken0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateTokenRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceUpdateToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateToken(ctx, req.(*UpdateTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateTokenReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_ListHotToken0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListHotTokenRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceListHotToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListHotToken(ctx, req.(*ListHotTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListHotTokenReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_CreateHotToken0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateHotTokenRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceCreateHotToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateHotToken(ctx, req.(*CreateHotTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateHotTokenReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_DeleteHotToken0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteHotTokenRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceDeleteHotToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteHotToken(ctx, req.(*DeleteHotTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteHotTokenReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_SortHotToken0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CommonSortReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceSortHotToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SortHotToken(ctx, req.(*CommonSortReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CommonSortReply)
		return ctx.Result(200, reply)
	}
}

func _SwapService_GetSwapRecord0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSwapRecordRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceGetSwapRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSwapRecord(ctx, req.(*GetSwapRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SwapRecord)
		return ctx.Result(200, reply)
	}
}

func _SwapService_ListSwapRecord0_HTTP_Handler(srv SwapServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListSwapRecordRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSwapServiceListSwapRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSwapRecord(ctx, req.(*ListSwapRecordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListSwapRecordReply)
		return ctx.Result(200, reply)
	}
}

type SwapServiceHTTPClient interface {
	CreateHotToken(ctx context.Context, req *CreateHotTokenRequest, opts ...http.CallOption) (rsp *CreateHotTokenReply, err error)
	DeleteHotToken(ctx context.Context, req *DeleteHotTokenRequest, opts ...http.CallOption) (rsp *DeleteHotTokenReply, err error)
	GetSwapRecord(ctx context.Context, req *GetSwapRecordRequest, opts ...http.CallOption) (rsp *SwapRecord, err error)
	ListHotToken(ctx context.Context, req *ListHotTokenRequest, opts ...http.CallOption) (rsp *ListHotTokenReply, err error)
	ListSwapChannel(ctx context.Context, req *ListSwapChannelRequest, opts ...http.CallOption) (rsp *ListSwapChannelReply, err error)
	ListSwapRecord(ctx context.Context, req *ListSwapRecordRequest, opts ...http.CallOption) (rsp *ListSwapRecordReply, err error)
	ListToken(ctx context.Context, req *ListTokenRequest, opts ...http.CallOption) (rsp *ListTokenReply, err error)
	SortHotToken(ctx context.Context, req *CommonSortReq, opts ...http.CallOption) (rsp *CommonSortReply, err error)
	UpdateSwapChannel(ctx context.Context, req *UpdateSwapChannelRequest, opts ...http.CallOption) (rsp *UpdateSwapChannelReply, err error)
	UpdateToken(ctx context.Context, req *UpdateTokenRequest, opts ...http.CallOption) (rsp *UpdateTokenReply, err error)
}

type SwapServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewSwapServiceHTTPClient(client *http.Client) SwapServiceHTTPClient {
	return &SwapServiceHTTPClientImpl{client}
}

func (c *SwapServiceHTTPClientImpl) CreateHotToken(ctx context.Context, in *CreateHotTokenRequest, opts ...http.CallOption) (*CreateHotTokenReply, error) {
	var out CreateHotTokenReply
	pattern := "/admin/v1/swap/token/hot"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSwapServiceCreateHotToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) DeleteHotToken(ctx context.Context, in *DeleteHotTokenRequest, opts ...http.CallOption) (*DeleteHotTokenReply, error) {
	var out DeleteHotTokenReply
	pattern := "/admin/v1/swap/token/hot/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSwapServiceDeleteHotToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) GetSwapRecord(ctx context.Context, in *GetSwapRecordRequest, opts ...http.CallOption) (*SwapRecord, error) {
	var out SwapRecord
	pattern := "/admin/v1/swap/record/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSwapServiceGetSwapRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) ListHotToken(ctx context.Context, in *ListHotTokenRequest, opts ...http.CallOption) (*ListHotTokenReply, error) {
	var out ListHotTokenReply
	pattern := "/admin/v1/swap/token/hot"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSwapServiceListHotToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) ListSwapChannel(ctx context.Context, in *ListSwapChannelRequest, opts ...http.CallOption) (*ListSwapChannelReply, error) {
	var out ListSwapChannelReply
	pattern := "/admin/v1/swap/channel"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSwapServiceListSwapChannel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) ListSwapRecord(ctx context.Context, in *ListSwapRecordRequest, opts ...http.CallOption) (*ListSwapRecordReply, error) {
	var out ListSwapRecordReply
	pattern := "/admin/v1/swap/record"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSwapServiceListSwapRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) ListToken(ctx context.Context, in *ListTokenRequest, opts ...http.CallOption) (*ListTokenReply, error) {
	var out ListTokenReply
	pattern := "/admin/v1/swap/token"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationSwapServiceListToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) SortHotToken(ctx context.Context, in *CommonSortReq, opts ...http.CallOption) (*CommonSortReply, error) {
	var out CommonSortReply
	pattern := "/admin/v1/swap/token/hot/sort"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSwapServiceSortHotToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) UpdateSwapChannel(ctx context.Context, in *UpdateSwapChannelRequest, opts ...http.CallOption) (*UpdateSwapChannelReply, error) {
	var out UpdateSwapChannelReply
	pattern := "/admin/v1/swap/channel/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSwapServiceUpdateSwapChannel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SwapServiceHTTPClientImpl) UpdateToken(ctx context.Context, in *UpdateTokenRequest, opts ...http.CallOption) (*UpdateTokenReply, error) {
	var out UpdateTokenReply
	pattern := "/admin/v1/swap/token/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSwapServiceUpdateToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
