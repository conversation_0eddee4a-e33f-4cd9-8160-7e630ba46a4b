syntax = "proto3";

package api.walletadmin.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "byd_wallet/api/walletadmin/v1;v1";

service UserGuideService {
  // 创建用户指南
  rpc CreateUserGuide(CreateUserGuideReq) returns (CreateUserGuideReply) {
    option (google.api.http) = {
      post: "/admin/v1/user-guides"
      body: "*"
    };
  }

  // 更新用户指南
  rpc UpdateUserGuide(UpdateUserGuideReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/user-guides/{id}"
      body: "*"
    };
  }

  // 删除用户指南
  rpc DeleteUserGuide(DeleteUserGuideReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/admin/v1/user-guides/{id}"};
  }

  // 用户指南列表
  rpc ListUserGuides(ListUserGuidesReq) returns (ListUserGuidesReply) {
    option (google.api.http) = {get: "/admin/v1/user-guides"};
  }

  // 获取用户指南详情
  rpc GetUserGuide(GetUserGuideReq) returns (UserGuideInfo) {
    option (google.api.http) = {get: "/admin/v1/user-guides/{id}"};
  }

  // 创建用户指南分类
  rpc CreateUserGuideCategory(CreateUserGuideCategoryReq) returns (CreateUserGuideCategoryReply) {
    option (google.api.http) = {
      post: "/admin/v1/user-guide-categories"
      body: "*"
    };
  }

  // 更新用户指南分类
  rpc UpdateUserGuideCategory(UpdateUserGuideCategoryReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/admin/v1/user-guide-categories/{id}"
      body: "*"
    };
  }

  // 删除用户指南分类
  rpc DeleteUserGuideCategory(DeleteUserGuideCategoryReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {delete: "/admin/v1/user-guide-categories/{id}"};
  }

  // 用户指南分类列表
  rpc ListUserGuideCategories(ListUserGuideCategoriesReq) returns (ListUserGuideCategoriesReply) {
    option (google.api.http) = {get: "/admin/v1/user-guide-categories"};
  }

  // 获取用户指南分类详情
  rpc GetUserGuideCategory(GetUserGuideCategoryReq) returns (UserGuideCategoryInfo) {
    option (google.api.http) = {get: "/admin/v1/user-guide-categories/{id}"};
  }
}

// 用户指南内容
message UserGuideContentInfo {
  // 正文文本
  string content = 1;
  // 图片URL
  string photo_url = 2;
  // 是否展示
  bool display = 3;
}

// 创建用户指南请求
message CreateUserGuideReq {
  // 分类ID
  uint64 category_id = 1 [(buf.validate.field).uint64.gt = 0];
  // 语言
  string language = 2 [(buf.validate.field).string.min_len = 1];
  // 标题
  string title = 3 [(buf.validate.field).string.min_len = 1];
  // 简介
  string summary = 4;
  // 是否展示
  bool display = 5;
  // 内容列表
  repeated UserGuideContentInfo contents = 6;
}

// 创建用户指南响应
message CreateUserGuideReply {}

// 更新用户指南请求
message UpdateUserGuideReq {
  // 用户指南ID
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // 分类ID
  uint64 category_id = 2;
  // 语言
  string language = 3;
  // 标题
  string title = 4;
  // 简介
  string summary = 5;
  // 是否展示
  bool display = 6;
  // 内容列表
  repeated UserGuideContentInfo contents = 7;
}

// 删除用户指南请求
message DeleteUserGuideReq {
  // 用户指南ID
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

// 用户指南列表请求
message ListUserGuidesReq {
  // 分类ID筛选
  uint64 category_id = 1;
  // 语言筛选
  string language = 2;
  // 页码
  int64 page = 3 [(buf.validate.field).int64.gte = 1];
  // 每页数量
  int64 page_size = 4 [
    (buf.validate.field).int64.gte = 1,
    (buf.validate.field).int64.lte = 100
  ];
}

// 用户指南列表响应
message ListUserGuidesReply {
  // 用户指南列表
  repeated UserGuideInfo list = 1;
  // 总数
  int64 total_count = 2;
}

// 获取用户指南详情请求
message GetUserGuideReq {
  // 用户指南ID
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

// 用户指南信息
message UserGuideInfo {
  // 用户指南ID
  uint64 id = 1;
  // 分类ID
  uint64 category_id = 2;
  // 语言
  string language = 3;
  // 标题
  string title = 4;
  // 简介
  string summary = 5;
  // 是否展示
  bool display = 6;
  // 内容列表
  repeated UserGuideContentInfo contents = 7;
  // 分类
  string category = 8;
}

// 创建用户指南分类请求
message CreateUserGuideCategoryReq {
  // 语言
  string language = 1 [(buf.validate.field).string.min_len = 1];
  // 名称
  string name = 2 [(buf.validate.field).string.min_len = 1];
  // 图标URL
  string logo_url = 3;
  // 是否展示
  bool display = 4;
}

// 创建用户指南分类响应
message CreateUserGuideCategoryReply {}

// 更新用户指南分类请求
message UpdateUserGuideCategoryReq {
  // 分类ID
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
  // 语言
  string language = 2 [(buf.validate.field).string.min_len = 1];
  // 名称
  string name = 3 [(buf.validate.field).string.min_len = 1];
  // 图标URL
  string logo_url = 4;
  // 是否展示
  bool display = 5;
}

// 删除用户指南分类请求
message DeleteUserGuideCategoryReq {
  // 分类ID
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

// 用户指南分类列表请求
message ListUserGuideCategoriesReq {
  // 语言筛选
  string language = 1;
  // 页码
  int64 page = 2 [(buf.validate.field).int64.gte = 1];
  // 每页数量
  int64 page_size = 3 [
    (buf.validate.field).int64.gte = 1,
    (buf.validate.field).int64.lte = 100
  ];
}

// 用户指南分类列表响应
message ListUserGuideCategoriesReply {
  // 分类列表
  repeated UserGuideCategoryInfo list = 1;
  // 总数
  int64 total_count = 2;
}

// 获取用户指南分类详情请求
message GetUserGuideCategoryReq {
  // 分类ID
  uint64 id = 1 [(buf.validate.field).uint64.gt = 0];
}

// 用户指南分类信息
message UserGuideCategoryInfo {
  // 分类ID
  uint64 id = 1;
  // 语言
  string language = 2;
  // 名称
  string name = 3;
  // 图标URL
  string logo_url = 4;
  // 是否展示
  bool display = 5;
  // 文章数量
  int64 guide_count = 6;
}