// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/coin.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateCoinStarSortReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 当前排序(来自列表的sort_order字段值)
	CurrentSort int64 `protobuf:"varint,2,opt,name=current_sort,json=currentSort,proto3" json:"current_sort,omitempty"`
	// 添加排序(数值越大,排序越靠前)
	TargetSort    int64 `protobuf:"varint,3,opt,name=target_sort,json=targetSort,proto3" json:"target_sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCoinStarSortReq) Reset() {
	*x = UpdateCoinStarSortReq{}
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCoinStarSortReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCoinStarSortReq) ProtoMessage() {}

func (x *UpdateCoinStarSortReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCoinStarSortReq.ProtoReflect.Descriptor instead.
func (*UpdateCoinStarSortReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_coin_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateCoinStarSortReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCoinStarSortReq) GetCurrentSort() int64 {
	if x != nil {
		return x.CurrentSort
	}
	return 0
}

func (x *UpdateCoinStarSortReq) GetTargetSort() int64 {
	if x != nil {
		return x.TargetSort
	}
	return 0
}

type ListCoinStarReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 页码
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 页容量
	PageSize int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 链索引(传值-1查询全部链)
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 币符号
	Symbol *string `protobuf:"bytes,4,opt,name=symbol,proto3,oneof" json:"symbol,omitempty"`
	// 币合约地址
	Address       *string `protobuf:"bytes,5,opt,name=address,proto3,oneof" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCoinStarReq) Reset() {
	*x = ListCoinStarReq{}
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCoinStarReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCoinStarReq) ProtoMessage() {}

func (x *ListCoinStarReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCoinStarReq.ProtoReflect.Descriptor instead.
func (*ListCoinStarReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_coin_proto_rawDescGZIP(), []int{1}
}

func (x *ListCoinStarReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListCoinStarReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCoinStarReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListCoinStarReq) GetSymbol() string {
	if x != nil && x.Symbol != nil {
		return *x.Symbol
	}
	return ""
}

func (x *ListCoinStarReq) GetAddress() string {
	if x != nil && x.Address != nil {
		return *x.Address
	}
	return ""
}

type CoinStar struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 创建时间(时间戳,单位秒)
	CreatedAt int64 `protobuf:"varint,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 币名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,5,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 精度位数
	Decimals int64 `protobuf:"varint,6,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 币合约地址
	Address string `protobuf:"bytes,7,opt,name=address,proto3" json:"address,omitempty"`
	// 链名称
	ChainName string `protobuf:"bytes,8,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	// 币图标
	LogoUrl string `protobuf:"bytes,9,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 排序
	SortOrder     int64 `protobuf:"varint,10,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CoinStar) Reset() {
	*x = CoinStar{}
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CoinStar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoinStar) ProtoMessage() {}

func (x *CoinStar) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoinStar.ProtoReflect.Descriptor instead.
func (*CoinStar) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_coin_proto_rawDescGZIP(), []int{2}
}

func (x *CoinStar) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CoinStar) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *CoinStar) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CoinStar) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *CoinStar) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *CoinStar) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *CoinStar) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *CoinStar) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

func (x *CoinStar) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *CoinStar) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

type ListCoinStarReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*CoinStar            `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总记录数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCoinStarReply) Reset() {
	*x = ListCoinStarReply{}
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCoinStarReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCoinStarReply) ProtoMessage() {}

func (x *ListCoinStarReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCoinStarReply.ProtoReflect.Descriptor instead.
func (*ListCoinStarReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_coin_proto_rawDescGZIP(), []int{3}
}

func (x *ListCoinStarReply) GetList() []*CoinStar {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListCoinStarReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type DeleteCoinStarReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id            uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCoinStarReq) Reset() {
	*x = DeleteCoinStarReq{}
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCoinStarReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCoinStarReq) ProtoMessage() {}

func (x *DeleteCoinStarReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCoinStarReq.ProtoReflect.Descriptor instead.
func (*DeleteCoinStarReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_coin_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteCoinStarReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type CreateCoinStarReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 币种ID
	CoinId        uint64 `protobuf:"varint,1,opt,name=coin_id,json=coinId,proto3" json:"coin_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCoinStarReq) Reset() {
	*x = CreateCoinStarReq{}
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCoinStarReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCoinStarReq) ProtoMessage() {}

func (x *CreateCoinStarReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCoinStarReq.ProtoReflect.Descriptor instead.
func (*CreateCoinStarReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_coin_proto_rawDescGZIP(), []int{5}
}

func (x *CreateCoinStarReq) GetCoinId() uint64 {
	if x != nil {
		return x.CoinId
	}
	return 0
}

type UpdateCoinReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 币图标
	LogoUrl string `protobuf:"bytes,2,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 是否展示
	IsDisplay     bool `protobuf:"varint,3,opt,name=is_display,json=isDisplay,proto3" json:"is_display,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCoinReq) Reset() {
	*x = UpdateCoinReq{}
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCoinReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCoinReq) ProtoMessage() {}

func (x *UpdateCoinReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCoinReq.ProtoReflect.Descriptor instead.
func (*UpdateCoinReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_coin_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateCoinReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCoinReq) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *UpdateCoinReq) GetIsDisplay() bool {
	if x != nil {
		return x.IsDisplay
	}
	return false
}

type ListCoinReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 页码
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 页容量
	PageSize int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 链索引(传值-1查询全部链)
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 币符号
	Symbol *string `protobuf:"bytes,4,opt,name=symbol,proto3,oneof" json:"symbol,omitempty"`
	// 币合约地址
	Address       *string `protobuf:"bytes,5,opt,name=address,proto3,oneof" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCoinReq) Reset() {
	*x = ListCoinReq{}
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCoinReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCoinReq) ProtoMessage() {}

func (x *ListCoinReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCoinReq.ProtoReflect.Descriptor instead.
func (*ListCoinReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_coin_proto_rawDescGZIP(), []int{7}
}

func (x *ListCoinReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListCoinReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCoinReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListCoinReq) GetSymbol() string {
	if x != nil && x.Symbol != nil {
		return *x.Symbol
	}
	return ""
}

func (x *ListCoinReq) GetAddress() string {
	if x != nil && x.Address != nil {
		return *x.Address
	}
	return ""
}

type Coin struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 币名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,4,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 精度位数
	Decimals int64 `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 币合约地址
	Address string `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	// 创建时间(时间戳,单位秒)
	CreatedAt int64 `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 链名称
	ChainName string `protobuf:"bytes,8,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	// 币图标
	LogoUrl string `protobuf:"bytes,9,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 是否展示
	IsDisplay bool `protobuf:"varint,10,opt,name=is_display,json=isDisplay,proto3" json:"is_display,omitempty"`
	// 网络ID
	ChainId       string `protobuf:"bytes,11,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Coin) Reset() {
	*x = Coin{}
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Coin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Coin) ProtoMessage() {}

func (x *Coin) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Coin.ProtoReflect.Descriptor instead.
func (*Coin) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_coin_proto_rawDescGZIP(), []int{8}
}

func (x *Coin) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Coin) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Coin) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Coin) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Coin) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *Coin) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Coin) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Coin) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

func (x *Coin) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *Coin) GetIsDisplay() bool {
	if x != nil {
		return x.IsDisplay
	}
	return false
}

func (x *Coin) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

type ListCoinReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*Coin                `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总记录数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCoinReply) Reset() {
	*x = ListCoinReply{}
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCoinReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCoinReply) ProtoMessage() {}

func (x *ListCoinReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_coin_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCoinReply.ProtoReflect.Descriptor instead.
func (*ListCoinReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_coin_proto_rawDescGZIP(), []int{9}
}

func (x *ListCoinReply) GetList() []*Coin {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListCoinReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

var File_api_walletadmin_v1_coin_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_coin_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/walletadmin/v1/coin.proto\x12\x12api.walletadmin.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1bgoogle/protobuf/empty.proto\"t\n" +
	"\x15UpdateCoinStarSortReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12!\n" +
	"\fcurrent_sort\x18\x02 \x01(\x03R\vcurrentSort\x12\x1f\n" +
	"\vtarget_sort\x18\x03 \x01(\x03R\n" +
	"targetSort\"\xca\x01\n" +
	"\x0fListCoinStarReq\x12\x1b\n" +
	"\x04page\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12&\n" +
	"\tpage_size\x18\x02 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\bpageSize\x12\x1f\n" +
	"\vchain_index\x18\x03 \x01(\x03R\n" +
	"chainIndex\x12\x1b\n" +
	"\x06symbol\x18\x04 \x01(\tH\x00R\x06symbol\x88\x01\x01\x12\x1d\n" +
	"\aaddress\x18\x05 \x01(\tH\x01R\aaddress\x88\x01\x01B\t\n" +
	"\a_symbolB\n" +
	"\n" +
	"\b_address\"\x95\x02\n" +
	"\bCoinStar\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1d\n" +
	"\n" +
	"created_at\x18\x02 \x01(\x03R\tcreatedAt\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x04 \x01(\tR\x06symbol\x12\x1f\n" +
	"\vchain_index\x18\x05 \x01(\x03R\n" +
	"chainIndex\x12\x1a\n" +
	"\bdecimals\x18\x06 \x01(\x03R\bdecimals\x12\x18\n" +
	"\aaddress\x18\a \x01(\tR\aaddress\x12\x1d\n" +
	"\n" +
	"chain_name\x18\b \x01(\tR\tchainName\x12\x19\n" +
	"\blogo_url\x18\t \x01(\tR\alogoUrl\x12\x1d\n" +
	"\n" +
	"sort_order\x18\n" +
	" \x01(\x03R\tsortOrder\"f\n" +
	"\x11ListCoinStarReply\x120\n" +
	"\x04list\x18\x01 \x03(\v2\x1c.api.walletadmin.v1.CoinStarR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount\",\n" +
	"\x11DeleteCoinStarReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"5\n" +
	"\x11CreateCoinStarReq\x12 \n" +
	"\acoin_id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x06coinId\"l\n" +
	"\rUpdateCoinReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12#\n" +
	"\blogo_url\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x88\x01\x01R\alogoUrl\x12\x1d\n" +
	"\n" +
	"is_display\x18\x03 \x01(\bR\tisDisplay\"\xc6\x01\n" +
	"\vListCoinReq\x12\x1b\n" +
	"\x04page\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12&\n" +
	"\tpage_size\x18\x02 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\bpageSize\x12\x1f\n" +
	"\vchain_index\x18\x03 \x01(\x03R\n" +
	"chainIndex\x12\x1b\n" +
	"\x06symbol\x18\x04 \x01(\tH\x00R\x06symbol\x88\x01\x01\x12\x1d\n" +
	"\aaddress\x18\x05 \x01(\tH\x01R\aaddress\x88\x01\x01B\t\n" +
	"\a_symbolB\n" +
	"\n" +
	"\b_address\"\xac\x02\n" +
	"\x04Coin\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x03 \x01(\tR\x06symbol\x12\x1f\n" +
	"\vchain_index\x18\x04 \x01(\x03R\n" +
	"chainIndex\x12\x1a\n" +
	"\bdecimals\x18\x05 \x01(\x03R\bdecimals\x12\x18\n" +
	"\aaddress\x18\x06 \x01(\tR\aaddress\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"chain_name\x18\b \x01(\tR\tchainName\x12\x19\n" +
	"\blogo_url\x18\t \x01(\tR\alogoUrl\x12\x1d\n" +
	"\n" +
	"is_display\x18\n" +
	" \x01(\bR\tisDisplay\x12\x19\n" +
	"\bchain_id\x18\v \x01(\tR\achainId\"^\n" +
	"\rListCoinReply\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.api.walletadmin.v1.CoinR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount2\xf1\x05\n" +
	"\aCoinSrv\x12p\n" +
	"\bListCoin\x12\x1f.api.walletadmin.v1.ListCoinReq\x1a!.api.walletadmin.v1.ListCoinReply\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/admin/v1/coin/list_coin\x12n\n" +
	"\n" +
	"UpdateCoin\x12!.api.walletadmin.v1.UpdateCoinReq\x1a\x16.google.protobuf.Empty\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\x1a\x1a/admin/v1/coin/update_coin\x12{\n" +
	"\x0eCreateCoinStar\x12%.api.walletadmin.v1.CreateCoinStarReq\x1a\x16.google.protobuf.Empty\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/admin/v1/coin/create_coin_star\x12\x81\x01\n" +
	"\fListCoinStar\x12#.api.walletadmin.v1.ListCoinStarReq\x1a%.api.walletadmin.v1.ListCoinStarReply\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/admin/v1/coin/list_coin_star\x12x\n" +
	"\x0eDeleteCoinStar\x12%.api.walletadmin.v1.DeleteCoinStarReq\x1a\x16.google.protobuf.Empty\"'\x82\xd3\xe4\x93\x02!*\x1f/admin/v1/coin/delete_coin_star\x12\x88\x01\n" +
	"\x12UpdateCoinStarSort\x12).api.walletadmin.v1.UpdateCoinStarSortReq\x1a\x16.google.protobuf.Empty\"/\x82\xd3\xe4\x93\x02):\x01*2$/admin/v1/coin/update_coin_star_sortB\xaf\x01\n" +
	"\x16com.api.walletadmin.v1B\tCoinProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_coin_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_coin_proto_rawDescData []byte
)

func file_api_walletadmin_v1_coin_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_coin_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_coin_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_coin_proto_rawDesc), len(file_api_walletadmin_v1_coin_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_coin_proto_rawDescData
}

var file_api_walletadmin_v1_coin_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_walletadmin_v1_coin_proto_goTypes = []any{
	(*UpdateCoinStarSortReq)(nil), // 0: api.walletadmin.v1.UpdateCoinStarSortReq
	(*ListCoinStarReq)(nil),       // 1: api.walletadmin.v1.ListCoinStarReq
	(*CoinStar)(nil),              // 2: api.walletadmin.v1.CoinStar
	(*ListCoinStarReply)(nil),     // 3: api.walletadmin.v1.ListCoinStarReply
	(*DeleteCoinStarReq)(nil),     // 4: api.walletadmin.v1.DeleteCoinStarReq
	(*CreateCoinStarReq)(nil),     // 5: api.walletadmin.v1.CreateCoinStarReq
	(*UpdateCoinReq)(nil),         // 6: api.walletadmin.v1.UpdateCoinReq
	(*ListCoinReq)(nil),           // 7: api.walletadmin.v1.ListCoinReq
	(*Coin)(nil),                  // 8: api.walletadmin.v1.Coin
	(*ListCoinReply)(nil),         // 9: api.walletadmin.v1.ListCoinReply
	(*emptypb.Empty)(nil),         // 10: google.protobuf.Empty
}
var file_api_walletadmin_v1_coin_proto_depIdxs = []int32{
	2,  // 0: api.walletadmin.v1.ListCoinStarReply.list:type_name -> api.walletadmin.v1.CoinStar
	8,  // 1: api.walletadmin.v1.ListCoinReply.list:type_name -> api.walletadmin.v1.Coin
	7,  // 2: api.walletadmin.v1.CoinSrv.ListCoin:input_type -> api.walletadmin.v1.ListCoinReq
	6,  // 3: api.walletadmin.v1.CoinSrv.UpdateCoin:input_type -> api.walletadmin.v1.UpdateCoinReq
	5,  // 4: api.walletadmin.v1.CoinSrv.CreateCoinStar:input_type -> api.walletadmin.v1.CreateCoinStarReq
	1,  // 5: api.walletadmin.v1.CoinSrv.ListCoinStar:input_type -> api.walletadmin.v1.ListCoinStarReq
	4,  // 6: api.walletadmin.v1.CoinSrv.DeleteCoinStar:input_type -> api.walletadmin.v1.DeleteCoinStarReq
	0,  // 7: api.walletadmin.v1.CoinSrv.UpdateCoinStarSort:input_type -> api.walletadmin.v1.UpdateCoinStarSortReq
	9,  // 8: api.walletadmin.v1.CoinSrv.ListCoin:output_type -> api.walletadmin.v1.ListCoinReply
	10, // 9: api.walletadmin.v1.CoinSrv.UpdateCoin:output_type -> google.protobuf.Empty
	10, // 10: api.walletadmin.v1.CoinSrv.CreateCoinStar:output_type -> google.protobuf.Empty
	3,  // 11: api.walletadmin.v1.CoinSrv.ListCoinStar:output_type -> api.walletadmin.v1.ListCoinStarReply
	10, // 12: api.walletadmin.v1.CoinSrv.DeleteCoinStar:output_type -> google.protobuf.Empty
	10, // 13: api.walletadmin.v1.CoinSrv.UpdateCoinStarSort:output_type -> google.protobuf.Empty
	8,  // [8:14] is the sub-list for method output_type
	2,  // [2:8] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_coin_proto_init() }
func file_api_walletadmin_v1_coin_proto_init() {
	if File_api_walletadmin_v1_coin_proto != nil {
		return
	}
	file_api_walletadmin_v1_coin_proto_msgTypes[1].OneofWrappers = []any{}
	file_api_walletadmin_v1_coin_proto_msgTypes[7].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_coin_proto_rawDesc), len(file_api_walletadmin_v1_coin_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_coin_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_coin_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_coin_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_coin_proto = out.File
	file_api_walletadmin_v1_coin_proto_goTypes = nil
	file_api_walletadmin_v1_coin_proto_depIdxs = nil
}
