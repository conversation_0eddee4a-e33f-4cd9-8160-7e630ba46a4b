// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/tx.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListTxReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 页码
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 页容量
	PageSize int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 链索引(默认bitcoin)
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 转出地址
	FromAddress *string `protobuf:"bytes,4,opt,name=from_address,json=fromAddress,proto3,oneof" json:"from_address,omitempty"`
	// 转入地址
	ToAddress *string `protobuf:"bytes,5,opt,name=to_address,json=toAddress,proto3,oneof" json:"to_address,omitempty"`
	// 交易哈希
	TxHash        *string `protobuf:"bytes,6,opt,name=tx_hash,json=txHash,proto3,oneof" json:"tx_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTxReq) Reset() {
	*x = ListTxReq{}
	mi := &file_api_walletadmin_v1_tx_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTxReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTxReq) ProtoMessage() {}

func (x *ListTxReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_tx_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTxReq.ProtoReflect.Descriptor instead.
func (*ListTxReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_tx_proto_rawDescGZIP(), []int{0}
}

func (x *ListTxReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTxReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTxReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListTxReq) GetFromAddress() string {
	if x != nil && x.FromAddress != nil {
		return *x.FromAddress
	}
	return ""
}

func (x *ListTxReq) GetToAddress() string {
	if x != nil && x.ToAddress != nil {
		return *x.ToAddress
	}
	return ""
}

func (x *ListTxReq) GetTxHash() string {
	if x != nil && x.TxHash != nil {
		return *x.TxHash
	}
	return ""
}

type Tx struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 币名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,4,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 精度位数
	Decimals int64 `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 币合约地址
	TokenAddress string `protobuf:"bytes,6,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	// 交易(时间戳,单位秒)
	TxTime int64 `protobuf:"varint,7,opt,name=tx_time,json=txTime,proto3" json:"tx_time,omitempty"`
	// 链名称
	ChainName string `protobuf:"bytes,8,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	// 转出地址
	FromAddress string `protobuf:"bytes,9,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 转入地址
	ToAddress string `protobuf:"bytes,10,opt,name=to_address,json=toAddress,proto3" json:"to_address,omitempty"`
	// 交易状态(success,fail)
	Status string `protobuf:"bytes,11,opt,name=status,proto3" json:"status,omitempty"`
	// 交易金额
	Value string `protobuf:"bytes,12,opt,name=value,proto3" json:"value,omitempty"`
	// 交易手续费
	Fee string `protobuf:"bytes,13,opt,name=fee,proto3" json:"fee,omitempty"`
	// 交易类型
	Method        string `protobuf:"bytes,14,opt,name=method,proto3" json:"method,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Tx) Reset() {
	*x = Tx{}
	mi := &file_api_walletadmin_v1_tx_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Tx) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Tx) ProtoMessage() {}

func (x *Tx) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_tx_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Tx.ProtoReflect.Descriptor instead.
func (*Tx) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_tx_proto_rawDescGZIP(), []int{1}
}

func (x *Tx) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Tx) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Tx) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Tx) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Tx) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *Tx) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *Tx) GetTxTime() int64 {
	if x != nil {
		return x.TxTime
	}
	return 0
}

func (x *Tx) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

func (x *Tx) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *Tx) GetToAddress() string {
	if x != nil {
		return x.ToAddress
	}
	return ""
}

func (x *Tx) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Tx) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Tx) GetFee() string {
	if x != nil {
		return x.Fee
	}
	return ""
}

func (x *Tx) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

type ListTxReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*Tx                  `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总记录数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTxReply) Reset() {
	*x = ListTxReply{}
	mi := &file_api_walletadmin_v1_tx_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTxReply) ProtoMessage() {}

func (x *ListTxReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_tx_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTxReply.ProtoReflect.Descriptor instead.
func (*ListTxReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_tx_proto_rawDescGZIP(), []int{2}
}

func (x *ListTxReply) GetList() []*Tx {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListTxReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

var File_api_walletadmin_v1_tx_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_tx_proto_rawDesc = "" +
	"\n" +
	"\x1bapi/walletadmin/v1/tx.proto\x12\x12api.walletadmin.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\"\x99\x02\n" +
	"\tListTxReq\x12\x1b\n" +
	"\x04page\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12&\n" +
	"\tpage_size\x18\x02 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\bpageSize\x121\n" +
	"\vchain_index\x18\x03 \x01(\x03B\x10\xbaH\r\"\v \xff\xff\xff\xff\xff\xff\xff\xff\xff\x01R\n" +
	"chainIndex\x12&\n" +
	"\ffrom_address\x18\x04 \x01(\tH\x00R\vfromAddress\x88\x01\x01\x12\"\n" +
	"\n" +
	"to_address\x18\x05 \x01(\tH\x01R\ttoAddress\x88\x01\x01\x12\x1c\n" +
	"\atx_hash\x18\x06 \x01(\tH\x02R\x06txHash\x88\x01\x01B\x0f\n" +
	"\r_from_addressB\r\n" +
	"\v_to_addressB\n" +
	"\n" +
	"\b_tx_hash\"\xf4\x02\n" +
	"\x02Tx\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x03 \x01(\tR\x06symbol\x12\x1f\n" +
	"\vchain_index\x18\x04 \x01(\x03R\n" +
	"chainIndex\x12\x1a\n" +
	"\bdecimals\x18\x05 \x01(\x03R\bdecimals\x12#\n" +
	"\rtoken_address\x18\x06 \x01(\tR\ftokenAddress\x12\x17\n" +
	"\atx_time\x18\a \x01(\x03R\x06txTime\x12\x1d\n" +
	"\n" +
	"chain_name\x18\b \x01(\tR\tchainName\x12!\n" +
	"\ffrom_address\x18\t \x01(\tR\vfromAddress\x12\x1d\n" +
	"\n" +
	"to_address\x18\n" +
	" \x01(\tR\ttoAddress\x12\x16\n" +
	"\x06status\x18\v \x01(\tR\x06status\x12\x14\n" +
	"\x05value\x18\f \x01(\tR\x05value\x12\x10\n" +
	"\x03fee\x18\r \x01(\tR\x03fee\x12\x16\n" +
	"\x06method\x18\x0e \x01(\tR\x06method\"Z\n" +
	"\vListTxReply\x12*\n" +
	"\x04list\x18\x01 \x03(\v2\x16.api.walletadmin.v1.TxR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount2o\n" +
	"\x05TxSrv\x12f\n" +
	"\x06ListTx\x12\x1d.api.walletadmin.v1.ListTxReq\x1a\x1f.api.walletadmin.v1.ListTxReply\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/admin/v1/tx/list_txB\xad\x01\n" +
	"\x16com.api.walletadmin.v1B\aTxProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_tx_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_tx_proto_rawDescData []byte
)

func file_api_walletadmin_v1_tx_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_tx_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_tx_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_tx_proto_rawDesc), len(file_api_walletadmin_v1_tx_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_tx_proto_rawDescData
}

var file_api_walletadmin_v1_tx_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_walletadmin_v1_tx_proto_goTypes = []any{
	(*ListTxReq)(nil),   // 0: api.walletadmin.v1.ListTxReq
	(*Tx)(nil),          // 1: api.walletadmin.v1.Tx
	(*ListTxReply)(nil), // 2: api.walletadmin.v1.ListTxReply
}
var file_api_walletadmin_v1_tx_proto_depIdxs = []int32{
	1, // 0: api.walletadmin.v1.ListTxReply.list:type_name -> api.walletadmin.v1.Tx
	0, // 1: api.walletadmin.v1.TxSrv.ListTx:input_type -> api.walletadmin.v1.ListTxReq
	2, // 2: api.walletadmin.v1.TxSrv.ListTx:output_type -> api.walletadmin.v1.ListTxReply
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_tx_proto_init() }
func file_api_walletadmin_v1_tx_proto_init() {
	if File_api_walletadmin_v1_tx_proto != nil {
		return
	}
	file_api_walletadmin_v1_tx_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_tx_proto_rawDesc), len(file_api_walletadmin_v1_tx_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_tx_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_tx_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_tx_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_tx_proto = out.File
	file_api_walletadmin_v1_tx_proto_goTypes = nil
	file_api_walletadmin_v1_tx_proto_depIdxs = nil
}
