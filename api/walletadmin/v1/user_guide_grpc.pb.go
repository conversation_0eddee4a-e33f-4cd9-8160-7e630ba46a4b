// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/walletadmin/v1/user_guide.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserGuideService_CreateUserGuide_FullMethodName         = "/api.walletadmin.v1.UserGuideService/CreateUserGuide"
	UserGuideService_UpdateUserGuide_FullMethodName         = "/api.walletadmin.v1.UserGuideService/UpdateUserGuide"
	UserGuideService_DeleteUserGuide_FullMethodName         = "/api.walletadmin.v1.UserGuideService/DeleteUserGuide"
	UserGuideService_ListUserGuides_FullMethodName          = "/api.walletadmin.v1.UserGuideService/ListUserGuides"
	UserGuideService_GetUserGuide_FullMethodName            = "/api.walletadmin.v1.UserGuideService/GetUserGuide"
	UserGuideService_CreateUserGuideCategory_FullMethodName = "/api.walletadmin.v1.UserGuideService/CreateUserGuideCategory"
	UserGuideService_UpdateUserGuideCategory_FullMethodName = "/api.walletadmin.v1.UserGuideService/UpdateUserGuideCategory"
	UserGuideService_DeleteUserGuideCategory_FullMethodName = "/api.walletadmin.v1.UserGuideService/DeleteUserGuideCategory"
	UserGuideService_ListUserGuideCategories_FullMethodName = "/api.walletadmin.v1.UserGuideService/ListUserGuideCategories"
	UserGuideService_GetUserGuideCategory_FullMethodName    = "/api.walletadmin.v1.UserGuideService/GetUserGuideCategory"
)

// UserGuideServiceClient is the client API for UserGuideService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserGuideServiceClient interface {
	// 创建用户指南
	CreateUserGuide(ctx context.Context, in *CreateUserGuideReq, opts ...grpc.CallOption) (*CreateUserGuideReply, error)
	// 更新用户指南
	UpdateUserGuide(ctx context.Context, in *UpdateUserGuideReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除用户指南
	DeleteUserGuide(ctx context.Context, in *DeleteUserGuideReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 用户指南列表
	ListUserGuides(ctx context.Context, in *ListUserGuidesReq, opts ...grpc.CallOption) (*ListUserGuidesReply, error)
	// 获取用户指南详情
	GetUserGuide(ctx context.Context, in *GetUserGuideReq, opts ...grpc.CallOption) (*UserGuideInfo, error)
	// 创建用户指南分类
	CreateUserGuideCategory(ctx context.Context, in *CreateUserGuideCategoryReq, opts ...grpc.CallOption) (*CreateUserGuideCategoryReply, error)
	// 更新用户指南分类
	UpdateUserGuideCategory(ctx context.Context, in *UpdateUserGuideCategoryReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除用户指南分类
	DeleteUserGuideCategory(ctx context.Context, in *DeleteUserGuideCategoryReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 用户指南分类列表
	ListUserGuideCategories(ctx context.Context, in *ListUserGuideCategoriesReq, opts ...grpc.CallOption) (*ListUserGuideCategoriesReply, error)
	// 获取用户指南分类详情
	GetUserGuideCategory(ctx context.Context, in *GetUserGuideCategoryReq, opts ...grpc.CallOption) (*UserGuideCategoryInfo, error)
}

type userGuideServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserGuideServiceClient(cc grpc.ClientConnInterface) UserGuideServiceClient {
	return &userGuideServiceClient{cc}
}

func (c *userGuideServiceClient) CreateUserGuide(ctx context.Context, in *CreateUserGuideReq, opts ...grpc.CallOption) (*CreateUserGuideReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateUserGuideReply)
	err := c.cc.Invoke(ctx, UserGuideService_CreateUserGuide_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideServiceClient) UpdateUserGuide(ctx context.Context, in *UpdateUserGuideReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, UserGuideService_UpdateUserGuide_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideServiceClient) DeleteUserGuide(ctx context.Context, in *DeleteUserGuideReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, UserGuideService_DeleteUserGuide_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideServiceClient) ListUserGuides(ctx context.Context, in *ListUserGuidesReq, opts ...grpc.CallOption) (*ListUserGuidesReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListUserGuidesReply)
	err := c.cc.Invoke(ctx, UserGuideService_ListUserGuides_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideServiceClient) GetUserGuide(ctx context.Context, in *GetUserGuideReq, opts ...grpc.CallOption) (*UserGuideInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserGuideInfo)
	err := c.cc.Invoke(ctx, UserGuideService_GetUserGuide_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideServiceClient) CreateUserGuideCategory(ctx context.Context, in *CreateUserGuideCategoryReq, opts ...grpc.CallOption) (*CreateUserGuideCategoryReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateUserGuideCategoryReply)
	err := c.cc.Invoke(ctx, UserGuideService_CreateUserGuideCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideServiceClient) UpdateUserGuideCategory(ctx context.Context, in *UpdateUserGuideCategoryReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, UserGuideService_UpdateUserGuideCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideServiceClient) DeleteUserGuideCategory(ctx context.Context, in *DeleteUserGuideCategoryReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, UserGuideService_DeleteUserGuideCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideServiceClient) ListUserGuideCategories(ctx context.Context, in *ListUserGuideCategoriesReq, opts ...grpc.CallOption) (*ListUserGuideCategoriesReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListUserGuideCategoriesReply)
	err := c.cc.Invoke(ctx, UserGuideService_ListUserGuideCategories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideServiceClient) GetUserGuideCategory(ctx context.Context, in *GetUserGuideCategoryReq, opts ...grpc.CallOption) (*UserGuideCategoryInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserGuideCategoryInfo)
	err := c.cc.Invoke(ctx, UserGuideService_GetUserGuideCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserGuideServiceServer is the server API for UserGuideService service.
// All implementations must embed UnimplementedUserGuideServiceServer
// for forward compatibility.
type UserGuideServiceServer interface {
	// 创建用户指南
	CreateUserGuide(context.Context, *CreateUserGuideReq) (*CreateUserGuideReply, error)
	// 更新用户指南
	UpdateUserGuide(context.Context, *UpdateUserGuideReq) (*emptypb.Empty, error)
	// 删除用户指南
	DeleteUserGuide(context.Context, *DeleteUserGuideReq) (*emptypb.Empty, error)
	// 用户指南列表
	ListUserGuides(context.Context, *ListUserGuidesReq) (*ListUserGuidesReply, error)
	// 获取用户指南详情
	GetUserGuide(context.Context, *GetUserGuideReq) (*UserGuideInfo, error)
	// 创建用户指南分类
	CreateUserGuideCategory(context.Context, *CreateUserGuideCategoryReq) (*CreateUserGuideCategoryReply, error)
	// 更新用户指南分类
	UpdateUserGuideCategory(context.Context, *UpdateUserGuideCategoryReq) (*emptypb.Empty, error)
	// 删除用户指南分类
	DeleteUserGuideCategory(context.Context, *DeleteUserGuideCategoryReq) (*emptypb.Empty, error)
	// 用户指南分类列表
	ListUserGuideCategories(context.Context, *ListUserGuideCategoriesReq) (*ListUserGuideCategoriesReply, error)
	// 获取用户指南分类详情
	GetUserGuideCategory(context.Context, *GetUserGuideCategoryReq) (*UserGuideCategoryInfo, error)
	mustEmbedUnimplementedUserGuideServiceServer()
}

// UnimplementedUserGuideServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserGuideServiceServer struct{}

func (UnimplementedUserGuideServiceServer) CreateUserGuide(context.Context, *CreateUserGuideReq) (*CreateUserGuideReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserGuide not implemented")
}
func (UnimplementedUserGuideServiceServer) UpdateUserGuide(context.Context, *UpdateUserGuideReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserGuide not implemented")
}
func (UnimplementedUserGuideServiceServer) DeleteUserGuide(context.Context, *DeleteUserGuideReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserGuide not implemented")
}
func (UnimplementedUserGuideServiceServer) ListUserGuides(context.Context, *ListUserGuidesReq) (*ListUserGuidesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserGuides not implemented")
}
func (UnimplementedUserGuideServiceServer) GetUserGuide(context.Context, *GetUserGuideReq) (*UserGuideInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserGuide not implemented")
}
func (UnimplementedUserGuideServiceServer) CreateUserGuideCategory(context.Context, *CreateUserGuideCategoryReq) (*CreateUserGuideCategoryReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserGuideCategory not implemented")
}
func (UnimplementedUserGuideServiceServer) UpdateUserGuideCategory(context.Context, *UpdateUserGuideCategoryReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserGuideCategory not implemented")
}
func (UnimplementedUserGuideServiceServer) DeleteUserGuideCategory(context.Context, *DeleteUserGuideCategoryReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserGuideCategory not implemented")
}
func (UnimplementedUserGuideServiceServer) ListUserGuideCategories(context.Context, *ListUserGuideCategoriesReq) (*ListUserGuideCategoriesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserGuideCategories not implemented")
}
func (UnimplementedUserGuideServiceServer) GetUserGuideCategory(context.Context, *GetUserGuideCategoryReq) (*UserGuideCategoryInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserGuideCategory not implemented")
}
func (UnimplementedUserGuideServiceServer) mustEmbedUnimplementedUserGuideServiceServer() {}
func (UnimplementedUserGuideServiceServer) testEmbeddedByValue()                          {}

// UnsafeUserGuideServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserGuideServiceServer will
// result in compilation errors.
type UnsafeUserGuideServiceServer interface {
	mustEmbedUnimplementedUserGuideServiceServer()
}

func RegisterUserGuideServiceServer(s grpc.ServiceRegistrar, srv UserGuideServiceServer) {
	// If the following call pancis, it indicates UnimplementedUserGuideServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserGuideService_ServiceDesc, srv)
}

func _UserGuideService_CreateUserGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserGuideReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideServiceServer).CreateUserGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideService_CreateUserGuide_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideServiceServer).CreateUserGuide(ctx, req.(*CreateUserGuideReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideService_UpdateUserGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserGuideReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideServiceServer).UpdateUserGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideService_UpdateUserGuide_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideServiceServer).UpdateUserGuide(ctx, req.(*UpdateUserGuideReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideService_DeleteUserGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserGuideReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideServiceServer).DeleteUserGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideService_DeleteUserGuide_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideServiceServer).DeleteUserGuide(ctx, req.(*DeleteUserGuideReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideService_ListUserGuides_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserGuidesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideServiceServer).ListUserGuides(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideService_ListUserGuides_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideServiceServer).ListUserGuides(ctx, req.(*ListUserGuidesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideService_GetUserGuide_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGuideReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideServiceServer).GetUserGuide(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideService_GetUserGuide_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideServiceServer).GetUserGuide(ctx, req.(*GetUserGuideReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideService_CreateUserGuideCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserGuideCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideServiceServer).CreateUserGuideCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideService_CreateUserGuideCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideServiceServer).CreateUserGuideCategory(ctx, req.(*CreateUserGuideCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideService_UpdateUserGuideCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserGuideCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideServiceServer).UpdateUserGuideCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideService_UpdateUserGuideCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideServiceServer).UpdateUserGuideCategory(ctx, req.(*UpdateUserGuideCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideService_DeleteUserGuideCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserGuideCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideServiceServer).DeleteUserGuideCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideService_DeleteUserGuideCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideServiceServer).DeleteUserGuideCategory(ctx, req.(*DeleteUserGuideCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideService_ListUserGuideCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserGuideCategoriesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideServiceServer).ListUserGuideCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideService_ListUserGuideCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideServiceServer).ListUserGuideCategories(ctx, req.(*ListUserGuideCategoriesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideService_GetUserGuideCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserGuideCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideServiceServer).GetUserGuideCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideService_GetUserGuideCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideServiceServer).GetUserGuideCategory(ctx, req.(*GetUserGuideCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserGuideService_ServiceDesc is the grpc.ServiceDesc for UserGuideService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserGuideService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.walletadmin.v1.UserGuideService",
	HandlerType: (*UserGuideServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateUserGuide",
			Handler:    _UserGuideService_CreateUserGuide_Handler,
		},
		{
			MethodName: "UpdateUserGuide",
			Handler:    _UserGuideService_UpdateUserGuide_Handler,
		},
		{
			MethodName: "DeleteUserGuide",
			Handler:    _UserGuideService_DeleteUserGuide_Handler,
		},
		{
			MethodName: "ListUserGuides",
			Handler:    _UserGuideService_ListUserGuides_Handler,
		},
		{
			MethodName: "GetUserGuide",
			Handler:    _UserGuideService_GetUserGuide_Handler,
		},
		{
			MethodName: "CreateUserGuideCategory",
			Handler:    _UserGuideService_CreateUserGuideCategory_Handler,
		},
		{
			MethodName: "UpdateUserGuideCategory",
			Handler:    _UserGuideService_UpdateUserGuideCategory_Handler,
		},
		{
			MethodName: "DeleteUserGuideCategory",
			Handler:    _UserGuideService_DeleteUserGuideCategory_Handler,
		},
		{
			MethodName: "ListUserGuideCategories",
			Handler:    _UserGuideService_ListUserGuideCategories_Handler,
		},
		{
			MethodName: "GetUserGuideCategory",
			Handler:    _UserGuideService_GetUserGuideCategory_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/walletadmin/v1/user_guide.proto",
}
