// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/dapp.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeleteDappTopicReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappTopicReq) Reset() {
	*x = DeleteDappTopicReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappTopicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappTopicReq) ProtoMessage() {}

func (x *DeleteDappTopicReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappTopicReq.ProtoReflect.Descriptor instead.
func (*DeleteDappTopicReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{0}
}

func (x *DeleteDappTopicReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteDappTopicReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappTopicReply) Reset() {
	*x = DeleteDappTopicReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappTopicReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappTopicReply) ProtoMessage() {}

func (x *DeleteDappTopicReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappTopicReply.ProtoReflect.Descriptor instead.
func (*DeleteDappTopicReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{1}
}

type ListDappCategoryRelReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类id
	Id            uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappCategoryRelReq) Reset() {
	*x = ListDappCategoryRelReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappCategoryRelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappCategoryRelReq) ProtoMessage() {}

func (x *ListDappCategoryRelReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappCategoryRelReq.ProtoReflect.Descriptor instead.
func (*ListDappCategoryRelReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{2}
}

func (x *ListDappCategoryRelReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListDappCategoryRelReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Dapp                `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappCategoryRelReply) Reset() {
	*x = ListDappCategoryRelReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappCategoryRelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappCategoryRelReply) ProtoMessage() {}

func (x *ListDappCategoryRelReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappCategoryRelReply.ProtoReflect.Descriptor instead.
func (*ListDappCategoryRelReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{3}
}

func (x *ListDappCategoryRelReply) GetList() []*Dapp {
	if x != nil {
		return x.List
	}
	return nil
}

type DeleteDappCategoryRelReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// dapp id
	DappId        uint64 `protobuf:"varint,2,opt,name=dapp_id,json=dappId,proto3" json:"dapp_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappCategoryRelReq) Reset() {
	*x = DeleteDappCategoryRelReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappCategoryRelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappCategoryRelReq) ProtoMessage() {}

func (x *DeleteDappCategoryRelReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappCategoryRelReq.ProtoReflect.Descriptor instead.
func (*DeleteDappCategoryRelReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteDappCategoryRelReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteDappCategoryRelReq) GetDappId() uint64 {
	if x != nil {
		return x.DappId
	}
	return 0
}

type DeleteDappCategoryRelReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappCategoryRelReply) Reset() {
	*x = DeleteDappCategoryRelReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappCategoryRelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappCategoryRelReply) ProtoMessage() {}

func (x *DeleteDappCategoryRelReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappCategoryRelReply.ProtoReflect.Descriptor instead.
func (*DeleteDappCategoryRelReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{5}
}

type CreateDappCategoryRelReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// dapp id
	DappId        uint64 `protobuf:"varint,2,opt,name=dapp_id,json=dappId,proto3" json:"dapp_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappCategoryRelReq) Reset() {
	*x = CreateDappCategoryRelReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappCategoryRelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappCategoryRelReq) ProtoMessage() {}

func (x *CreateDappCategoryRelReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappCategoryRelReq.ProtoReflect.Descriptor instead.
func (*CreateDappCategoryRelReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{6}
}

func (x *CreateDappCategoryRelReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateDappCategoryRelReq) GetDappId() uint64 {
	if x != nil {
		return x.DappId
	}
	return 0
}

type CreateDappCategoryRelReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappCategoryRelReply) Reset() {
	*x = CreateDappCategoryRelReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappCategoryRelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappCategoryRelReply) ProtoMessage() {}

func (x *CreateDappCategoryRelReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappCategoryRelReply.ProtoReflect.Descriptor instead.
func (*CreateDappCategoryRelReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{7}
}

type CreateDappIndexReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 专题: dapp_topic
	// 分类: dapp_category
	OwnerType string `protobuf:"bytes,1,opt,name=owner_type,json=ownerType,proto3" json:"owner_type,omitempty"`
	// 专题或者分类的主键id
	OwnerId       uint64 `protobuf:"varint,2,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappIndexReq) Reset() {
	*x = CreateDappIndexReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappIndexReq) ProtoMessage() {}

func (x *CreateDappIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappIndexReq.ProtoReflect.Descriptor instead.
func (*CreateDappIndexReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{8}
}

func (x *CreateDappIndexReq) GetOwnerType() string {
	if x != nil {
		return x.OwnerType
	}
	return ""
}

func (x *CreateDappIndexReq) GetOwnerId() uint64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

type CreateDappIndexReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappIndexReply) Reset() {
	*x = CreateDappIndexReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappIndexReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappIndexReply) ProtoMessage() {}

func (x *CreateDappIndexReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappIndexReply.ProtoReflect.Descriptor instead.
func (*CreateDappIndexReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{9}
}

type ListDappIndexReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappIndexReq) Reset() {
	*x = ListDappIndexReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappIndexReq) ProtoMessage() {}

func (x *ListDappIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappIndexReq.ProtoReflect.Descriptor instead.
func (*ListDappIndexReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{10}
}

type ListDappIndexReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DappIndex           `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappIndexReply) Reset() {
	*x = ListDappIndexReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappIndexReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappIndexReply) ProtoMessage() {}

func (x *ListDappIndexReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappIndexReply.ProtoReflect.Descriptor instead.
func (*ListDappIndexReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{11}
}

func (x *ListDappIndexReply) GetList() []*DappIndex {
	if x != nil {
		return x.List
	}
	return nil
}

type DeleteDappIndexReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappIndexReq) Reset() {
	*x = DeleteDappIndexReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappIndexReq) ProtoMessage() {}

func (x *DeleteDappIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappIndexReq.ProtoReflect.Descriptor instead.
func (*DeleteDappIndexReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteDappIndexReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteDappIndexReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappIndexReply) Reset() {
	*x = DeleteDappIndexReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappIndexReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappIndexReply) ProtoMessage() {}

func (x *DeleteDappIndexReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappIndexReply.ProtoReflect.Descriptor instead.
func (*DeleteDappIndexReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{13}
}

type DappIndex struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*Dapp                `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Id    uint64                 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// 序号
	SortOrder int64 `protobuf:"varint,3,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// 分类或者专题名称
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// 专题: dapp_topic
	// 分类: dapp_category
	OwnerType     string `protobuf:"bytes,5,opt,name=owner_type,json=ownerType,proto3" json:"owner_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappIndex) Reset() {
	*x = DappIndex{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappIndex) ProtoMessage() {}

func (x *DappIndex) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappIndex.ProtoReflect.Descriptor instead.
func (*DappIndex) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{14}
}

func (x *DappIndex) GetList() []*Dapp {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DappIndex) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DappIndex) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *DappIndex) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DappIndex) GetOwnerType() string {
	if x != nil {
		return x.OwnerType
	}
	return ""
}

type UpdatedDappIndex struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 序号
	SortOrder     uint64 `protobuf:"varint,2,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatedDappIndex) Reset() {
	*x = UpdatedDappIndex{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatedDappIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatedDappIndex) ProtoMessage() {}

func (x *UpdatedDappIndex) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatedDappIndex.ProtoReflect.Descriptor instead.
func (*UpdatedDappIndex) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{15}
}

func (x *UpdatedDappIndex) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatedDappIndex) GetSortOrder() uint64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

type BatchUpdateDappIndexReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*UpdatedDappIndex    `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchUpdateDappIndexReq) Reset() {
	*x = BatchUpdateDappIndexReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateDappIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateDappIndexReq) ProtoMessage() {}

func (x *BatchUpdateDappIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateDappIndexReq.ProtoReflect.Descriptor instead.
func (*BatchUpdateDappIndexReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{16}
}

func (x *BatchUpdateDappIndexReq) GetList() []*UpdatedDappIndex {
	if x != nil {
		return x.List
	}
	return nil
}

type BatchUpdateDappIndexReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchUpdateDappIndexReply) Reset() {
	*x = BatchUpdateDappIndexReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateDappIndexReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateDappIndexReply) ProtoMessage() {}

func (x *BatchUpdateDappIndexReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateDappIndexReply.ProtoReflect.Descriptor instead.
func (*BatchUpdateDappIndexReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{17}
}

type CreateDappNavigationReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类id
	DappCategoryId uint64 `protobuf:"varint,1,opt,name=dapp_category_id,json=dappCategoryId,proto3" json:"dapp_category_id,omitempty"`
	// 是否展示，默认true
	Show          bool `protobuf:"varint,2,opt,name=show,proto3" json:"show,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappNavigationReq) Reset() {
	*x = CreateDappNavigationReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappNavigationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappNavigationReq) ProtoMessage() {}

func (x *CreateDappNavigationReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappNavigationReq.ProtoReflect.Descriptor instead.
func (*CreateDappNavigationReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{18}
}

func (x *CreateDappNavigationReq) GetDappCategoryId() uint64 {
	if x != nil {
		return x.DappCategoryId
	}
	return 0
}

func (x *CreateDappNavigationReq) GetShow() bool {
	if x != nil {
		return x.Show
	}
	return false
}

type CreateDappNavigationReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappNavigationReply) Reset() {
	*x = CreateDappNavigationReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappNavigationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappNavigationReply) ProtoMessage() {}

func (x *CreateDappNavigationReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappNavigationReply.ProtoReflect.Descriptor instead.
func (*CreateDappNavigationReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{19}
}

type DeleteDappNavigationReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappNavigationReq) Reset() {
	*x = DeleteDappNavigationReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappNavigationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappNavigationReq) ProtoMessage() {}

func (x *DeleteDappNavigationReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappNavigationReq.ProtoReflect.Descriptor instead.
func (*DeleteDappNavigationReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{20}
}

func (x *DeleteDappNavigationReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteDappNavigationReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappNavigationReply) Reset() {
	*x = DeleteDappNavigationReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappNavigationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappNavigationReply) ProtoMessage() {}

func (x *DeleteDappNavigationReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappNavigationReply.ProtoReflect.Descriptor instead.
func (*DeleteDappNavigationReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{21}
}

type ListDappNavigationReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappNavigationReq) Reset() {
	*x = ListDappNavigationReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappNavigationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappNavigationReq) ProtoMessage() {}

func (x *ListDappNavigationReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappNavigationReq.ProtoReflect.Descriptor instead.
func (*ListDappNavigationReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{22}
}

type ListDappNavigationReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DappNavigation      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappNavigationReply) Reset() {
	*x = ListDappNavigationReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappNavigationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappNavigationReply) ProtoMessage() {}

func (x *ListDappNavigationReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappNavigationReply.ProtoReflect.Descriptor instead.
func (*ListDappNavigationReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{23}
}

func (x *ListDappNavigationReply) GetList() []*DappNavigation {
	if x != nil {
		return x.List
	}
	return nil
}

type DappNavigation struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 是否推荐到首页导航
	Show bool `protobuf:"varint,2,opt,name=show,proto3" json:"show,omitempty"`
	// 分类多语言
	I18Ns []*DappCategoryI18N `protobuf:"bytes,3,rep,name=i18ns,proto3" json:"i18ns,omitempty"`
	// app数量
	AppCount int64 `protobuf:"varint,4,opt,name=app_count,json=appCount,proto3" json:"app_count,omitempty"`
	// 热门数量
	HotCount int64 `protobuf:"varint,5,opt,name=hot_count,json=hotCount,proto3" json:"hot_count,omitempty"`
	// 排序序号
	SortOrder     int64 `protobuf:"varint,6,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappNavigation) Reset() {
	*x = DappNavigation{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappNavigation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappNavigation) ProtoMessage() {}

func (x *DappNavigation) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappNavigation.ProtoReflect.Descriptor instead.
func (*DappNavigation) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{24}
}

func (x *DappNavigation) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DappNavigation) GetShow() bool {
	if x != nil {
		return x.Show
	}
	return false
}

func (x *DappNavigation) GetI18Ns() []*DappCategoryI18N {
	if x != nil {
		return x.I18Ns
	}
	return nil
}

func (x *DappNavigation) GetAppCount() int64 {
	if x != nil {
		return x.AppCount
	}
	return 0
}

func (x *DappNavigation) GetHotCount() int64 {
	if x != nil {
		return x.HotCount
	}
	return 0
}

func (x *DappNavigation) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

type CreateDappTopicReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 专题
	DataTopic     *DappTopic `protobuf:"bytes,1,opt,name=data_topic,json=dataTopic,proto3" json:"data_topic,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappTopicReq) Reset() {
	*x = CreateDappTopicReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappTopicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappTopicReq) ProtoMessage() {}

func (x *CreateDappTopicReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappTopicReq.ProtoReflect.Descriptor instead.
func (*CreateDappTopicReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{25}
}

func (x *CreateDappTopicReq) GetDataTopic() *DappTopic {
	if x != nil {
		return x.DataTopic
	}
	return nil
}

type CreateDappTopicReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappTopicReply) Reset() {
	*x = CreateDappTopicReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappTopicReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappTopicReply) ProtoMessage() {}

func (x *CreateDappTopicReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappTopicReply.ProtoReflect.Descriptor instead.
func (*CreateDappTopicReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{26}
}

type DappTopic struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否展示，默认true
	Show bool `protobuf:"varint,1,opt,name=show,proto3" json:"show,omitempty"`
	// 背景图url
	BackgroundUrl string `protobuf:"bytes,2,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	// 专题多语言
	I18Ns         []*DappTopicI18N `protobuf:"bytes,3,rep,name=i18ns,proto3" json:"i18ns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappTopic) Reset() {
	*x = DappTopic{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappTopic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappTopic) ProtoMessage() {}

func (x *DappTopic) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappTopic.ProtoReflect.Descriptor instead.
func (*DappTopic) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{27}
}

func (x *DappTopic) GetShow() bool {
	if x != nil {
		return x.Show
	}
	return false
}

func (x *DappTopic) GetBackgroundUrl() string {
	if x != nil {
		return x.BackgroundUrl
	}
	return ""
}

func (x *DappTopic) GetI18Ns() []*DappTopicI18N {
	if x != nil {
		return x.I18Ns
	}
	return nil
}

// DappTopicI18N dapp专题多语言
type DappTopicI18N struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 专题名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 简介
	Summary string `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	// 语言缩写 参考 https://zh.wikipedia.org/wiki/ISO_639-1
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	// 大标题
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// 上小标题
	TopTitle string `protobuf:"bytes,5,opt,name=top_title,json=topTitle,proto3" json:"top_title,omitempty"`
	// 下小标题
	BottomTitle   string `protobuf:"bytes,6,opt,name=bottom_title,json=bottomTitle,proto3" json:"bottom_title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappTopicI18N) Reset() {
	*x = DappTopicI18N{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappTopicI18N) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappTopicI18N) ProtoMessage() {}

func (x *DappTopicI18N) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappTopicI18N.ProtoReflect.Descriptor instead.
func (*DappTopicI18N) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{28}
}

func (x *DappTopicI18N) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DappTopicI18N) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *DappTopicI18N) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *DappTopicI18N) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DappTopicI18N) GetTopTitle() string {
	if x != nil {
		return x.TopTitle
	}
	return ""
}

func (x *DappTopicI18N) GetBottomTitle() string {
	if x != nil {
		return x.BottomTitle
	}
	return ""
}

type DappTopicRel struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	DappId uint64                 `protobuf:"varint,1,opt,name=dapp_id,json=dappId,proto3" json:"dapp_id,omitempty"`
	// 序号
	SortOrder     int64 `protobuf:"varint,2,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappTopicRel) Reset() {
	*x = DappTopicRel{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappTopicRel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappTopicRel) ProtoMessage() {}

func (x *DappTopicRel) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappTopicRel.ProtoReflect.Descriptor instead.
func (*DappTopicRel) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{29}
}

func (x *DappTopicRel) GetDappId() uint64 {
	if x != nil {
		return x.DappId
	}
	return 0
}

func (x *DappTopicRel) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

type ListDappCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappCategoryReq) Reset() {
	*x = ListDappCategoryReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappCategoryReq) ProtoMessage() {}

func (x *ListDappCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappCategoryReq.ProtoReflect.Descriptor instead.
func (*ListDappCategoryReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{30}
}

type ListDappCategoryReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DappCategory        `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappCategoryReply) Reset() {
	*x = ListDappCategoryReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappCategoryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappCategoryReply) ProtoMessage() {}

func (x *ListDappCategoryReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappCategoryReply.ProtoReflect.Descriptor instead.
func (*ListDappCategoryReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{31}
}

func (x *ListDappCategoryReply) GetList() []*DappCategory {
	if x != nil {
		return x.List
	}
	return nil
}

type UpdateDappCategoryReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 是否展示，默认true
	Show bool `protobuf:"varint,2,opt,name=show,proto3" json:"show,omitempty"`
	// 分类多语言
	I18Ns         []*DappCategoryI18N `protobuf:"bytes,3,rep,name=i18ns,proto3" json:"i18ns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDappCategoryReq) Reset() {
	*x = UpdateDappCategoryReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDappCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDappCategoryReq) ProtoMessage() {}

func (x *UpdateDappCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDappCategoryReq.ProtoReflect.Descriptor instead.
func (*UpdateDappCategoryReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{32}
}

func (x *UpdateDappCategoryReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDappCategoryReq) GetShow() bool {
	if x != nil {
		return x.Show
	}
	return false
}

func (x *UpdateDappCategoryReq) GetI18Ns() []*DappCategoryI18N {
	if x != nil {
		return x.I18Ns
	}
	return nil
}

type UpdateDappCategoryReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDappCategoryReply) Reset() {
	*x = UpdateDappCategoryReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDappCategoryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDappCategoryReply) ProtoMessage() {}

func (x *UpdateDappCategoryReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDappCategoryReply.ProtoReflect.Descriptor instead.
func (*UpdateDappCategoryReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{33}
}

type DeleteDappCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappCategoryReq) Reset() {
	*x = DeleteDappCategoryReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappCategoryReq) ProtoMessage() {}

func (x *DeleteDappCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappCategoryReq.ProtoReflect.Descriptor instead.
func (*DeleteDappCategoryReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{34}
}

func (x *DeleteDappCategoryReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteDappCategoryReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappCategoryReply) Reset() {
	*x = DeleteDappCategoryReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappCategoryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappCategoryReply) ProtoMessage() {}

func (x *DeleteDappCategoryReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappCategoryReply.ProtoReflect.Descriptor instead.
func (*DeleteDappCategoryReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{35}
}

type DappCategory struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 是否展示，默认true
	Show bool `protobuf:"varint,2,opt,name=show,proto3" json:"show,omitempty"`
	// 分类多语言
	I18Ns []*DappCategoryI18N `protobuf:"bytes,3,rep,name=i18ns,proto3" json:"i18ns,omitempty"`
	// app数量
	AppCount int64 `protobuf:"varint,4,opt,name=app_count,json=appCount,proto3" json:"app_count,omitempty"`
	// 热门数量
	HotCount int64 `protobuf:"varint,5,opt,name=hot_count,json=hotCount,proto3" json:"hot_count,omitempty"`
	// 是否推荐到首页导航
	Navigation    bool `protobuf:"varint,6,opt,name=navigation,proto3" json:"navigation,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappCategory) Reset() {
	*x = DappCategory{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappCategory) ProtoMessage() {}

func (x *DappCategory) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappCategory.ProtoReflect.Descriptor instead.
func (*DappCategory) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{36}
}

func (x *DappCategory) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DappCategory) GetShow() bool {
	if x != nil {
		return x.Show
	}
	return false
}

func (x *DappCategory) GetI18Ns() []*DappCategoryI18N {
	if x != nil {
		return x.I18Ns
	}
	return nil
}

func (x *DappCategory) GetAppCount() int64 {
	if x != nil {
		return x.AppCount
	}
	return 0
}

func (x *DappCategory) GetHotCount() int64 {
	if x != nil {
		return x.HotCount
	}
	return 0
}

func (x *DappCategory) GetNavigation() bool {
	if x != nil {
		return x.Navigation
	}
	return false
}

type DappCategoryRel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DappId        uint64                 `protobuf:"varint,1,opt,name=dapp_id,json=dappId,proto3" json:"dapp_id,omitempty"`
	SortOrder     int64                  `protobuf:"varint,2,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappCategoryRel) Reset() {
	*x = DappCategoryRel{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappCategoryRel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappCategoryRel) ProtoMessage() {}

func (x *DappCategoryRel) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappCategoryRel.ProtoReflect.Descriptor instead.
func (*DappCategoryRel) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{37}
}

func (x *DappCategoryRel) GetDappId() uint64 {
	if x != nil {
		return x.DappId
	}
	return 0
}

func (x *DappCategoryRel) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

type DappCategoryI18N struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分类名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 分类摘要
	Summary string `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	// 语言缩写 参考 https://zh.wikipedia.org/wiki/ISO_639-1
	Language      string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappCategoryI18N) Reset() {
	*x = DappCategoryI18N{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappCategoryI18N) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappCategoryI18N) ProtoMessage() {}

func (x *DappCategoryI18N) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappCategoryI18N.ProtoReflect.Descriptor instead.
func (*DappCategoryI18N) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{38}
}

func (x *DappCategoryI18N) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DappCategoryI18N) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *DappCategoryI18N) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type CreateDappCategoryReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否展示，默认传true
	Show bool `protobuf:"varint,1,opt,name=show,proto3" json:"show,omitempty"`
	// 分类多语言
	I18Ns         []*DappCategoryI18N `protobuf:"bytes,2,rep,name=i18ns,proto3" json:"i18ns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappCategoryReq) Reset() {
	*x = CreateDappCategoryReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappCategoryReq) ProtoMessage() {}

func (x *CreateDappCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappCategoryReq.ProtoReflect.Descriptor instead.
func (*CreateDappCategoryReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{39}
}

func (x *CreateDappCategoryReq) GetShow() bool {
	if x != nil {
		return x.Show
	}
	return false
}

func (x *CreateDappCategoryReq) GetI18Ns() []*DappCategoryI18N {
	if x != nil {
		return x.I18Ns
	}
	return nil
}

type CreateDappCategoryReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappCategoryReply) Reset() {
	*x = CreateDappCategoryReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappCategoryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappCategoryReply) ProtoMessage() {}

func (x *CreateDappCategoryReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappCategoryReply.ProtoReflect.Descriptor instead.
func (*CreateDappCategoryReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{40}
}

type ListDappReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappReq) Reset() {
	*x = ListDappReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappReq) ProtoMessage() {}

func (x *ListDappReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappReq.ProtoReflect.Descriptor instead.
func (*ListDappReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{41}
}

type ListDappReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Dapp                `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappReply) Reset() {
	*x = ListDappReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappReply) ProtoMessage() {}

func (x *ListDappReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappReply.ProtoReflect.Descriptor instead.
func (*ListDappReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{42}
}

func (x *ListDappReply) GetList() []*Dapp {
	if x != nil {
		return x.List
	}
	return nil
}

type UpdateDappReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// dapp主键id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// dapp
	Dapp          *Dapp `protobuf:"bytes,2,opt,name=dapp,proto3" json:"dapp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDappReq) Reset() {
	*x = UpdateDappReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDappReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDappReq) ProtoMessage() {}

func (x *UpdateDappReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDappReq.ProtoReflect.Descriptor instead.
func (*UpdateDappReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{43}
}

func (x *UpdateDappReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDappReq) GetDapp() *Dapp {
	if x != nil {
		return x.Dapp
	}
	return nil
}

type UpdateDappReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDappReply) Reset() {
	*x = UpdateDappReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDappReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDappReply) ProtoMessage() {}

func (x *UpdateDappReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDappReply.ProtoReflect.Descriptor instead.
func (*UpdateDappReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{44}
}

type DeleteDappReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappReq) Reset() {
	*x = DeleteDappReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappReq) ProtoMessage() {}

func (x *DeleteDappReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappReq.ProtoReflect.Descriptor instead.
func (*DeleteDappReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{45}
}

func (x *DeleteDappReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteDappReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappReply) Reset() {
	*x = DeleteDappReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappReply) ProtoMessage() {}

func (x *DeleteDappReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappReply.ProtoReflect.Descriptor instead.
func (*DeleteDappReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{46}
}

type CreateDappReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Dapp          *Dapp                  `protobuf:"bytes,1,opt,name=dapp,proto3" json:"dapp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappReq) Reset() {
	*x = CreateDappReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappReq) ProtoMessage() {}

func (x *CreateDappReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappReq.ProtoReflect.Descriptor instead.
func (*CreateDappReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{47}
}

func (x *CreateDappReq) GetDapp() *Dapp {
	if x != nil {
		return x.Dapp
	}
	return nil
}

type Dapp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 主键id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// dapp图标
	Logo string `protobuf:"bytes,2,opt,name=logo,proto3" json:"logo,omitempty"`
	// dapp内容链接
	Link string `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
	// 标签
	Tags string `protobuf:"bytes,4,opt,name=tags,proto3" json:"tags,omitempty"`
	// 是否热门
	Hot bool `protobuf:"varint,5,opt,name=hot,proto3" json:"hot,omitempty"`
	// 是否展示
	Show bool `protobuf:"varint,6,opt,name=show,proto3" json:"show,omitempty"`
	// 多语言
	I18Ns []*DappI18N `protobuf:"bytes,7,rep,name=i18ns,proto3" json:"i18ns,omitempty"`
	// 支持的网络
	Networks []*BlockchainNetwork `protobuf:"bytes,8,rep,name=networks,proto3" json:"networks,omitempty"`
	// 添加时间 Unix时间戳
	CreatedAt     int64 `protobuf:"varint,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Dapp) Reset() {
	*x = Dapp{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Dapp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dapp) ProtoMessage() {}

func (x *Dapp) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dapp.ProtoReflect.Descriptor instead.
func (*Dapp) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{48}
}

func (x *Dapp) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Dapp) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *Dapp) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *Dapp) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

func (x *Dapp) GetHot() bool {
	if x != nil {
		return x.Hot
	}
	return false
}

func (x *Dapp) GetShow() bool {
	if x != nil {
		return x.Show
	}
	return false
}

func (x *Dapp) GetI18Ns() []*DappI18N {
	if x != nil {
		return x.I18Ns
	}
	return nil
}

func (x *Dapp) GetNetworks() []*BlockchainNetwork {
	if x != nil {
		return x.Networks
	}
	return nil
}

func (x *Dapp) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type DappI18N struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// dapp名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 摘要
	Summary string `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	// 语言缩写 参考 https://zh.wikipedia.org/wiki/ISO_639-1
	Language      string `protobuf:"bytes,3,opt,name=language,proto3" json:"language,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappI18N) Reset() {
	*x = DappI18N{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappI18N) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappI18N) ProtoMessage() {}

func (x *DappI18N) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappI18N.ProtoReflect.Descriptor instead.
func (*DappI18N) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{49}
}

func (x *DappI18N) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DappI18N) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *DappI18N) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

type CreateDappReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappReply) Reset() {
	*x = CreateDappReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappReply) ProtoMessage() {}

func (x *CreateDappReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappReply.ProtoReflect.Descriptor instead.
func (*CreateDappReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{50}
}

// BlockchainNetwork 区块链网络
type BlockchainNetwork struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 矿币
	Symbol string `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 序号
	SortOrder int64 `protobuf:"varint,4,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// 网络图标
	BlockchainUrl string `protobuf:"bytes,5,opt,name=blockchain_url,json=blockchainUrl,proto3" json:"blockchain_url,omitempty"`
	// 矿币图标
	TokenUrl string `protobuf:"bytes,6,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	// 授权合约地址
	Address       string `protobuf:"bytes,7,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockchainNetwork) Reset() {
	*x = BlockchainNetwork{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockchainNetwork) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockchainNetwork) ProtoMessage() {}

func (x *BlockchainNetwork) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockchainNetwork.ProtoReflect.Descriptor instead.
func (*BlockchainNetwork) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{51}
}

func (x *BlockchainNetwork) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BlockchainNetwork) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BlockchainNetwork) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *BlockchainNetwork) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *BlockchainNetwork) GetBlockchainUrl() string {
	if x != nil {
		return x.BlockchainUrl
	}
	return ""
}

func (x *BlockchainNetwork) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *BlockchainNetwork) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

// UpdateDappTopic 更新DAPP专题请求
type UpdateDappTopicReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 是否展示，默认true
	Show bool `protobuf:"varint,2,opt,name=show,proto3" json:"show,omitempty"`
	// 背景图url
	BackgroundUrl string `protobuf:"bytes,3,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	// 专题多语言
	I18Ns         []*DappTopicI18N `protobuf:"bytes,4,rep,name=i18ns,proto3" json:"i18ns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDappTopicReq) Reset() {
	*x = UpdateDappTopicReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDappTopicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDappTopicReq) ProtoMessage() {}

func (x *UpdateDappTopicReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDappTopicReq.ProtoReflect.Descriptor instead.
func (*UpdateDappTopicReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{52}
}

func (x *UpdateDappTopicReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDappTopicReq) GetShow() bool {
	if x != nil {
		return x.Show
	}
	return false
}

func (x *UpdateDappTopicReq) GetBackgroundUrl() string {
	if x != nil {
		return x.BackgroundUrl
	}
	return ""
}

func (x *UpdateDappTopicReq) GetI18Ns() []*DappTopicI18N {
	if x != nil {
		return x.I18Ns
	}
	return nil
}

type UpdateDappTopicReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDappTopicReply) Reset() {
	*x = UpdateDappTopicReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDappTopicReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDappTopicReply) ProtoMessage() {}

func (x *UpdateDappTopicReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDappTopicReply.ProtoReflect.Descriptor instead.
func (*UpdateDappTopicReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{53}
}

// ListDappTopic 查询DAPP专题请求
type ListDappTopicReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappTopicReq) Reset() {
	*x = ListDappTopicReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappTopicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappTopicReq) ProtoMessage() {}

func (x *ListDappTopicReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappTopicReq.ProtoReflect.Descriptor instead.
func (*ListDappTopicReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{54}
}

type ListDappTopicReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DappTopicInfo       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappTopicReply) Reset() {
	*x = ListDappTopicReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappTopicReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappTopicReply) ProtoMessage() {}

func (x *ListDappTopicReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappTopicReply.ProtoReflect.Descriptor instead.
func (*ListDappTopicReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{55}
}

func (x *ListDappTopicReply) GetList() []*DappTopicInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// DappTopicInfo DAPP专题信息（用于查询返回）
type DappTopicInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 是否展示，默认true
	Show bool `protobuf:"varint,2,opt,name=show,proto3" json:"show,omitempty"`
	// 背景图url
	BackgroundUrl string `protobuf:"bytes,3,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	// 专题多语言
	I18Ns []*DappTopicI18N `protobuf:"bytes,4,rep,name=i18ns,proto3" json:"i18ns,omitempty"`
	// app数量
	AppCount int64 `protobuf:"varint,5,opt,name=app_count,json=appCount,proto3" json:"app_count,omitempty"`
	// 添加时间 Unix时间戳
	CreatedAt     int64 `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappTopicInfo) Reset() {
	*x = DappTopicInfo{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappTopicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappTopicInfo) ProtoMessage() {}

func (x *DappTopicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappTopicInfo.ProtoReflect.Descriptor instead.
func (*DappTopicInfo) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{56}
}

func (x *DappTopicInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DappTopicInfo) GetShow() bool {
	if x != nil {
		return x.Show
	}
	return false
}

func (x *DappTopicInfo) GetBackgroundUrl() string {
	if x != nil {
		return x.BackgroundUrl
	}
	return ""
}

func (x *DappTopicInfo) GetI18Ns() []*DappTopicI18N {
	if x != nil {
		return x.I18Ns
	}
	return nil
}

func (x *DappTopicInfo) GetAppCount() int64 {
	if x != nil {
		return x.AppCount
	}
	return 0
}

func (x *DappTopicInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

// CreateDappTopicRel 请求消息
type CreateDappTopicRelReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 专题id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// dapp id
	DappId uint64 `protobuf:"varint,2,opt,name=dapp_id,json=dappId,proto3" json:"dapp_id,omitempty"`
	// 序号
	SortOrder     int64 `protobuf:"varint,3,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappTopicRelReq) Reset() {
	*x = CreateDappTopicRelReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappTopicRelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappTopicRelReq) ProtoMessage() {}

func (x *CreateDappTopicRelReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappTopicRelReq.ProtoReflect.Descriptor instead.
func (*CreateDappTopicRelReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{57}
}

func (x *CreateDappTopicRelReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateDappTopicRelReq) GetDappId() uint64 {
	if x != nil {
		return x.DappId
	}
	return 0
}

func (x *CreateDappTopicRelReq) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

type CreateDappTopicRelReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDappTopicRelReply) Reset() {
	*x = CreateDappTopicRelReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDappTopicRelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDappTopicRelReply) ProtoMessage() {}

func (x *CreateDappTopicRelReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDappTopicRelReply.ProtoReflect.Descriptor instead.
func (*CreateDappTopicRelReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{58}
}

// DeleteDappTopicRel 请求消息
type DeleteDappTopicRelReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 专题id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// dapp id
	DappId        uint64 `protobuf:"varint,2,opt,name=dapp_id,json=dappId,proto3" json:"dapp_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappTopicRelReq) Reset() {
	*x = DeleteDappTopicRelReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappTopicRelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappTopicRelReq) ProtoMessage() {}

func (x *DeleteDappTopicRelReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappTopicRelReq.ProtoReflect.Descriptor instead.
func (*DeleteDappTopicRelReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{59}
}

func (x *DeleteDappTopicRelReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteDappTopicRelReq) GetDappId() uint64 {
	if x != nil {
		return x.DappId
	}
	return 0
}

type DeleteDappTopicRelReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDappTopicRelReply) Reset() {
	*x = DeleteDappTopicRelReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDappTopicRelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDappTopicRelReply) ProtoMessage() {}

func (x *DeleteDappTopicRelReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDappTopicRelReply.ProtoReflect.Descriptor instead.
func (*DeleteDappTopicRelReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{60}
}

// ListDappTopicRel 请求消息
type ListDappTopicRelReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 专题id
	Id            uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappTopicRelReq) Reset() {
	*x = ListDappTopicRelReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappTopicRelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappTopicRelReq) ProtoMessage() {}

func (x *ListDappTopicRelReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappTopicRelReq.ProtoReflect.Descriptor instead.
func (*ListDappTopicRelReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{61}
}

func (x *ListDappTopicRelReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListDappTopicRelReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Dapp                `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappTopicRelReply) Reset() {
	*x = ListDappTopicRelReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappTopicRelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappTopicRelReply) ProtoMessage() {}

func (x *ListDappTopicRelReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappTopicRelReply.ProtoReflect.Descriptor instead.
func (*ListDappTopicRelReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{62}
}

func (x *ListDappTopicRelReply) GetList() []*Dapp {
	if x != nil {
		return x.List
	}
	return nil
}

// UpdateDappNavigationSort 批量更新导航栏排序请求
type BatchUpdateDappNavigationReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DappNavigation      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchUpdateDappNavigationReq) Reset() {
	*x = BatchUpdateDappNavigationReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateDappNavigationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateDappNavigationReq) ProtoMessage() {}

func (x *BatchUpdateDappNavigationReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateDappNavigationReq.ProtoReflect.Descriptor instead.
func (*BatchUpdateDappNavigationReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{63}
}

func (x *BatchUpdateDappNavigationReq) GetList() []*DappNavigation {
	if x != nil {
		return x.List
	}
	return nil
}

type BatchUpdateDappNavigationReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchUpdateDappNavigationReply) Reset() {
	*x = BatchUpdateDappNavigationReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchUpdateDappNavigationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateDappNavigationReply) ProtoMessage() {}

func (x *BatchUpdateDappNavigationReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateDappNavigationReply.ProtoReflect.Descriptor instead.
func (*BatchUpdateDappNavigationReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{64}
}

// SortReq 排序请求
type SortReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前排序(来自列表的sort_order字段值)
	CurrentSort int64 `protobuf:"varint,1,opt,name=current_sort,json=currentSort,proto3" json:"current_sort,omitempty"`
	// 更新后排序
	TargetSort int64 `protobuf:"varint,2,opt,name=target_sort,json=targetSort,proto3" json:"target_sort,omitempty"`
	// 记录ID
	Id            uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SortReq) Reset() {
	*x = SortReq{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SortReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortReq) ProtoMessage() {}

func (x *SortReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortReq.ProtoReflect.Descriptor instead.
func (*SortReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{65}
}

func (x *SortReq) GetCurrentSort() int64 {
	if x != nil {
		return x.CurrentSort
	}
	return 0
}

func (x *SortReq) GetTargetSort() int64 {
	if x != nil {
		return x.TargetSort
	}
	return 0
}

func (x *SortReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type SortReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SortReply) Reset() {
	*x = SortReply{}
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SortReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortReply) ProtoMessage() {}

func (x *SortReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_dapp_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortReply.ProtoReflect.Descriptor instead.
func (*SortReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_dapp_proto_rawDescGZIP(), []int{66}
}

var File_api_walletadmin_v1_dapp_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_dapp_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/walletadmin/v1/dapp.proto\x12\x12api.walletadmin.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\"-\n" +
	"\x12DeleteDappTopicReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"\x16\n" +
	"\x14DeleteDappTopicReply\"1\n" +
	"\x16ListDappCategoryRelReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"H\n" +
	"\x18ListDappCategoryRelReply\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.api.walletadmin.v1.DappR\x04list\"U\n" +
	"\x18DeleteDappCategoryRelReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12 \n" +
	"\adapp_id\x18\x02 \x01(\x04B\a\xbaH\x042\x02 \x00R\x06dappId\"\x1c\n" +
	"\x1aDeleteDappCategoryRelReply\"U\n" +
	"\x18CreateDappCategoryRelReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12 \n" +
	"\adapp_id\x18\x02 \x01(\x04B\a\xbaH\x042\x02 \x00R\x06dappId\"\x1c\n" +
	"\x1aCreateDappCategoryRelReply\"N\n" +
	"\x12CreateDappIndexReq\x12\x1d\n" +
	"\n" +
	"owner_type\x18\x01 \x01(\tR\townerType\x12\x19\n" +
	"\bowner_id\x18\x02 \x01(\x04R\aownerId\"\x16\n" +
	"\x14CreateDappIndexReply\"\x12\n" +
	"\x10ListDappIndexReq\"G\n" +
	"\x12ListDappIndexReply\x121\n" +
	"\x04list\x18\x01 \x03(\v2\x1d.api.walletadmin.v1.DappIndexR\x04list\"-\n" +
	"\x12DeleteDappIndexReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"\x16\n" +
	"\x14DeleteDappIndexReply\"\x9b\x01\n" +
	"\tDappIndex\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.api.walletadmin.v1.DappR\x04list\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x04R\x02id\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x03 \x01(\x03R\tsortOrder\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"owner_type\x18\x05 \x01(\tR\townerType\"J\n" +
	"\x10UpdatedDappIndex\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x02 \x01(\x04R\tsortOrder\"S\n" +
	"\x17BatchUpdateDappIndexReq\x128\n" +
	"\x04list\x18\x01 \x03(\v2$.api.walletadmin.v1.UpdatedDappIndexR\x04list\"\x1b\n" +
	"\x19BatchUpdateDappIndexReply\"`\n" +
	"\x17CreateDappNavigationReq\x121\n" +
	"\x10dapp_category_id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x0edappCategoryId\x12\x12\n" +
	"\x04show\x18\x02 \x01(\bR\x04show\"\x1b\n" +
	"\x19CreateDappNavigationReply\"2\n" +
	"\x17DeleteDappNavigationReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"\x1b\n" +
	"\x19DeleteDappNavigationReply\"\x17\n" +
	"\x15ListDappNavigationReq\"Q\n" +
	"\x17ListDappNavigationReply\x126\n" +
	"\x04list\x18\x01 \x03(\v2\".api.walletadmin.v1.DappNavigationR\x04list\"\xc9\x01\n" +
	"\x0eDappNavigation\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04show\x18\x02 \x01(\bR\x04show\x12:\n" +
	"\x05i18ns\x18\x03 \x03(\v2$.api.walletadmin.v1.DappCategoryI18NR\x05i18ns\x12\x1b\n" +
	"\tapp_count\x18\x04 \x01(\x03R\bappCount\x12\x1b\n" +
	"\thot_count\x18\x05 \x01(\x03R\bhotCount\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x06 \x01(\x03R\tsortOrder\"R\n" +
	"\x12CreateDappTopicReq\x12<\n" +
	"\n" +
	"data_topic\x18\x01 \x01(\v2\x1d.api.walletadmin.v1.DappTopicR\tdataTopic\"\x16\n" +
	"\x14CreateDappTopicReply\"\x92\x01\n" +
	"\tDappTopic\x12\x12\n" +
	"\x04show\x18\x01 \x01(\bR\x04show\x12.\n" +
	"\x0ebackground_url\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\rbackgroundUrl\x12A\n" +
	"\x05i18ns\x18\x03 \x03(\v2!.api.walletadmin.v1.DappTopicI18NB\b\xbaH\x05\x92\x01\x02\b\x01R\x05i18ns\"\xca\x01\n" +
	"\rDappTopicI18N\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12!\n" +
	"\asummary\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\asummary\x12#\n" +
	"\blanguage\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\blanguage\x12\x1d\n" +
	"\x05title\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x05title\x12\x1b\n" +
	"\ttop_title\x18\x05 \x01(\tR\btopTitle\x12!\n" +
	"\fbottom_title\x18\x06 \x01(\tR\vbottomTitle\"O\n" +
	"\fDappTopicRel\x12 \n" +
	"\adapp_id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x06dappId\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x02 \x01(\x03R\tsortOrder\"\x15\n" +
	"\x13ListDappCategoryReq\"M\n" +
	"\x15ListDappCategoryReply\x124\n" +
	"\x04list\x18\x01 \x03(\v2 .api.walletadmin.v1.DappCategoryR\x04list\"\x8a\x01\n" +
	"\x15UpdateDappCategoryReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12\x12\n" +
	"\x04show\x18\x02 \x01(\bR\x04show\x12D\n" +
	"\x05i18ns\x18\x03 \x03(\v2$.api.walletadmin.v1.DappCategoryI18NB\b\xbaH\x05\x92\x01\x02\b\x01R\x05i18ns\"\x19\n" +
	"\x17UpdateDappCategoryReply\"0\n" +
	"\x15DeleteDappCategoryReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"\x19\n" +
	"\x17DeleteDappCategoryReply\"\xc8\x01\n" +
	"\fDappCategory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04show\x18\x02 \x01(\bR\x04show\x12:\n" +
	"\x05i18ns\x18\x03 \x03(\v2$.api.walletadmin.v1.DappCategoryI18NR\x05i18ns\x12\x1b\n" +
	"\tapp_count\x18\x04 \x01(\x03R\bappCount\x12\x1b\n" +
	"\thot_count\x18\x05 \x01(\x03R\bhotCount\x12\x1e\n" +
	"\n" +
	"navigation\x18\x06 \x01(\bR\n" +
	"navigation\"R\n" +
	"\x0fDappCategoryRel\x12 \n" +
	"\adapp_id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x06dappId\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x02 \x01(\x03R\tsortOrder\"w\n" +
	"\x10DappCategoryI18N\x12\x1b\n" +
	"\x04name\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x04name\x12!\n" +
	"\asummary\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\asummary\x12#\n" +
	"\blanguage\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\blanguage\"q\n" +
	"\x15CreateDappCategoryReq\x12\x12\n" +
	"\x04show\x18\x01 \x01(\bR\x04show\x12D\n" +
	"\x05i18ns\x18\x02 \x03(\v2$.api.walletadmin.v1.DappCategoryI18NB\b\xbaH\x05\x92\x01\x02\b\x01R\x05i18ns\"\x19\n" +
	"\x17CreateDappCategoryReply\"\r\n" +
	"\vListDappReq\"=\n" +
	"\rListDappReply\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.api.walletadmin.v1.DappR\x04list\"^\n" +
	"\rUpdateDappReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x124\n" +
	"\x04dapp\x18\x02 \x01(\v2\x18.api.walletadmin.v1.DappB\x06\xbaH\x03\xc8\x01\x01R\x04dapp\"\x11\n" +
	"\x0fUpdateDappReply\"(\n" +
	"\rDeleteDappReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"\x11\n" +
	"\x0fDeleteDappReply\"E\n" +
	"\rCreateDappReq\x124\n" +
	"\x04dapp\x18\x01 \x01(\v2\x18.api.walletadmin.v1.DappB\x06\xbaH\x03\xc8\x01\x01R\x04dapp\"\xb6\x02\n" +
	"\x04Dapp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1c\n" +
	"\x04logo\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x88\x01\x01R\x04logo\x12\x1c\n" +
	"\x04link\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x88\x01\x01R\x04link\x12\x12\n" +
	"\x04tags\x18\x04 \x01(\tR\x04tags\x12\x10\n" +
	"\x03hot\x18\x05 \x01(\bR\x03hot\x12\x12\n" +
	"\x04show\x18\x06 \x01(\bR\x04show\x12<\n" +
	"\x05i18ns\x18\a \x03(\v2\x1c.api.walletadmin.v1.DappI18NB\b\xbaH\x05\x92\x01\x02\b\x01R\x05i18ns\x12K\n" +
	"\bnetworks\x18\b \x03(\v2%.api.walletadmin.v1.BlockchainNetworkB\b\xbaH\x05\x92\x01\x02\b\x01R\bnetworks\x12\x1d\n" +
	"\n" +
	"created_at\x18\t \x01(\x03R\tcreatedAt\"o\n" +
	"\bDappI18N\x12\x1b\n" +
	"\x04name\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x04name\x12!\n" +
	"\asummary\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\asummary\x12#\n" +
	"\blanguage\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\blanguage\"\x11\n" +
	"\x0fCreateDappReply\"\xcc\x01\n" +
	"\x11BlockchainNetwork\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x03 \x01(\tR\x06symbol\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x04 \x01(\x03R\tsortOrder\x12%\n" +
	"\x0eblockchain_url\x18\x05 \x01(\tR\rblockchainUrl\x12\x1b\n" +
	"\ttoken_url\x18\x06 \x01(\tR\btokenUrl\x12\x18\n" +
	"\aaddress\x18\a \x01(\tR\aaddress\"\xb4\x01\n" +
	"\x12UpdateDappTopicReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12\x12\n" +
	"\x04show\x18\x02 \x01(\bR\x04show\x12.\n" +
	"\x0ebackground_url\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\rbackgroundUrl\x12A\n" +
	"\x05i18ns\x18\x04 \x03(\v2!.api.walletadmin.v1.DappTopicI18NB\b\xbaH\x05\x92\x01\x02\b\x01R\x05i18ns\"\x16\n" +
	"\x14UpdateDappTopicReply\"\x12\n" +
	"\x10ListDappTopicReq\"K\n" +
	"\x12ListDappTopicReply\x125\n" +
	"\x04list\x18\x01 \x03(\v2!.api.walletadmin.v1.DappTopicInfoR\x04list\"\xcf\x01\n" +
	"\rDappTopicInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04show\x18\x02 \x01(\bR\x04show\x12%\n" +
	"\x0ebackground_url\x18\x03 \x01(\tR\rbackgroundUrl\x127\n" +
	"\x05i18ns\x18\x04 \x03(\v2!.api.walletadmin.v1.DappTopicI18NR\x05i18ns\x12\x1b\n" +
	"\tapp_count\x18\x05 \x01(\x03R\bappCount\x12\x1d\n" +
	"\n" +
	"created_at\x18\x06 \x01(\x03R\tcreatedAt\"q\n" +
	"\x15CreateDappTopicRelReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12 \n" +
	"\adapp_id\x18\x02 \x01(\x04B\a\xbaH\x042\x02 \x00R\x06dappId\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x03 \x01(\x03R\tsortOrder\"\x19\n" +
	"\x17CreateDappTopicRelReply\"R\n" +
	"\x15DeleteDappTopicRelReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12 \n" +
	"\adapp_id\x18\x02 \x01(\x04B\a\xbaH\x042\x02 \x00R\x06dappId\"\x19\n" +
	"\x17DeleteDappTopicRelReply\".\n" +
	"\x13ListDappTopicRelReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"E\n" +
	"\x15ListDappTopicRelReply\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.api.walletadmin.v1.DappR\x04list\"`\n" +
	"\x1cBatchUpdateDappNavigationReq\x12@\n" +
	"\x04list\x18\x01 \x03(\v2\".api.walletadmin.v1.DappNavigationB\b\xbaH\x05\x92\x01\x02\b\x02R\x04list\" \n" +
	"\x1eBatchUpdateDappNavigationReply\"]\n" +
	"\aSortReq\x12!\n" +
	"\fcurrent_sort\x18\x01 \x01(\x03R\vcurrentSort\x12\x1f\n" +
	"\vtarget_sort\x18\x02 \x01(\x03R\n" +
	"targetSort\x12\x0e\n" +
	"\x02id\x18\x03 \x01(\x04R\x02id\"\v\n" +
	"\tSortReply2\x8a\x1f\n" +
	"\fAdminDappSrv\x12o\n" +
	"\n" +
	"CreateDapp\x12!.api.walletadmin.v1.CreateDappReq\x1a#.api.walletadmin.v1.CreateDappReply\"\x19\x82\xd3\xe4\x93\x02\x13:\x01*\"\x0e/admin/v1/dapp\x12q\n" +
	"\n" +
	"DeleteDapp\x12!.api.walletadmin.v1.DeleteDappReq\x1a#.api.walletadmin.v1.DeleteDappReply\"\x1b\x82\xd3\xe4\x93\x02\x15*\x13/admin/v1/dapp/{id}\x12t\n" +
	"\n" +
	"UpdateDapp\x12!.api.walletadmin.v1.UpdateDappReq\x1a#.api.walletadmin.v1.UpdateDappReply\"\x1e\x82\xd3\xe4\x93\x02\x18:\x01*\x1a\x13/admin/v1/dapp/{id}\x12f\n" +
	"\bListDapp\x12\x1f.api.walletadmin.v1.ListDappReq\x1a!.api.walletadmin.v1.ListDappReply\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/admin/v1/dapp\x12\x90\x01\n" +
	"\x12CreateDappCategory\x12).api.walletadmin.v1.CreateDappCategoryReq\x1a+.api.walletadmin.v1.CreateDappCategoryReply\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/admin/v1/dapp/category\x12\x95\x01\n" +
	"\x12UpdateDappCategory\x12).api.walletadmin.v1.UpdateDappCategoryReq\x1a+.api.walletadmin.v1.UpdateDappCategoryReply\"'\x82\xd3\xe4\x93\x02!:\x01*\x1a\x1c/admin/v1/dapp/category/{id}\x12\x87\x01\n" +
	"\x10ListDappCategory\x12'.api.walletadmin.v1.ListDappCategoryReq\x1a).api.walletadmin.v1.ListDappCategoryReply\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/admin/v1/dapp/category\x12\x92\x01\n" +
	"\x12DeleteDappCategory\x12).api.walletadmin.v1.DeleteDappCategoryReq\x1a+.api.walletadmin.v1.DeleteDappCategoryReply\"$\x82\xd3\xe4\x93\x02\x1e*\x1c/admin/v1/dapp/category/{id}\x12\xa2\x01\n" +
	"\x15CreateDappCategoryRel\x12,.api.walletadmin.v1.CreateDappCategoryRelReq\x1a..api.walletadmin.v1.CreateDappCategoryRelReply\"+\x82\xd3\xe4\x93\x02%:\x01*\" /admin/v1/dapp/category/{id}/rel\x12\x9f\x01\n" +
	"\x15DeleteDappCategoryRel\x12,.api.walletadmin.v1.DeleteDappCategoryRelReq\x1a..api.walletadmin.v1.DeleteDappCategoryRelReply\"(\x82\xd3\xe4\x93\x02\"* /admin/v1/dapp/category/{id}/rel\x12\x99\x01\n" +
	"\x13ListDappCategoryRel\x12*.api.walletadmin.v1.ListDappCategoryRelReq\x1a,.api.walletadmin.v1.ListDappCategoryRelReply\"(\x82\xd3\xe4\x93\x02\"\x12 /admin/v1/dapp/category/{id}/rel\x12\x84\x01\n" +
	"\x0fCreateDappTopic\x12&.api.walletadmin.v1.CreateDappTopicReq\x1a(.api.walletadmin.v1.CreateDappTopicReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/admin/v1/dapp/topic\x12\x86\x01\n" +
	"\x0fDeleteDappTopic\x12&.api.walletadmin.v1.DeleteDappTopicReq\x1a(.api.walletadmin.v1.DeleteDappTopicReply\"!\x82\xd3\xe4\x93\x02\x1b*\x19/admin/v1/dapp/topic/{id}\x12\x89\x01\n" +
	"\x0fUpdateDappTopic\x12&.api.walletadmin.v1.UpdateDappTopicReq\x1a(.api.walletadmin.v1.UpdateDappTopicReply\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\x1a\x19/admin/v1/dapp/topic/{id}\x12{\n" +
	"\rListDappTopic\x12$.api.walletadmin.v1.ListDappTopicReq\x1a&.api.walletadmin.v1.ListDappTopicReply\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/admin/v1/dapp/topic\x12\xa1\x01\n" +
	"\x14CreateDappNavigation\x12+.api.walletadmin.v1.CreateDappNavigationReq\x1a-.api.walletadmin.v1.CreateDappNavigationReply\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/admin/v1/dapp/category/navigation\x12\xa3\x01\n" +
	"\x14DeleteDappNavigation\x12+.api.walletadmin.v1.DeleteDappNavigationReq\x1a-.api.walletadmin.v1.DeleteDappNavigationReply\"/\x82\xd3\xe4\x93\x02)*'/admin/v1/dapp/category/navigation/{id}\x12\x98\x01\n" +
	"\x12ListDappNavigation\x12).api.walletadmin.v1.ListDappNavigationReq\x1a+.api.walletadmin.v1.ListDappNavigationReply\"*\x82\xd3\xe4\x93\x02$\x12\"/admin/v1/dapp/category/navigation\x12\xb0\x01\n" +
	"\x19BatchUpdateDappNavigation\x120.api.walletadmin.v1.BatchUpdateDappNavigationReq\x1a2.api.walletadmin.v1.BatchUpdateDappNavigationReply\"-\x82\xd3\xe4\x93\x02':\x01*\x1a\"/admin/v1/dapp/category/navigation\x12\x84\x01\n" +
	"\x12SortDappNavigation\x12\x1b.api.walletadmin.v1.SortReq\x1a\x1d.api.walletadmin.v1.SortReply\"2\x82\xd3\xe4\x93\x02,:\x01*\x1a'/admin/v1/dapp/category/navigation/sort\x12\x84\x01\n" +
	"\x0fCreateDappIndex\x12&.api.walletadmin.v1.CreateDappIndexReq\x1a(.api.walletadmin.v1.CreateDappIndexReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/admin/v1/dapp/index\x12{\n" +
	"\rListDappIndex\x12$.api.walletadmin.v1.ListDappIndexReq\x1a&.api.walletadmin.v1.ListDappIndexReply\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/admin/v1/dapp/index\x12\x86\x01\n" +
	"\x0fDeleteDappIndex\x12&.api.walletadmin.v1.DeleteDappIndexReq\x1a(.api.walletadmin.v1.DeleteDappIndexReply\"!\x82\xd3\xe4\x93\x02\x1b*\x19/admin/v1/dapp/index/{id}\x12\x93\x01\n" +
	"\x14BatchUpdateDappIndex\x12+.api.walletadmin.v1.BatchUpdateDappIndexReq\x1a-.api.walletadmin.v1.BatchUpdateDappIndexReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\x1a\x14/admin/v1/dapp/index\x12q\n" +
	"\rSortDappIndex\x12\x1b.api.walletadmin.v1.SortReq\x1a\x1d.api.walletadmin.v1.SortReply\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\x1a\x19/admin/v1/dapp/index/sort\x12\x96\x01\n" +
	"\x12CreateDappTopicRel\x12).api.walletadmin.v1.CreateDappTopicRelReq\x1a+.api.walletadmin.v1.CreateDappTopicRelReply\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/admin/v1/dapp/topic/{id}/rel\x12\x93\x01\n" +
	"\x12DeleteDappTopicRel\x12).api.walletadmin.v1.DeleteDappTopicRelReq\x1a+.api.walletadmin.v1.DeleteDappTopicRelReply\"%\x82\xd3\xe4\x93\x02\x1f*\x1d/admin/v1/dapp/topic/{id}/rel\x12\x8d\x01\n" +
	"\x10ListDappTopicRel\x12'.api.walletadmin.v1.ListDappTopicRelReq\x1a).api.walletadmin.v1.ListDappTopicRelReply\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/admin/v1/dapp/topic/{id}/relB\xaf\x01\n" +
	"\x16com.api.walletadmin.v1B\tDappProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_dapp_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_dapp_proto_rawDescData []byte
)

func file_api_walletadmin_v1_dapp_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_dapp_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_dapp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_dapp_proto_rawDesc), len(file_api_walletadmin_v1_dapp_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_dapp_proto_rawDescData
}

var file_api_walletadmin_v1_dapp_proto_msgTypes = make([]protoimpl.MessageInfo, 67)
var file_api_walletadmin_v1_dapp_proto_goTypes = []any{
	(*DeleteDappTopicReq)(nil),             // 0: api.walletadmin.v1.DeleteDappTopicReq
	(*DeleteDappTopicReply)(nil),           // 1: api.walletadmin.v1.DeleteDappTopicReply
	(*ListDappCategoryRelReq)(nil),         // 2: api.walletadmin.v1.ListDappCategoryRelReq
	(*ListDappCategoryRelReply)(nil),       // 3: api.walletadmin.v1.ListDappCategoryRelReply
	(*DeleteDappCategoryRelReq)(nil),       // 4: api.walletadmin.v1.DeleteDappCategoryRelReq
	(*DeleteDappCategoryRelReply)(nil),     // 5: api.walletadmin.v1.DeleteDappCategoryRelReply
	(*CreateDappCategoryRelReq)(nil),       // 6: api.walletadmin.v1.CreateDappCategoryRelReq
	(*CreateDappCategoryRelReply)(nil),     // 7: api.walletadmin.v1.CreateDappCategoryRelReply
	(*CreateDappIndexReq)(nil),             // 8: api.walletadmin.v1.CreateDappIndexReq
	(*CreateDappIndexReply)(nil),           // 9: api.walletadmin.v1.CreateDappIndexReply
	(*ListDappIndexReq)(nil),               // 10: api.walletadmin.v1.ListDappIndexReq
	(*ListDappIndexReply)(nil),             // 11: api.walletadmin.v1.ListDappIndexReply
	(*DeleteDappIndexReq)(nil),             // 12: api.walletadmin.v1.DeleteDappIndexReq
	(*DeleteDappIndexReply)(nil),           // 13: api.walletadmin.v1.DeleteDappIndexReply
	(*DappIndex)(nil),                      // 14: api.walletadmin.v1.DappIndex
	(*UpdatedDappIndex)(nil),               // 15: api.walletadmin.v1.UpdatedDappIndex
	(*BatchUpdateDappIndexReq)(nil),        // 16: api.walletadmin.v1.BatchUpdateDappIndexReq
	(*BatchUpdateDappIndexReply)(nil),      // 17: api.walletadmin.v1.BatchUpdateDappIndexReply
	(*CreateDappNavigationReq)(nil),        // 18: api.walletadmin.v1.CreateDappNavigationReq
	(*CreateDappNavigationReply)(nil),      // 19: api.walletadmin.v1.CreateDappNavigationReply
	(*DeleteDappNavigationReq)(nil),        // 20: api.walletadmin.v1.DeleteDappNavigationReq
	(*DeleteDappNavigationReply)(nil),      // 21: api.walletadmin.v1.DeleteDappNavigationReply
	(*ListDappNavigationReq)(nil),          // 22: api.walletadmin.v1.ListDappNavigationReq
	(*ListDappNavigationReply)(nil),        // 23: api.walletadmin.v1.ListDappNavigationReply
	(*DappNavigation)(nil),                 // 24: api.walletadmin.v1.DappNavigation
	(*CreateDappTopicReq)(nil),             // 25: api.walletadmin.v1.CreateDappTopicReq
	(*CreateDappTopicReply)(nil),           // 26: api.walletadmin.v1.CreateDappTopicReply
	(*DappTopic)(nil),                      // 27: api.walletadmin.v1.DappTopic
	(*DappTopicI18N)(nil),                  // 28: api.walletadmin.v1.DappTopicI18N
	(*DappTopicRel)(nil),                   // 29: api.walletadmin.v1.DappTopicRel
	(*ListDappCategoryReq)(nil),            // 30: api.walletadmin.v1.ListDappCategoryReq
	(*ListDappCategoryReply)(nil),          // 31: api.walletadmin.v1.ListDappCategoryReply
	(*UpdateDappCategoryReq)(nil),          // 32: api.walletadmin.v1.UpdateDappCategoryReq
	(*UpdateDappCategoryReply)(nil),        // 33: api.walletadmin.v1.UpdateDappCategoryReply
	(*DeleteDappCategoryReq)(nil),          // 34: api.walletadmin.v1.DeleteDappCategoryReq
	(*DeleteDappCategoryReply)(nil),        // 35: api.walletadmin.v1.DeleteDappCategoryReply
	(*DappCategory)(nil),                   // 36: api.walletadmin.v1.DappCategory
	(*DappCategoryRel)(nil),                // 37: api.walletadmin.v1.DappCategoryRel
	(*DappCategoryI18N)(nil),               // 38: api.walletadmin.v1.DappCategoryI18N
	(*CreateDappCategoryReq)(nil),          // 39: api.walletadmin.v1.CreateDappCategoryReq
	(*CreateDappCategoryReply)(nil),        // 40: api.walletadmin.v1.CreateDappCategoryReply
	(*ListDappReq)(nil),                    // 41: api.walletadmin.v1.ListDappReq
	(*ListDappReply)(nil),                  // 42: api.walletadmin.v1.ListDappReply
	(*UpdateDappReq)(nil),                  // 43: api.walletadmin.v1.UpdateDappReq
	(*UpdateDappReply)(nil),                // 44: api.walletadmin.v1.UpdateDappReply
	(*DeleteDappReq)(nil),                  // 45: api.walletadmin.v1.DeleteDappReq
	(*DeleteDappReply)(nil),                // 46: api.walletadmin.v1.DeleteDappReply
	(*CreateDappReq)(nil),                  // 47: api.walletadmin.v1.CreateDappReq
	(*Dapp)(nil),                           // 48: api.walletadmin.v1.Dapp
	(*DappI18N)(nil),                       // 49: api.walletadmin.v1.DappI18N
	(*CreateDappReply)(nil),                // 50: api.walletadmin.v1.CreateDappReply
	(*BlockchainNetwork)(nil),              // 51: api.walletadmin.v1.BlockchainNetwork
	(*UpdateDappTopicReq)(nil),             // 52: api.walletadmin.v1.UpdateDappTopicReq
	(*UpdateDappTopicReply)(nil),           // 53: api.walletadmin.v1.UpdateDappTopicReply
	(*ListDappTopicReq)(nil),               // 54: api.walletadmin.v1.ListDappTopicReq
	(*ListDappTopicReply)(nil),             // 55: api.walletadmin.v1.ListDappTopicReply
	(*DappTopicInfo)(nil),                  // 56: api.walletadmin.v1.DappTopicInfo
	(*CreateDappTopicRelReq)(nil),          // 57: api.walletadmin.v1.CreateDappTopicRelReq
	(*CreateDappTopicRelReply)(nil),        // 58: api.walletadmin.v1.CreateDappTopicRelReply
	(*DeleteDappTopicRelReq)(nil),          // 59: api.walletadmin.v1.DeleteDappTopicRelReq
	(*DeleteDappTopicRelReply)(nil),        // 60: api.walletadmin.v1.DeleteDappTopicRelReply
	(*ListDappTopicRelReq)(nil),            // 61: api.walletadmin.v1.ListDappTopicRelReq
	(*ListDappTopicRelReply)(nil),          // 62: api.walletadmin.v1.ListDappTopicRelReply
	(*BatchUpdateDappNavigationReq)(nil),   // 63: api.walletadmin.v1.BatchUpdateDappNavigationReq
	(*BatchUpdateDappNavigationReply)(nil), // 64: api.walletadmin.v1.BatchUpdateDappNavigationReply
	(*SortReq)(nil),                        // 65: api.walletadmin.v1.SortReq
	(*SortReply)(nil),                      // 66: api.walletadmin.v1.SortReply
}
var file_api_walletadmin_v1_dapp_proto_depIdxs = []int32{
	48, // 0: api.walletadmin.v1.ListDappCategoryRelReply.list:type_name -> api.walletadmin.v1.Dapp
	14, // 1: api.walletadmin.v1.ListDappIndexReply.list:type_name -> api.walletadmin.v1.DappIndex
	48, // 2: api.walletadmin.v1.DappIndex.list:type_name -> api.walletadmin.v1.Dapp
	15, // 3: api.walletadmin.v1.BatchUpdateDappIndexReq.list:type_name -> api.walletadmin.v1.UpdatedDappIndex
	24, // 4: api.walletadmin.v1.ListDappNavigationReply.list:type_name -> api.walletadmin.v1.DappNavigation
	38, // 5: api.walletadmin.v1.DappNavigation.i18ns:type_name -> api.walletadmin.v1.DappCategoryI18N
	27, // 6: api.walletadmin.v1.CreateDappTopicReq.data_topic:type_name -> api.walletadmin.v1.DappTopic
	28, // 7: api.walletadmin.v1.DappTopic.i18ns:type_name -> api.walletadmin.v1.DappTopicI18N
	36, // 8: api.walletadmin.v1.ListDappCategoryReply.list:type_name -> api.walletadmin.v1.DappCategory
	38, // 9: api.walletadmin.v1.UpdateDappCategoryReq.i18ns:type_name -> api.walletadmin.v1.DappCategoryI18N
	38, // 10: api.walletadmin.v1.DappCategory.i18ns:type_name -> api.walletadmin.v1.DappCategoryI18N
	38, // 11: api.walletadmin.v1.CreateDappCategoryReq.i18ns:type_name -> api.walletadmin.v1.DappCategoryI18N
	48, // 12: api.walletadmin.v1.ListDappReply.list:type_name -> api.walletadmin.v1.Dapp
	48, // 13: api.walletadmin.v1.UpdateDappReq.dapp:type_name -> api.walletadmin.v1.Dapp
	48, // 14: api.walletadmin.v1.CreateDappReq.dapp:type_name -> api.walletadmin.v1.Dapp
	49, // 15: api.walletadmin.v1.Dapp.i18ns:type_name -> api.walletadmin.v1.DappI18N
	51, // 16: api.walletadmin.v1.Dapp.networks:type_name -> api.walletadmin.v1.BlockchainNetwork
	28, // 17: api.walletadmin.v1.UpdateDappTopicReq.i18ns:type_name -> api.walletadmin.v1.DappTopicI18N
	56, // 18: api.walletadmin.v1.ListDappTopicReply.list:type_name -> api.walletadmin.v1.DappTopicInfo
	28, // 19: api.walletadmin.v1.DappTopicInfo.i18ns:type_name -> api.walletadmin.v1.DappTopicI18N
	48, // 20: api.walletadmin.v1.ListDappTopicRelReply.list:type_name -> api.walletadmin.v1.Dapp
	24, // 21: api.walletadmin.v1.BatchUpdateDappNavigationReq.list:type_name -> api.walletadmin.v1.DappNavigation
	47, // 22: api.walletadmin.v1.AdminDappSrv.CreateDapp:input_type -> api.walletadmin.v1.CreateDappReq
	45, // 23: api.walletadmin.v1.AdminDappSrv.DeleteDapp:input_type -> api.walletadmin.v1.DeleteDappReq
	43, // 24: api.walletadmin.v1.AdminDappSrv.UpdateDapp:input_type -> api.walletadmin.v1.UpdateDappReq
	41, // 25: api.walletadmin.v1.AdminDappSrv.ListDapp:input_type -> api.walletadmin.v1.ListDappReq
	39, // 26: api.walletadmin.v1.AdminDappSrv.CreateDappCategory:input_type -> api.walletadmin.v1.CreateDappCategoryReq
	32, // 27: api.walletadmin.v1.AdminDappSrv.UpdateDappCategory:input_type -> api.walletadmin.v1.UpdateDappCategoryReq
	30, // 28: api.walletadmin.v1.AdminDappSrv.ListDappCategory:input_type -> api.walletadmin.v1.ListDappCategoryReq
	34, // 29: api.walletadmin.v1.AdminDappSrv.DeleteDappCategory:input_type -> api.walletadmin.v1.DeleteDappCategoryReq
	6,  // 30: api.walletadmin.v1.AdminDappSrv.CreateDappCategoryRel:input_type -> api.walletadmin.v1.CreateDappCategoryRelReq
	4,  // 31: api.walletadmin.v1.AdminDappSrv.DeleteDappCategoryRel:input_type -> api.walletadmin.v1.DeleteDappCategoryRelReq
	2,  // 32: api.walletadmin.v1.AdminDappSrv.ListDappCategoryRel:input_type -> api.walletadmin.v1.ListDappCategoryRelReq
	25, // 33: api.walletadmin.v1.AdminDappSrv.CreateDappTopic:input_type -> api.walletadmin.v1.CreateDappTopicReq
	0,  // 34: api.walletadmin.v1.AdminDappSrv.DeleteDappTopic:input_type -> api.walletadmin.v1.DeleteDappTopicReq
	52, // 35: api.walletadmin.v1.AdminDappSrv.UpdateDappTopic:input_type -> api.walletadmin.v1.UpdateDappTopicReq
	54, // 36: api.walletadmin.v1.AdminDappSrv.ListDappTopic:input_type -> api.walletadmin.v1.ListDappTopicReq
	18, // 37: api.walletadmin.v1.AdminDappSrv.CreateDappNavigation:input_type -> api.walletadmin.v1.CreateDappNavigationReq
	20, // 38: api.walletadmin.v1.AdminDappSrv.DeleteDappNavigation:input_type -> api.walletadmin.v1.DeleteDappNavigationReq
	22, // 39: api.walletadmin.v1.AdminDappSrv.ListDappNavigation:input_type -> api.walletadmin.v1.ListDappNavigationReq
	63, // 40: api.walletadmin.v1.AdminDappSrv.BatchUpdateDappNavigation:input_type -> api.walletadmin.v1.BatchUpdateDappNavigationReq
	65, // 41: api.walletadmin.v1.AdminDappSrv.SortDappNavigation:input_type -> api.walletadmin.v1.SortReq
	8,  // 42: api.walletadmin.v1.AdminDappSrv.CreateDappIndex:input_type -> api.walletadmin.v1.CreateDappIndexReq
	10, // 43: api.walletadmin.v1.AdminDappSrv.ListDappIndex:input_type -> api.walletadmin.v1.ListDappIndexReq
	12, // 44: api.walletadmin.v1.AdminDappSrv.DeleteDappIndex:input_type -> api.walletadmin.v1.DeleteDappIndexReq
	16, // 45: api.walletadmin.v1.AdminDappSrv.BatchUpdateDappIndex:input_type -> api.walletadmin.v1.BatchUpdateDappIndexReq
	65, // 46: api.walletadmin.v1.AdminDappSrv.SortDappIndex:input_type -> api.walletadmin.v1.SortReq
	57, // 47: api.walletadmin.v1.AdminDappSrv.CreateDappTopicRel:input_type -> api.walletadmin.v1.CreateDappTopicRelReq
	59, // 48: api.walletadmin.v1.AdminDappSrv.DeleteDappTopicRel:input_type -> api.walletadmin.v1.DeleteDappTopicRelReq
	61, // 49: api.walletadmin.v1.AdminDappSrv.ListDappTopicRel:input_type -> api.walletadmin.v1.ListDappTopicRelReq
	50, // 50: api.walletadmin.v1.AdminDappSrv.CreateDapp:output_type -> api.walletadmin.v1.CreateDappReply
	46, // 51: api.walletadmin.v1.AdminDappSrv.DeleteDapp:output_type -> api.walletadmin.v1.DeleteDappReply
	44, // 52: api.walletadmin.v1.AdminDappSrv.UpdateDapp:output_type -> api.walletadmin.v1.UpdateDappReply
	42, // 53: api.walletadmin.v1.AdminDappSrv.ListDapp:output_type -> api.walletadmin.v1.ListDappReply
	40, // 54: api.walletadmin.v1.AdminDappSrv.CreateDappCategory:output_type -> api.walletadmin.v1.CreateDappCategoryReply
	33, // 55: api.walletadmin.v1.AdminDappSrv.UpdateDappCategory:output_type -> api.walletadmin.v1.UpdateDappCategoryReply
	31, // 56: api.walletadmin.v1.AdminDappSrv.ListDappCategory:output_type -> api.walletadmin.v1.ListDappCategoryReply
	35, // 57: api.walletadmin.v1.AdminDappSrv.DeleteDappCategory:output_type -> api.walletadmin.v1.DeleteDappCategoryReply
	7,  // 58: api.walletadmin.v1.AdminDappSrv.CreateDappCategoryRel:output_type -> api.walletadmin.v1.CreateDappCategoryRelReply
	5,  // 59: api.walletadmin.v1.AdminDappSrv.DeleteDappCategoryRel:output_type -> api.walletadmin.v1.DeleteDappCategoryRelReply
	3,  // 60: api.walletadmin.v1.AdminDappSrv.ListDappCategoryRel:output_type -> api.walletadmin.v1.ListDappCategoryRelReply
	26, // 61: api.walletadmin.v1.AdminDappSrv.CreateDappTopic:output_type -> api.walletadmin.v1.CreateDappTopicReply
	1,  // 62: api.walletadmin.v1.AdminDappSrv.DeleteDappTopic:output_type -> api.walletadmin.v1.DeleteDappTopicReply
	53, // 63: api.walletadmin.v1.AdminDappSrv.UpdateDappTopic:output_type -> api.walletadmin.v1.UpdateDappTopicReply
	55, // 64: api.walletadmin.v1.AdminDappSrv.ListDappTopic:output_type -> api.walletadmin.v1.ListDappTopicReply
	19, // 65: api.walletadmin.v1.AdminDappSrv.CreateDappNavigation:output_type -> api.walletadmin.v1.CreateDappNavigationReply
	21, // 66: api.walletadmin.v1.AdminDappSrv.DeleteDappNavigation:output_type -> api.walletadmin.v1.DeleteDappNavigationReply
	23, // 67: api.walletadmin.v1.AdminDappSrv.ListDappNavigation:output_type -> api.walletadmin.v1.ListDappNavigationReply
	64, // 68: api.walletadmin.v1.AdminDappSrv.BatchUpdateDappNavigation:output_type -> api.walletadmin.v1.BatchUpdateDappNavigationReply
	66, // 69: api.walletadmin.v1.AdminDappSrv.SortDappNavigation:output_type -> api.walletadmin.v1.SortReply
	9,  // 70: api.walletadmin.v1.AdminDappSrv.CreateDappIndex:output_type -> api.walletadmin.v1.CreateDappIndexReply
	11, // 71: api.walletadmin.v1.AdminDappSrv.ListDappIndex:output_type -> api.walletadmin.v1.ListDappIndexReply
	13, // 72: api.walletadmin.v1.AdminDappSrv.DeleteDappIndex:output_type -> api.walletadmin.v1.DeleteDappIndexReply
	17, // 73: api.walletadmin.v1.AdminDappSrv.BatchUpdateDappIndex:output_type -> api.walletadmin.v1.BatchUpdateDappIndexReply
	66, // 74: api.walletadmin.v1.AdminDappSrv.SortDappIndex:output_type -> api.walletadmin.v1.SortReply
	58, // 75: api.walletadmin.v1.AdminDappSrv.CreateDappTopicRel:output_type -> api.walletadmin.v1.CreateDappTopicRelReply
	60, // 76: api.walletadmin.v1.AdminDappSrv.DeleteDappTopicRel:output_type -> api.walletadmin.v1.DeleteDappTopicRelReply
	62, // 77: api.walletadmin.v1.AdminDappSrv.ListDappTopicRel:output_type -> api.walletadmin.v1.ListDappTopicRelReply
	50, // [50:78] is the sub-list for method output_type
	22, // [22:50] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_dapp_proto_init() }
func file_api_walletadmin_v1_dapp_proto_init() {
	if File_api_walletadmin_v1_dapp_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_dapp_proto_rawDesc), len(file_api_walletadmin_v1_dapp_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   67,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_dapp_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_dapp_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_dapp_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_dapp_proto = out.File
	file_api_walletadmin_v1_dapp_proto_goTypes = nil
	file_api_walletadmin_v1_dapp_proto_depIdxs = nil
}
