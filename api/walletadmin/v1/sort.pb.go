// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/sort.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 排序类型
type SortType int32

const (
	// 向上排序
	SortType_UP SortType = 0
	// 向下排序
	SortType_DOWN SortType = 1
	// 置顶
	SortType_TOP SortType = 2
)

// Enum value maps for SortType.
var (
	SortType_name = map[int32]string{
		0: "UP",
		1: "DOWN",
		2: "TOP",
	}
	SortType_value = map[string]int32{
		"UP":   0,
		"DOWN": 1,
		"TOP":  2,
	}
)

func (x SortType) Enum() *SortType {
	p := new(SortType)
	*p = x
	return p
}

func (x SortType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SortType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_walletadmin_v1_sort_proto_enumTypes[0].Descriptor()
}

func (SortType) Type() protoreflect.EnumType {
	return &file_api_walletadmin_v1_sort_proto_enumTypes[0]
}

func (x SortType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SortType.Descriptor instead.
func (SortType) EnumDescriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_sort_proto_rawDescGZIP(), []int{0}
}

// 排序请求
type CommonSortReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 排序类型
	SortType      SortType `protobuf:"varint,2,opt,name=sort_type,json=sortType,proto3,enum=api.walletadmin.v1.SortType" json:"sort_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonSortReq) Reset() {
	*x = CommonSortReq{}
	mi := &file_api_walletadmin_v1_sort_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonSortReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonSortReq) ProtoMessage() {}

func (x *CommonSortReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_sort_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonSortReq.ProtoReflect.Descriptor instead.
func (*CommonSortReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_sort_proto_rawDescGZIP(), []int{0}
}

func (x *CommonSortReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CommonSortReq) GetSortType() SortType {
	if x != nil {
		return x.SortType
	}
	return SortType_UP
}

type CommonSortReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonSortReply) Reset() {
	*x = CommonSortReply{}
	mi := &file_api_walletadmin_v1_sort_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonSortReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonSortReply) ProtoMessage() {}

func (x *CommonSortReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_sort_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonSortReply.ProtoReflect.Descriptor instead.
func (*CommonSortReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_sort_proto_rawDescGZIP(), []int{1}
}

var File_api_walletadmin_v1_sort_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_sort_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/walletadmin/v1/sort.proto\x12\x12api.walletadmin.v1\"Z\n" +
	"\rCommonSortReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x129\n" +
	"\tsort_type\x18\x02 \x01(\x0e2\x1c.api.walletadmin.v1.SortTypeR\bsortType\"\x11\n" +
	"\x0fCommonSortReply*%\n" +
	"\bSortType\x12\x06\n" +
	"\x02UP\x10\x00\x12\b\n" +
	"\x04DOWN\x10\x01\x12\a\n" +
	"\x03TOP\x10\x02B\xaf\x01\n" +
	"\x16com.api.walletadmin.v1B\tSortProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_sort_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_sort_proto_rawDescData []byte
)

func file_api_walletadmin_v1_sort_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_sort_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_sort_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_sort_proto_rawDesc), len(file_api_walletadmin_v1_sort_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_sort_proto_rawDescData
}

var file_api_walletadmin_v1_sort_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_walletadmin_v1_sort_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_walletadmin_v1_sort_proto_goTypes = []any{
	(SortType)(0),           // 0: api.walletadmin.v1.SortType
	(*CommonSortReq)(nil),   // 1: api.walletadmin.v1.CommonSortReq
	(*CommonSortReply)(nil), // 2: api.walletadmin.v1.CommonSortReply
}
var file_api_walletadmin_v1_sort_proto_depIdxs = []int32{
	0, // 0: api.walletadmin.v1.CommonSortReq.sort_type:type_name -> api.walletadmin.v1.SortType
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_sort_proto_init() }
func file_api_walletadmin_v1_sort_proto_init() {
	if File_api_walletadmin_v1_sort_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_sort_proto_rawDesc), len(file_api_walletadmin_v1_sort_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_walletadmin_v1_sort_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_sort_proto_depIdxs,
		EnumInfos:         file_api_walletadmin_v1_sort_proto_enumTypes,
		MessageInfos:      file_api_walletadmin_v1_sort_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_sort_proto = out.File
	file_api_walletadmin_v1_sort_proto_goTypes = nil
	file_api_walletadmin_v1_sort_proto_depIdxs = nil
}
