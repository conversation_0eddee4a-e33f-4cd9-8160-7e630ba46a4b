// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/chain.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateChainSortReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前排序(来自列表的sort_order字段值)
	CurrentSort int64 `protobuf:"varint,1,opt,name=current_sort,json=currentSort,proto3" json:"current_sort,omitempty"`
	// 更新后排序(数值越大,排序越靠前)
	TargetSort int64 `protobuf:"varint,2,opt,name=target_sort,json=targetSort,proto3" json:"target_sort,omitempty"`
	// 记录ID
	Id            uint64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateChainSortReq) Reset() {
	*x = UpdateChainSortReq{}
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateChainSortReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateChainSortReq) ProtoMessage() {}

func (x *UpdateChainSortReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateChainSortReq.ProtoReflect.Descriptor instead.
func (*UpdateChainSortReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_chain_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateChainSortReq) GetCurrentSort() int64 {
	if x != nil {
		return x.CurrentSort
	}
	return 0
}

func (x *UpdateChainSortReq) GetTargetSort() int64 {
	if x != nil {
		return x.TargetSort
	}
	return 0
}

func (x *UpdateChainSortReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateChainReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 网络图标
	BlockchainUrl string `protobuf:"bytes,1,opt,name=blockchain_url,json=blockchainUrl,proto3" json:"blockchain_url,omitempty"`
	// 币图标
	TokenUrl string `protobuf:"bytes,2,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	// 是否展示
	IsDisplay bool `protobuf:"varint,3,opt,name=is_display,json=isDisplay,proto3" json:"is_display,omitempty"`
	// 记录ID
	Id            uint64 `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateChainReq) Reset() {
	*x = UpdateChainReq{}
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateChainReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateChainReq) ProtoMessage() {}

func (x *UpdateChainReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateChainReq.ProtoReflect.Descriptor instead.
func (*UpdateChainReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_chain_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateChainReq) GetBlockchainUrl() string {
	if x != nil {
		return x.BlockchainUrl
	}
	return ""
}

func (x *UpdateChainReq) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *UpdateChainReq) GetIsDisplay() bool {
	if x != nil {
		return x.IsDisplay
	}
	return false
}

func (x *UpdateChainReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListChainReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公链名称
	ChainName     *string `protobuf:"bytes,3,opt,name=chain_name,json=chainName,proto3,oneof" json:"chain_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListChainReq) Reset() {
	*x = ListChainReq{}
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListChainReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChainReq) ProtoMessage() {}

func (x *ListChainReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChainReq.ProtoReflect.Descriptor instead.
func (*ListChainReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_chain_proto_rawDescGZIP(), []int{2}
}

func (x *ListChainReq) GetChainName() string {
	if x != nil && x.ChainName != nil {
		return *x.ChainName
	}
	return ""
}

type Chain struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 链名称
	ChainName string `protobuf:"bytes,2,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 精度位数
	Decimals int64 `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 链类型
	ChainType string `protobuf:"bytes,6,opt,name=chain_type,json=chainType,proto3" json:"chain_type,omitempty"`
	// 当前同步块高
	CurrencyBlock int64 `protobuf:"varint,7,opt,name=currency_block,json=currencyBlock,proto3" json:"currency_block,omitempty"`
	// 是否正在同步
	IsSyncing bool `protobuf:"varint,8,opt,name=is_syncing,json=isSyncing,proto3" json:"is_syncing,omitempty"`
	// 是否展示
	IsDisplay bool `protobuf:"varint,9,opt,name=is_display,json=isDisplay,proto3" json:"is_display,omitempty"`
	// 网络图标
	BlockchainUrl string `protobuf:"bytes,10,opt,name=blockchain_url,json=blockchainUrl,proto3" json:"blockchain_url,omitempty"`
	// 币图标
	TokenUrl string `protobuf:"bytes,11,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	// 网络ID
	ChainId string `protobuf:"bytes,12,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// 排序
	SortOrder     int64 `protobuf:"varint,13,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Chain) Reset() {
	*x = Chain{}
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Chain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chain) ProtoMessage() {}

func (x *Chain) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chain.ProtoReflect.Descriptor instead.
func (*Chain) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_chain_proto_rawDescGZIP(), []int{3}
}

func (x *Chain) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Chain) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

func (x *Chain) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Chain) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Chain) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *Chain) GetChainType() string {
	if x != nil {
		return x.ChainType
	}
	return ""
}

func (x *Chain) GetCurrencyBlock() int64 {
	if x != nil {
		return x.CurrencyBlock
	}
	return 0
}

func (x *Chain) GetIsSyncing() bool {
	if x != nil {
		return x.IsSyncing
	}
	return false
}

func (x *Chain) GetIsDisplay() bool {
	if x != nil {
		return x.IsDisplay
	}
	return false
}

func (x *Chain) GetBlockchainUrl() string {
	if x != nil {
		return x.BlockchainUrl
	}
	return ""
}

func (x *Chain) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *Chain) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *Chain) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

type ListChainReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Chain               `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListChainReply) Reset() {
	*x = ListChainReply{}
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListChainReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChainReply) ProtoMessage() {}

func (x *ListChainReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_chain_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChainReply.ProtoReflect.Descriptor instead.
func (*ListChainReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_chain_proto_rawDescGZIP(), []int{4}
}

func (x *ListChainReply) GetList() []*Chain {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_walletadmin_v1_chain_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_chain_proto_rawDesc = "" +
	"\n" +
	"\x1eapi/walletadmin/v1/chain.proto\x12\x12api.walletadmin.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1bgoogle/protobuf/empty.proto\"q\n" +
	"\x12UpdateChainSortReq\x12!\n" +
	"\fcurrent_sort\x18\x01 \x01(\x03R\vcurrentSort\x12\x1f\n" +
	"\vtarget_sort\x18\x02 \x01(\x03R\n" +
	"targetSort\x12\x17\n" +
	"\x02id\x18\x03 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"\xa0\x01\n" +
	"\x0eUpdateChainReq\x12/\n" +
	"\x0eblockchain_url\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x88\x01\x01R\rblockchainUrl\x12%\n" +
	"\ttoken_url\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x88\x01\x01R\btokenUrl\x12\x1d\n" +
	"\n" +
	"is_display\x18\x03 \x01(\bR\tisDisplay\x12\x17\n" +
	"\x02id\x18\x04 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"A\n" +
	"\fListChainReq\x12\"\n" +
	"\n" +
	"chain_name\x18\x03 \x01(\tH\x00R\tchainName\x88\x01\x01B\r\n" +
	"\v_chain_name\"\x8d\x03\n" +
	"\x05Chain\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1d\n" +
	"\n" +
	"chain_name\x18\x02 \x01(\tR\tchainName\x12\x1f\n" +
	"\vchain_index\x18\x03 \x01(\x03R\n" +
	"chainIndex\x12\x16\n" +
	"\x06symbol\x18\x04 \x01(\tR\x06symbol\x12\x1a\n" +
	"\bdecimals\x18\x05 \x01(\x03R\bdecimals\x12\x1d\n" +
	"\n" +
	"chain_type\x18\x06 \x01(\tR\tchainType\x12%\n" +
	"\x0ecurrency_block\x18\a \x01(\x03R\rcurrencyBlock\x12\x1d\n" +
	"\n" +
	"is_syncing\x18\b \x01(\bR\tisSyncing\x12\x1d\n" +
	"\n" +
	"is_display\x18\t \x01(\bR\tisDisplay\x12%\n" +
	"\x0eblockchain_url\x18\n" +
	" \x01(\tR\rblockchainUrl\x12\x1b\n" +
	"\ttoken_url\x18\v \x01(\tR\btokenUrl\x12\x19\n" +
	"\bchain_id\x18\f \x01(\tR\achainId\x12\x1d\n" +
	"\n" +
	"sort_order\x18\r \x01(\x03R\tsortOrder\"?\n" +
	"\x0eListChainReply\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.api.walletadmin.v1.ChainR\x04list2\xf6\x02\n" +
	"\bChainSrv\x12u\n" +
	"\tListChain\x12 .api.walletadmin.v1.ListChainReq\x1a\".api.walletadmin.v1.ListChainReply\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/admin/v1/chain/list_chain\x12r\n" +
	"\vUpdateChain\x12\".api.walletadmin.v1.UpdateChainReq\x1a\x16.google.protobuf.Empty\"'\x82\xd3\xe4\x93\x02!:\x01*\x1a\x1c/admin/v1/chain/update_chain\x12\x7f\n" +
	"\x0fUpdateChainSort\x12&.api.walletadmin.v1.UpdateChainSortReq\x1a\x16.google.protobuf.Empty\",\x82\xd3\xe4\x93\x02&:\x01*2!/admin/v1/chain/update_chain_sortB\xb0\x01\n" +
	"\x16com.api.walletadmin.v1B\n" +
	"ChainProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_chain_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_chain_proto_rawDescData []byte
)

func file_api_walletadmin_v1_chain_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_chain_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_chain_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_chain_proto_rawDesc), len(file_api_walletadmin_v1_chain_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_chain_proto_rawDescData
}

var file_api_walletadmin_v1_chain_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_walletadmin_v1_chain_proto_goTypes = []any{
	(*UpdateChainSortReq)(nil), // 0: api.walletadmin.v1.UpdateChainSortReq
	(*UpdateChainReq)(nil),     // 1: api.walletadmin.v1.UpdateChainReq
	(*ListChainReq)(nil),       // 2: api.walletadmin.v1.ListChainReq
	(*Chain)(nil),              // 3: api.walletadmin.v1.Chain
	(*ListChainReply)(nil),     // 4: api.walletadmin.v1.ListChainReply
	(*emptypb.Empty)(nil),      // 5: google.protobuf.Empty
}
var file_api_walletadmin_v1_chain_proto_depIdxs = []int32{
	3, // 0: api.walletadmin.v1.ListChainReply.list:type_name -> api.walletadmin.v1.Chain
	2, // 1: api.walletadmin.v1.ChainSrv.ListChain:input_type -> api.walletadmin.v1.ListChainReq
	1, // 2: api.walletadmin.v1.ChainSrv.UpdateChain:input_type -> api.walletadmin.v1.UpdateChainReq
	0, // 3: api.walletadmin.v1.ChainSrv.UpdateChainSort:input_type -> api.walletadmin.v1.UpdateChainSortReq
	4, // 4: api.walletadmin.v1.ChainSrv.ListChain:output_type -> api.walletadmin.v1.ListChainReply
	5, // 5: api.walletadmin.v1.ChainSrv.UpdateChain:output_type -> google.protobuf.Empty
	5, // 6: api.walletadmin.v1.ChainSrv.UpdateChainSort:output_type -> google.protobuf.Empty
	4, // [4:7] is the sub-list for method output_type
	1, // [1:4] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_chain_proto_init() }
func file_api_walletadmin_v1_chain_proto_init() {
	if File_api_walletadmin_v1_chain_proto != nil {
		return
	}
	file_api_walletadmin_v1_chain_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_chain_proto_rawDesc), len(file_api_walletadmin_v1_chain_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_chain_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_chain_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_chain_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_chain_proto = out.File
	file_api_walletadmin_v1_chain_proto_goTypes = nil
	file_api_walletadmin_v1_chain_proto_depIdxs = nil
}
