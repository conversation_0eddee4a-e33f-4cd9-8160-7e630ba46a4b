// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/walletadmin/v1/tx.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationTxSrvListTx = "/api.walletadmin.v1.TxSrv/ListTx"

type TxSrvHTTPServer interface {
	// ListTx 交易列表
	ListTx(context.Context, *ListTxReq) (*ListTxReply, error)
}

func RegisterTxSrvHTTPServer(s *http.Server, srv TxSrvHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/tx/list_tx", _TxSrv_ListTx0_HTTP_Handler(srv))
}

func _TxSrv_ListTx0_HTTP_Handler(srv TxSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListTxReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTxSrvListTx)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListTx(ctx, req.(*ListTxReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListTxReply)
		return ctx.Result(200, reply)
	}
}

type TxSrvHTTPClient interface {
	ListTx(ctx context.Context, req *ListTxReq, opts ...http.CallOption) (rsp *ListTxReply, err error)
}

type TxSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewTxSrvHTTPClient(client *http.Client) TxSrvHTTPClient {
	return &TxSrvHTTPClientImpl{client}
}

func (c *TxSrvHTTPClientImpl) ListTx(ctx context.Context, in *ListTxReq, opts ...http.CallOption) (*ListTxReply, error) {
	var out ListTxReply
	pattern := "/admin/v1/tx/list_tx"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationTxSrvListTx))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
