// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/walletadmin/v1/user_guide.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserGuideServiceCreateUserGuide = "/api.walletadmin.v1.UserGuideService/CreateUserGuide"
const OperationUserGuideServiceCreateUserGuideCategory = "/api.walletadmin.v1.UserGuideService/CreateUserGuideCategory"
const OperationUserGuideServiceDeleteUserGuide = "/api.walletadmin.v1.UserGuideService/DeleteUserGuide"
const OperationUserGuideServiceDeleteUserGuideCategory = "/api.walletadmin.v1.UserGuideService/DeleteUserGuideCategory"
const OperationUserGuideServiceGetUserGuide = "/api.walletadmin.v1.UserGuideService/GetUserGuide"
const OperationUserGuideServiceGetUserGuideCategory = "/api.walletadmin.v1.UserGuideService/GetUserGuideCategory"
const OperationUserGuideServiceListUserGuideCategories = "/api.walletadmin.v1.UserGuideService/ListUserGuideCategories"
const OperationUserGuideServiceListUserGuides = "/api.walletadmin.v1.UserGuideService/ListUserGuides"
const OperationUserGuideServiceUpdateUserGuide = "/api.walletadmin.v1.UserGuideService/UpdateUserGuide"
const OperationUserGuideServiceUpdateUserGuideCategory = "/api.walletadmin.v1.UserGuideService/UpdateUserGuideCategory"

type UserGuideServiceHTTPServer interface {
	// CreateUserGuide 创建用户指南
	CreateUserGuide(context.Context, *CreateUserGuideReq) (*CreateUserGuideReply, error)
	// CreateUserGuideCategory 创建用户指南分类
	CreateUserGuideCategory(context.Context, *CreateUserGuideCategoryReq) (*CreateUserGuideCategoryReply, error)
	// DeleteUserGuide 删除用户指南
	DeleteUserGuide(context.Context, *DeleteUserGuideReq) (*emptypb.Empty, error)
	// DeleteUserGuideCategory 删除用户指南分类
	DeleteUserGuideCategory(context.Context, *DeleteUserGuideCategoryReq) (*emptypb.Empty, error)
	// GetUserGuide 获取用户指南详情
	GetUserGuide(context.Context, *GetUserGuideReq) (*UserGuideInfo, error)
	// GetUserGuideCategory 获取用户指南分类详情
	GetUserGuideCategory(context.Context, *GetUserGuideCategoryReq) (*UserGuideCategoryInfo, error)
	// ListUserGuideCategories 用户指南分类列表
	ListUserGuideCategories(context.Context, *ListUserGuideCategoriesReq) (*ListUserGuideCategoriesReply, error)
	// ListUserGuides 用户指南列表
	ListUserGuides(context.Context, *ListUserGuidesReq) (*ListUserGuidesReply, error)
	// UpdateUserGuide 更新用户指南
	UpdateUserGuide(context.Context, *UpdateUserGuideReq) (*emptypb.Empty, error)
	// UpdateUserGuideCategory 更新用户指南分类
	UpdateUserGuideCategory(context.Context, *UpdateUserGuideCategoryReq) (*emptypb.Empty, error)
}

func RegisterUserGuideServiceHTTPServer(s *http.Server, srv UserGuideServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/admin/v1/user-guides", _UserGuideService_CreateUserGuide0_HTTP_Handler(srv))
	r.PUT("/admin/v1/user-guides/{id}", _UserGuideService_UpdateUserGuide0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/user-guides/{id}", _UserGuideService_DeleteUserGuide0_HTTP_Handler(srv))
	r.GET("/admin/v1/user-guides", _UserGuideService_ListUserGuides0_HTTP_Handler(srv))
	r.GET("/admin/v1/user-guides/{id}", _UserGuideService_GetUserGuide0_HTTP_Handler(srv))
	r.POST("/admin/v1/user-guide-categories", _UserGuideService_CreateUserGuideCategory0_HTTP_Handler(srv))
	r.PUT("/admin/v1/user-guide-categories/{id}", _UserGuideService_UpdateUserGuideCategory0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/user-guide-categories/{id}", _UserGuideService_DeleteUserGuideCategory0_HTTP_Handler(srv))
	r.GET("/admin/v1/user-guide-categories", _UserGuideService_ListUserGuideCategories0_HTTP_Handler(srv))
	r.GET("/admin/v1/user-guide-categories/{id}", _UserGuideService_GetUserGuideCategory0_HTTP_Handler(srv))
}

func _UserGuideService_CreateUserGuide0_HTTP_Handler(srv UserGuideServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserGuideReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideServiceCreateUserGuide)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserGuide(ctx, req.(*CreateUserGuideReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateUserGuideReply)
		return ctx.Result(200, reply)
	}
}

func _UserGuideService_UpdateUserGuide0_HTTP_Handler(srv UserGuideServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateUserGuideReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideServiceUpdateUserGuide)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserGuide(ctx, req.(*UpdateUserGuideReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _UserGuideService_DeleteUserGuide0_HTTP_Handler(srv UserGuideServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteUserGuideReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideServiceDeleteUserGuide)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUserGuide(ctx, req.(*DeleteUserGuideReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _UserGuideService_ListUserGuides0_HTTP_Handler(srv UserGuideServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserGuidesReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideServiceListUserGuides)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserGuides(ctx, req.(*ListUserGuidesReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserGuidesReply)
		return ctx.Result(200, reply)
	}
}

func _UserGuideService_GetUserGuide0_HTTP_Handler(srv UserGuideServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserGuideReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideServiceGetUserGuide)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserGuide(ctx, req.(*GetUserGuideReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserGuideInfo)
		return ctx.Result(200, reply)
	}
}

func _UserGuideService_CreateUserGuideCategory0_HTTP_Handler(srv UserGuideServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateUserGuideCategoryReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideServiceCreateUserGuideCategory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserGuideCategory(ctx, req.(*CreateUserGuideCategoryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateUserGuideCategoryReply)
		return ctx.Result(200, reply)
	}
}

func _UserGuideService_UpdateUserGuideCategory0_HTTP_Handler(srv UserGuideServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateUserGuideCategoryReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideServiceUpdateUserGuideCategory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserGuideCategory(ctx, req.(*UpdateUserGuideCategoryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _UserGuideService_DeleteUserGuideCategory0_HTTP_Handler(srv UserGuideServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteUserGuideCategoryReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideServiceDeleteUserGuideCategory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUserGuideCategory(ctx, req.(*DeleteUserGuideCategoryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _UserGuideService_ListUserGuideCategories0_HTTP_Handler(srv UserGuideServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserGuideCategoriesReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideServiceListUserGuideCategories)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserGuideCategories(ctx, req.(*ListUserGuideCategoriesReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserGuideCategoriesReply)
		return ctx.Result(200, reply)
	}
}

func _UserGuideService_GetUserGuideCategory0_HTTP_Handler(srv UserGuideServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserGuideCategoryReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideServiceGetUserGuideCategory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserGuideCategory(ctx, req.(*GetUserGuideCategoryReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UserGuideCategoryInfo)
		return ctx.Result(200, reply)
	}
}

type UserGuideServiceHTTPClient interface {
	CreateUserGuide(ctx context.Context, req *CreateUserGuideReq, opts ...http.CallOption) (rsp *CreateUserGuideReply, err error)
	CreateUserGuideCategory(ctx context.Context, req *CreateUserGuideCategoryReq, opts ...http.CallOption) (rsp *CreateUserGuideCategoryReply, err error)
	DeleteUserGuide(ctx context.Context, req *DeleteUserGuideReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteUserGuideCategory(ctx context.Context, req *DeleteUserGuideCategoryReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetUserGuide(ctx context.Context, req *GetUserGuideReq, opts ...http.CallOption) (rsp *UserGuideInfo, err error)
	GetUserGuideCategory(ctx context.Context, req *GetUserGuideCategoryReq, opts ...http.CallOption) (rsp *UserGuideCategoryInfo, err error)
	ListUserGuideCategories(ctx context.Context, req *ListUserGuideCategoriesReq, opts ...http.CallOption) (rsp *ListUserGuideCategoriesReply, err error)
	ListUserGuides(ctx context.Context, req *ListUserGuidesReq, opts ...http.CallOption) (rsp *ListUserGuidesReply, err error)
	UpdateUserGuide(ctx context.Context, req *UpdateUserGuideReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateUserGuideCategory(ctx context.Context, req *UpdateUserGuideCategoryReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type UserGuideServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewUserGuideServiceHTTPClient(client *http.Client) UserGuideServiceHTTPClient {
	return &UserGuideServiceHTTPClientImpl{client}
}

func (c *UserGuideServiceHTTPClientImpl) CreateUserGuide(ctx context.Context, in *CreateUserGuideReq, opts ...http.CallOption) (*CreateUserGuideReply, error) {
	var out CreateUserGuideReply
	pattern := "/admin/v1/user-guides"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserGuideServiceCreateUserGuide))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideServiceHTTPClientImpl) CreateUserGuideCategory(ctx context.Context, in *CreateUserGuideCategoryReq, opts ...http.CallOption) (*CreateUserGuideCategoryReply, error) {
	var out CreateUserGuideCategoryReply
	pattern := "/admin/v1/user-guide-categories"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserGuideServiceCreateUserGuideCategory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideServiceHTTPClientImpl) DeleteUserGuide(ctx context.Context, in *DeleteUserGuideReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/user-guides/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGuideServiceDeleteUserGuide))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideServiceHTTPClientImpl) DeleteUserGuideCategory(ctx context.Context, in *DeleteUserGuideCategoryReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/user-guide-categories/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGuideServiceDeleteUserGuideCategory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideServiceHTTPClientImpl) GetUserGuide(ctx context.Context, in *GetUserGuideReq, opts ...http.CallOption) (*UserGuideInfo, error) {
	var out UserGuideInfo
	pattern := "/admin/v1/user-guides/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGuideServiceGetUserGuide))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideServiceHTTPClientImpl) GetUserGuideCategory(ctx context.Context, in *GetUserGuideCategoryReq, opts ...http.CallOption) (*UserGuideCategoryInfo, error) {
	var out UserGuideCategoryInfo
	pattern := "/admin/v1/user-guide-categories/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGuideServiceGetUserGuideCategory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideServiceHTTPClientImpl) ListUserGuideCategories(ctx context.Context, in *ListUserGuideCategoriesReq, opts ...http.CallOption) (*ListUserGuideCategoriesReply, error) {
	var out ListUserGuideCategoriesReply
	pattern := "/admin/v1/user-guide-categories"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGuideServiceListUserGuideCategories))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideServiceHTTPClientImpl) ListUserGuides(ctx context.Context, in *ListUserGuidesReq, opts ...http.CallOption) (*ListUserGuidesReply, error) {
	var out ListUserGuidesReply
	pattern := "/admin/v1/user-guides"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGuideServiceListUserGuides))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideServiceHTTPClientImpl) UpdateUserGuide(ctx context.Context, in *UpdateUserGuideReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/user-guides/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserGuideServiceUpdateUserGuide))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideServiceHTTPClientImpl) UpdateUserGuideCategory(ctx context.Context, in *UpdateUserGuideCategoryReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/user-guide-categories/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserGuideServiceUpdateUserGuideCategory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
