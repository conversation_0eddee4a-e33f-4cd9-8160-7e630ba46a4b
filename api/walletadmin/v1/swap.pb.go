// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/swap.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SwapChannel struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 平台名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 是否可用
	Enable        bool `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapChannel) Reset() {
	*x = SwapChannel{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapChannel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapChannel) ProtoMessage() {}

func (x *SwapChannel) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapChannel.ProtoReflect.Descriptor instead.
func (*SwapChannel) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{0}
}

func (x *SwapChannel) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SwapChannel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SwapChannel) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type SwappableToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 币名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 链名称
	ChainIndex int64 `protobuf:"varint,4,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 精度位数
	Decimals int64 `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 币合约地址
	Address string `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	// 创建时间(时间戳,单位秒)
	CreatedAt int64 `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 链名称
	ChainName string `protobuf:"bytes,8,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	// 币图标
	LogoUrl string `protobuf:"bytes,9,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 是否展示
	Display bool `protobuf:"varint,10,opt,name=display,proto3" json:"display,omitempty"`
	// 网络ID
	ChainId string `protobuf:"bytes,11,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// 是否已上架
	Enable bool `protobuf:"varint,12,opt,name=enable,proto3" json:"enable,omitempty"`
	// 商户平台
	Channels []string `protobuf:"bytes,13,rep,name=channels,proto3" json:"channels,omitempty"`
	// 是否是所有
	IsAll         bool `protobuf:"varint,14,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwappableToken) Reset() {
	*x = SwappableToken{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwappableToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwappableToken) ProtoMessage() {}

func (x *SwappableToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwappableToken.ProtoReflect.Descriptor instead.
func (*SwappableToken) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{1}
}

func (x *SwappableToken) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SwappableToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SwappableToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *SwappableToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwappableToken) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *SwappableToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SwappableToken) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SwappableToken) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

func (x *SwappableToken) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *SwappableToken) GetDisplay() bool {
	if x != nil {
		return x.Display
	}
	return false
}

func (x *SwappableToken) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *SwappableToken) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *SwappableToken) GetChannels() []string {
	if x != nil {
		return x.Channels
	}
	return nil
}

func (x *SwappableToken) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

type SwapRecord struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 兑换记录id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 兑换时间(unix时间戳)
	SwappedAt int64 `protobuf:"varint,2,opt,name=swapped_at,json=swappedAt,proto3" json:"swapped_at,omitempty"`
	// 兑换状态
	// pending(处理中), wait_kyc(等待KYC验证), wait_refund(等待退款), refund_complete(退款完成)
	// wait_deposit_send_fail(充币失败), wait_for_information(异常订单-等待处理)
	// 终态： success(兑换成功), fail(兑换失败)，timeout(超时-未在指定时间内充币)
	Status string     `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	From   *SwapToken `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty"`
	To     *SwapToken `protobuf:"bytes,5,opt,name=to,proto3" json:"to,omitempty"`
	// 用户兑换消耗的GasFee
	GasFee string `protobuf:"bytes,6,opt,name=gas_fee,json=gasFee,proto3" json:"gas_fee,omitempty"`
	// 兑换手续费率
	FeeRate string `protobuf:"bytes,7,opt,name=fee_rate,json=feeRate,proto3" json:"fee_rate,omitempty"`
	// 用户发起的交易hash
	Hash string `protobuf:"bytes,8,opt,name=hash,proto3" json:"hash,omitempty"`
	// 授权hash
	ApprovalHash string `protobuf:"bytes,9,opt,name=approval_hash,json=approvalHash,proto3" json:"approval_hash,omitempty"`
	// 区块高度
	Height int64 `protobuf:"varint,10,opt,name=height,proto3" json:"height,omitempty"`
	// 兑换平台名称
	Dex string `protobuf:"bytes,11,opt,name=dex,proto3" json:"dex,omitempty"`
	// 兑换平台logo url
	DexLogo string `protobuf:"bytes,12,opt,name=dex_logo,json=dexLogo,proto3" json:"dex_logo,omitempty"`
	// 兑换价格
	SwapPrice string `protobuf:"bytes,13,opt,name=swap_price,json=swapPrice,proto3" json:"swap_price,omitempty"`
	// 兑换详情列表
	Details []*SwapDetail `protobuf:"bytes,14,rep,name=details,proto3" json:"details,omitempty"`
	// 结束时间(unix时间戳)
	FinishedAt    int64 `protobuf:"varint,15,opt,name=finished_at,json=finishedAt,proto3" json:"finished_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapRecord) Reset() {
	*x = SwapRecord{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapRecord) ProtoMessage() {}

func (x *SwapRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapRecord.ProtoReflect.Descriptor instead.
func (*SwapRecord) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{2}
}

func (x *SwapRecord) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SwapRecord) GetSwappedAt() int64 {
	if x != nil {
		return x.SwappedAt
	}
	return 0
}

func (x *SwapRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SwapRecord) GetFrom() *SwapToken {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *SwapRecord) GetTo() *SwapToken {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *SwapRecord) GetGasFee() string {
	if x != nil {
		return x.GasFee
	}
	return ""
}

func (x *SwapRecord) GetFeeRate() string {
	if x != nil {
		return x.FeeRate
	}
	return ""
}

func (x *SwapRecord) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *SwapRecord) GetApprovalHash() string {
	if x != nil {
		return x.ApprovalHash
	}
	return ""
}

func (x *SwapRecord) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *SwapRecord) GetDex() string {
	if x != nil {
		return x.Dex
	}
	return ""
}

func (x *SwapRecord) GetDexLogo() string {
	if x != nil {
		return x.DexLogo
	}
	return ""
}

func (x *SwapRecord) GetSwapPrice() string {
	if x != nil {
		return x.SwapPrice
	}
	return ""
}

func (x *SwapRecord) GetDetails() []*SwapDetail {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *SwapRecord) GetFinishedAt() int64 {
	if x != nil {
		return x.FinishedAt
	}
	return 0
}

type SwapToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 代币合约地址
	TokenAddress string `protobuf:"bytes,1,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	// 用户地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 链
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 数量
	Amount string `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// token logo url
	TokenLogo string `protobuf:"bytes,5,opt,name=token_logo,json=tokenLogo,proto3" json:"token_logo,omitempty"`
	// 币种
	Symbol string `protobuf:"bytes,6,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 精度
	Decimals      string `protobuf:"bytes,7,opt,name=decimals,proto3" json:"decimals,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapToken) Reset() {
	*x = SwapToken{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapToken) ProtoMessage() {}

func (x *SwapToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapToken.ProtoReflect.Descriptor instead.
func (*SwapToken) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{3}
}

func (x *SwapToken) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *SwapToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SwapToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwapToken) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *SwapToken) GetTokenLogo() string {
	if x != nil {
		return x.TokenLogo
	}
	return ""
}

func (x *SwapToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *SwapToken) GetDecimals() string {
	if x != nil {
		return x.Decimals
	}
	return ""
}

type SwapDetail struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 区块浏览器url
	ExplorerUrl string `protobuf:"bytes,2,opt,name=explorer_url,json=explorerUrl,proto3" json:"explorer_url,omitempty"`
	// 交易hash
	Hash string `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	// 交易状态
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// 是否收录
	Collected     bool `protobuf:"varint,5,opt,name=collected,proto3" json:"collected,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapDetail) Reset() {
	*x = SwapDetail{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapDetail) ProtoMessage() {}

func (x *SwapDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapDetail.ProtoReflect.Descriptor instead.
func (*SwapDetail) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{4}
}

func (x *SwapDetail) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwapDetail) GetExplorerUrl() string {
	if x != nil {
		return x.ExplorerUrl
	}
	return ""
}

func (x *SwapDetail) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *SwapDetail) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SwapDetail) GetCollected() bool {
	if x != nil {
		return x.Collected
	}
	return false
}

type ListSwapChannelRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 页码 从1开始
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	PageSize      int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSwapChannelRequest) Reset() {
	*x = ListSwapChannelRequest{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSwapChannelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapChannelRequest) ProtoMessage() {}

func (x *ListSwapChannelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapChannelRequest.ProtoReflect.Descriptor instead.
func (*ListSwapChannelRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{5}
}

func (x *ListSwapChannelRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSwapChannelRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListSwapChannelReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*SwapChannel         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSwapChannelReply) Reset() {
	*x = ListSwapChannelReply{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSwapChannelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapChannelReply) ProtoMessage() {}

func (x *ListSwapChannelReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapChannelReply.ProtoReflect.Descriptor instead.
func (*ListSwapChannelReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{6}
}

func (x *ListSwapChannelReply) GetList() []*SwapChannel {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListSwapChannelReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type UpdateSwapChannelRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 是否关闭通道
	Enable        bool `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSwapChannelRequest) Reset() {
	*x = UpdateSwapChannelRequest{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSwapChannelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSwapChannelRequest) ProtoMessage() {}

func (x *UpdateSwapChannelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSwapChannelRequest.ProtoReflect.Descriptor instead.
func (*UpdateSwapChannelRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateSwapChannelRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSwapChannelRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type UpdateSwapChannelReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSwapChannelReply) Reset() {
	*x = UpdateSwapChannelReply{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSwapChannelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSwapChannelReply) ProtoMessage() {}

func (x *UpdateSwapChannelReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSwapChannelReply.ProtoReflect.Descriptor instead.
func (*UpdateSwapChannelReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{8}
}

type ListTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公链索引，传-1 获取所有
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 简称
	Symbol string `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 合约号
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	// 兑换渠道id(商户id)
	ChannelId uint64 `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// 页码 从1开始
	Page int64 `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	PageSize int64 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 是否是原生代币
	Native        bool `protobuf:"varint,7,opt,name=native,proto3" json:"native,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTokenRequest) Reset() {
	*x = ListTokenRequest{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenRequest) ProtoMessage() {}

func (x *ListTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenRequest.ProtoReflect.Descriptor instead.
func (*ListTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{9}
}

func (x *ListTokenRequest) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListTokenRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *ListTokenRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ListTokenRequest) GetChannelId() uint64 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *ListTokenRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTokenRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTokenRequest) GetNative() bool {
	if x != nil {
		return x.Native
	}
	return false
}

type ListTokenReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*SwappableToken      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTokenReply) Reset() {
	*x = ListTokenReply{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenReply) ProtoMessage() {}

func (x *ListTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenReply.ProtoReflect.Descriptor instead.
func (*ListTokenReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{10}
}

func (x *ListTokenReply) GetList() []*SwappableToken {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListTokenReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type UpdateTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 是否展示
	Display       bool `protobuf:"varint,2,opt,name=display,proto3" json:"display,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTokenRequest) Reset() {
	*x = UpdateTokenRequest{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTokenRequest) ProtoMessage() {}

func (x *UpdateTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTokenRequest.ProtoReflect.Descriptor instead.
func (*UpdateTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateTokenRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTokenRequest) GetDisplay() bool {
	if x != nil {
		return x.Display
	}
	return false
}

type UpdateTokenReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTokenReply) Reset() {
	*x = UpdateTokenReply{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTokenReply) ProtoMessage() {}

func (x *UpdateTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTokenReply.ProtoReflect.Descriptor instead.
func (*UpdateTokenReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{12}
}

type ListHotTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公链索引，传-1 获取所有
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 币种
	Symbol string `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 合约地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	// 页码 从1开始
	Page int64 `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	PageSize      int64 `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHotTokenRequest) Reset() {
	*x = ListHotTokenRequest{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHotTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHotTokenRequest) ProtoMessage() {}

func (x *ListHotTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHotTokenRequest.ProtoReflect.Descriptor instead.
func (*ListHotTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{13}
}

func (x *ListHotTokenRequest) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListHotTokenRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *ListHotTokenRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ListHotTokenRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListHotTokenRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListHotTokenReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*SwappableToken      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHotTokenReply) Reset() {
	*x = ListHotTokenReply{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHotTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHotTokenReply) ProtoMessage() {}

func (x *ListHotTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHotTokenReply.ProtoReflect.Descriptor instead.
func (*ListHotTokenReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{14}
}

func (x *ListHotTokenReply) GetList() []*SwappableToken {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListHotTokenReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type CreateHotTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 通过搜索出来的数据获取
	SwappableTokenId int64 `protobuf:"varint,1,opt,name=swappable_token_id,json=swappableTokenId,proto3" json:"swappable_token_id,omitempty"`
	// 是否是全部
	IsAll         bool `protobuf:"varint,2,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateHotTokenRequest) Reset() {
	*x = CreateHotTokenRequest{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateHotTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateHotTokenRequest) ProtoMessage() {}

func (x *CreateHotTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateHotTokenRequest.ProtoReflect.Descriptor instead.
func (*CreateHotTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{15}
}

func (x *CreateHotTokenRequest) GetSwappableTokenId() int64 {
	if x != nil {
		return x.SwappableTokenId
	}
	return 0
}

func (x *CreateHotTokenRequest) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

type CreateHotTokenReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateHotTokenReply) Reset() {
	*x = CreateHotTokenReply{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateHotTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateHotTokenReply) ProtoMessage() {}

func (x *CreateHotTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateHotTokenReply.ProtoReflect.Descriptor instead.
func (*CreateHotTokenReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{16}
}

type DeleteHotTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteHotTokenRequest) Reset() {
	*x = DeleteHotTokenRequest{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteHotTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteHotTokenRequest) ProtoMessage() {}

func (x *DeleteHotTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteHotTokenRequest.ProtoReflect.Descriptor instead.
func (*DeleteHotTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteHotTokenRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteHotTokenReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteHotTokenReply) Reset() {
	*x = DeleteHotTokenReply{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteHotTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteHotTokenReply) ProtoMessage() {}

func (x *DeleteHotTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteHotTokenReply.ProtoReflect.Descriptor instead.
func (*DeleteHotTokenReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{18}
}

type GetSwapRecordRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 兑换记录id
	Id            uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSwapRecordRequest) Reset() {
	*x = GetSwapRecordRequest{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSwapRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSwapRecordRequest) ProtoMessage() {}

func (x *GetSwapRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSwapRecordRequest.ProtoReflect.Descriptor instead.
func (*GetSwapRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{19}
}

func (x *GetSwapRecordRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ListSwapRecordRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 页码 从1开始
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	PageSize int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 交易币种合约地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	// 兑换渠道id, 不传查询所有
	ChannelId uint64 `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// 兑换状态
	// pending(处理中), wait_kyc(等待KYC验证), wait_refund(等待退款), refund_complete(退款完成)
	// wait_deposit_send_fail(充币失败), wait_for_information(异常订单-等待处理)
	// 终态： success(兑换成功), fail(兑换失败)，timeout(超时-未在指定时间内充币)
	Status string `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	// 发起账户地址
	FromAddress string `protobuf:"bytes,6,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 交易币种名称
	Symbol        string `protobuf:"bytes,7,opt,name=symbol,proto3" json:"symbol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSwapRecordRequest) Reset() {
	*x = ListSwapRecordRequest{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSwapRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapRecordRequest) ProtoMessage() {}

func (x *ListSwapRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapRecordRequest.ProtoReflect.Descriptor instead.
func (*ListSwapRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{20}
}

func (x *ListSwapRecordRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSwapRecordRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSwapRecordRequest) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ListSwapRecordRequest) GetChannelId() uint64 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *ListSwapRecordRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ListSwapRecordRequest) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *ListSwapRecordRequest) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

type ListSwapRecordReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*SwapRecord          `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSwapRecordReply) Reset() {
	*x = ListSwapRecordReply{}
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSwapRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapRecordReply) ProtoMessage() {}

func (x *ListSwapRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_swap_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapRecordReply.ProtoReflect.Descriptor instead.
func (*ListSwapRecordReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_swap_proto_rawDescGZIP(), []int{21}
}

func (x *ListSwapRecordReply) GetList() []*SwapRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListSwapRecordReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

var File_api_walletadmin_v1_swap_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_swap_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/walletadmin/v1/swap.proto\x12\x12api.walletadmin.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1dapi/walletadmin/v1/sort.proto\"I\n" +
	"\vSwapChannel\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06enable\x18\x03 \x01(\bR\x06enable\"\xfc\x02\n" +
	"\x0eSwappableToken\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x03 \x01(\tR\x06symbol\x12\x1f\n" +
	"\vchain_index\x18\x04 \x01(\x03R\n" +
	"chainIndex\x12\x1a\n" +
	"\bdecimals\x18\x05 \x01(\x03R\bdecimals\x12\x18\n" +
	"\aaddress\x18\x06 \x01(\tR\aaddress\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"chain_name\x18\b \x01(\tR\tchainName\x12\x19\n" +
	"\blogo_url\x18\t \x01(\tR\alogoUrl\x12\x18\n" +
	"\adisplay\x18\n" +
	" \x01(\bR\adisplay\x12\x19\n" +
	"\bchain_id\x18\v \x01(\tR\achainId\x12\x16\n" +
	"\x06enable\x18\f \x01(\bR\x06enable\x12\x1a\n" +
	"\bchannels\x18\r \x03(\tR\bchannels\x12\x15\n" +
	"\x06is_all\x18\x0e \x01(\bR\x05isAll\"\xe1\x03\n" +
	"\n" +
	"SwapRecord\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1d\n" +
	"\n" +
	"swapped_at\x18\x02 \x01(\x03R\tswappedAt\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x121\n" +
	"\x04from\x18\x04 \x01(\v2\x1d.api.walletadmin.v1.SwapTokenR\x04from\x12-\n" +
	"\x02to\x18\x05 \x01(\v2\x1d.api.walletadmin.v1.SwapTokenR\x02to\x12\x17\n" +
	"\agas_fee\x18\x06 \x01(\tR\x06gasFee\x12\x19\n" +
	"\bfee_rate\x18\a \x01(\tR\afeeRate\x12\x12\n" +
	"\x04hash\x18\b \x01(\tR\x04hash\x12#\n" +
	"\rapproval_hash\x18\t \x01(\tR\fapprovalHash\x12\x16\n" +
	"\x06height\x18\n" +
	" \x01(\x03R\x06height\x12\x10\n" +
	"\x03dex\x18\v \x01(\tR\x03dex\x12\x19\n" +
	"\bdex_logo\x18\f \x01(\tR\adexLogo\x12\x1d\n" +
	"\n" +
	"swap_price\x18\r \x01(\tR\tswapPrice\x128\n" +
	"\adetails\x18\x0e \x03(\v2\x1e.api.walletadmin.v1.SwapDetailR\adetails\x12\x1f\n" +
	"\vfinished_at\x18\x0f \x01(\x03R\n" +
	"finishedAt\"\xd6\x01\n" +
	"\tSwapToken\x12#\n" +
	"\rtoken_address\x18\x01 \x01(\tR\ftokenAddress\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\x12\x1f\n" +
	"\vchain_index\x18\x03 \x01(\x03R\n" +
	"chainIndex\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\tR\x06amount\x12\x1d\n" +
	"\n" +
	"token_logo\x18\x05 \x01(\tR\ttokenLogo\x12\x16\n" +
	"\x06symbol\x18\x06 \x01(\tR\x06symbol\x12\x1a\n" +
	"\bdecimals\x18\a \x01(\tR\bdecimals\"\x9a\x01\n" +
	"\n" +
	"SwapDetail\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12!\n" +
	"\fexplorer_url\x18\x02 \x01(\tR\vexplorerUrl\x12\x12\n" +
	"\x04hash\x18\x03 \x01(\tR\x04hash\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\x12\x1c\n" +
	"\tcollected\x18\x05 \x01(\bR\tcollected\"]\n" +
	"\x16ListSwapChannelRequest\x12\x1b\n" +
	"\x04page\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12&\n" +
	"\tpage_size\x18\x02 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\bpageSize\"l\n" +
	"\x14ListSwapChannelReply\x123\n" +
	"\x04list\x18\x01 \x03(\v2\x1f.api.walletadmin.v1.SwapChannelR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount\"K\n" +
	"\x18UpdateSwapChannelRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12\x16\n" +
	"\x06enable\x18\x02 \x01(\bR\x06enable\"\x18\n" +
	"\x16UpdateSwapChannelReply\"\xe1\x01\n" +
	"\x10ListTokenRequest\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x16\n" +
	"\x06symbol\x18\x02 \x01(\tR\x06symbol\x12\x18\n" +
	"\aaddress\x18\x03 \x01(\tR\aaddress\x12\x1d\n" +
	"\n" +
	"channel_id\x18\x04 \x01(\x04R\tchannelId\x12\x1b\n" +
	"\x04page\x18\x05 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12&\n" +
	"\tpage_size\x18\x06 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\bpageSize\x12\x16\n" +
	"\x06native\x18\a \x01(\bR\x06native\"i\n" +
	"\x0eListTokenReply\x126\n" +
	"\x04list\x18\x01 \x03(\v2\".api.walletadmin.v1.SwappableTokenR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount\"G\n" +
	"\x12UpdateTokenRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12\x18\n" +
	"\adisplay\x18\x02 \x01(\bR\adisplay\"\x12\n" +
	"\x10UpdateTokenReply\"\xad\x01\n" +
	"\x13ListHotTokenRequest\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x16\n" +
	"\x06symbol\x18\x02 \x01(\tR\x06symbol\x12\x18\n" +
	"\aaddress\x18\x03 \x01(\tR\aaddress\x12\x1b\n" +
	"\x04page\x18\x04 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12&\n" +
	"\tpage_size\x18\x05 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\bpageSize\"l\n" +
	"\x11ListHotTokenReply\x126\n" +
	"\x04list\x18\x01 \x03(\v2\".api.walletadmin.v1.SwappableTokenR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount\"\\\n" +
	"\x15CreateHotTokenRequest\x12,\n" +
	"\x12swappable_token_id\x18\x01 \x01(\x03R\x10swappableTokenId\x12\x15\n" +
	"\x06is_all\x18\x02 \x01(\bR\x05isAll\"\x15\n" +
	"\x13CreateHotTokenReply\"0\n" +
	"\x15DeleteHotTokenRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"\x15\n" +
	"\x13DeleteHotTokenReply\"&\n" +
	"\x14GetSwapRecordRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\"\xe8\x01\n" +
	"\x15ListSwapRecordRequest\x12\x1b\n" +
	"\x04page\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12&\n" +
	"\tpage_size\x18\x02 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\bpageSize\x12\x18\n" +
	"\aaddress\x18\x03 \x01(\tR\aaddress\x12\x1d\n" +
	"\n" +
	"channel_id\x18\x04 \x01(\x04R\tchannelId\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12!\n" +
	"\ffrom_address\x18\x06 \x01(\tR\vfromAddress\x12\x16\n" +
	"\x06symbol\x18\a \x01(\tR\x06symbol\"j\n" +
	"\x13ListSwapRecordReply\x122\n" +
	"\x04list\x18\x01 \x03(\v2\x1e.api.walletadmin.v1.SwapRecordR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount2\xcd\n" +
	"\n" +
	"\vSwapService\x12\x87\x01\n" +
	"\x0fListSwapChannel\x12*.api.walletadmin.v1.ListSwapChannelRequest\x1a(.api.walletadmin.v1.ListSwapChannelReply\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/admin/v1/swap/channel\x12\x95\x01\n" +
	"\x11UpdateSwapChannel\x12,.api.walletadmin.v1.UpdateSwapChannelRequest\x1a*.api.walletadmin.v1.UpdateSwapChannelReply\"&\x82\xd3\xe4\x93\x02 :\x01*\x1a\x1b/admin/v1/swap/channel/{id}\x12s\n" +
	"\tListToken\x12$.api.walletadmin.v1.ListTokenRequest\x1a\".api.walletadmin.v1.ListTokenReply\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/admin/v1/swap/token\x12\x81\x01\n" +
	"\vUpdateToken\x12&.api.walletadmin.v1.UpdateTokenRequest\x1a$.api.walletadmin.v1.UpdateTokenReply\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\x1a\x19/admin/v1/swap/token/{id}\x12\x80\x01\n" +
	"\fListHotToken\x12'.api.walletadmin.v1.ListHotTokenRequest\x1a%.api.walletadmin.v1.ListHotTokenReply\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/admin/v1/swap/token/hot\x12\x89\x01\n" +
	"\x0eCreateHotToken\x12).api.walletadmin.v1.CreateHotTokenRequest\x1a'.api.walletadmin.v1.CreateHotTokenReply\"#\x82\xd3\xe4\x93\x02\x1d:\x01*\"\x18/admin/v1/swap/token/hot\x12\x8b\x01\n" +
	"\x0eDeleteHotToken\x12).api.walletadmin.v1.DeleteHotTokenRequest\x1a'.api.walletadmin.v1.DeleteHotTokenReply\"%\x82\xd3\xe4\x93\x02\x1f*\x1d/admin/v1/swap/token/hot/{id}\x12\x80\x01\n" +
	"\fSortHotToken\x12!.api.walletadmin.v1.CommonSortReq\x1a#.api.walletadmin.v1.CommonSortReply\"(\x82\xd3\xe4\x93\x02\":\x01*\x1a\x1d/admin/v1/swap/token/hot/sort\x12}\n" +
	"\rGetSwapRecord\x12(.api.walletadmin.v1.GetSwapRecordRequest\x1a\x1e.api.walletadmin.v1.SwapRecord\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/admin/v1/swap/record/{id}\x12\x83\x01\n" +
	"\x0eListSwapRecord\x12).api.walletadmin.v1.ListSwapRecordRequest\x1a'.api.walletadmin.v1.ListSwapRecordReply\"\x1d\x82\xd3\xe4\x93\x02\x17\x12\x15/admin/v1/swap/recordB\xaf\x01\n" +
	"\x16com.api.walletadmin.v1B\tSwapProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_swap_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_swap_proto_rawDescData []byte
)

func file_api_walletadmin_v1_swap_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_swap_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_swap_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_swap_proto_rawDesc), len(file_api_walletadmin_v1_swap_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_swap_proto_rawDescData
}

var file_api_walletadmin_v1_swap_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_api_walletadmin_v1_swap_proto_goTypes = []any{
	(*SwapChannel)(nil),              // 0: api.walletadmin.v1.SwapChannel
	(*SwappableToken)(nil),           // 1: api.walletadmin.v1.SwappableToken
	(*SwapRecord)(nil),               // 2: api.walletadmin.v1.SwapRecord
	(*SwapToken)(nil),                // 3: api.walletadmin.v1.SwapToken
	(*SwapDetail)(nil),               // 4: api.walletadmin.v1.SwapDetail
	(*ListSwapChannelRequest)(nil),   // 5: api.walletadmin.v1.ListSwapChannelRequest
	(*ListSwapChannelReply)(nil),     // 6: api.walletadmin.v1.ListSwapChannelReply
	(*UpdateSwapChannelRequest)(nil), // 7: api.walletadmin.v1.UpdateSwapChannelRequest
	(*UpdateSwapChannelReply)(nil),   // 8: api.walletadmin.v1.UpdateSwapChannelReply
	(*ListTokenRequest)(nil),         // 9: api.walletadmin.v1.ListTokenRequest
	(*ListTokenReply)(nil),           // 10: api.walletadmin.v1.ListTokenReply
	(*UpdateTokenRequest)(nil),       // 11: api.walletadmin.v1.UpdateTokenRequest
	(*UpdateTokenReply)(nil),         // 12: api.walletadmin.v1.UpdateTokenReply
	(*ListHotTokenRequest)(nil),      // 13: api.walletadmin.v1.ListHotTokenRequest
	(*ListHotTokenReply)(nil),        // 14: api.walletadmin.v1.ListHotTokenReply
	(*CreateHotTokenRequest)(nil),    // 15: api.walletadmin.v1.CreateHotTokenRequest
	(*CreateHotTokenReply)(nil),      // 16: api.walletadmin.v1.CreateHotTokenReply
	(*DeleteHotTokenRequest)(nil),    // 17: api.walletadmin.v1.DeleteHotTokenRequest
	(*DeleteHotTokenReply)(nil),      // 18: api.walletadmin.v1.DeleteHotTokenReply
	(*GetSwapRecordRequest)(nil),     // 19: api.walletadmin.v1.GetSwapRecordRequest
	(*ListSwapRecordRequest)(nil),    // 20: api.walletadmin.v1.ListSwapRecordRequest
	(*ListSwapRecordReply)(nil),      // 21: api.walletadmin.v1.ListSwapRecordReply
	(*CommonSortReq)(nil),            // 22: api.walletadmin.v1.CommonSortReq
	(*CommonSortReply)(nil),          // 23: api.walletadmin.v1.CommonSortReply
}
var file_api_walletadmin_v1_swap_proto_depIdxs = []int32{
	3,  // 0: api.walletadmin.v1.SwapRecord.from:type_name -> api.walletadmin.v1.SwapToken
	3,  // 1: api.walletadmin.v1.SwapRecord.to:type_name -> api.walletadmin.v1.SwapToken
	4,  // 2: api.walletadmin.v1.SwapRecord.details:type_name -> api.walletadmin.v1.SwapDetail
	0,  // 3: api.walletadmin.v1.ListSwapChannelReply.list:type_name -> api.walletadmin.v1.SwapChannel
	1,  // 4: api.walletadmin.v1.ListTokenReply.list:type_name -> api.walletadmin.v1.SwappableToken
	1,  // 5: api.walletadmin.v1.ListHotTokenReply.list:type_name -> api.walletadmin.v1.SwappableToken
	2,  // 6: api.walletadmin.v1.ListSwapRecordReply.list:type_name -> api.walletadmin.v1.SwapRecord
	5,  // 7: api.walletadmin.v1.SwapService.ListSwapChannel:input_type -> api.walletadmin.v1.ListSwapChannelRequest
	7,  // 8: api.walletadmin.v1.SwapService.UpdateSwapChannel:input_type -> api.walletadmin.v1.UpdateSwapChannelRequest
	9,  // 9: api.walletadmin.v1.SwapService.ListToken:input_type -> api.walletadmin.v1.ListTokenRequest
	11, // 10: api.walletadmin.v1.SwapService.UpdateToken:input_type -> api.walletadmin.v1.UpdateTokenRequest
	13, // 11: api.walletadmin.v1.SwapService.ListHotToken:input_type -> api.walletadmin.v1.ListHotTokenRequest
	15, // 12: api.walletadmin.v1.SwapService.CreateHotToken:input_type -> api.walletadmin.v1.CreateHotTokenRequest
	17, // 13: api.walletadmin.v1.SwapService.DeleteHotToken:input_type -> api.walletadmin.v1.DeleteHotTokenRequest
	22, // 14: api.walletadmin.v1.SwapService.SortHotToken:input_type -> api.walletadmin.v1.CommonSortReq
	19, // 15: api.walletadmin.v1.SwapService.GetSwapRecord:input_type -> api.walletadmin.v1.GetSwapRecordRequest
	20, // 16: api.walletadmin.v1.SwapService.ListSwapRecord:input_type -> api.walletadmin.v1.ListSwapRecordRequest
	6,  // 17: api.walletadmin.v1.SwapService.ListSwapChannel:output_type -> api.walletadmin.v1.ListSwapChannelReply
	8,  // 18: api.walletadmin.v1.SwapService.UpdateSwapChannel:output_type -> api.walletadmin.v1.UpdateSwapChannelReply
	10, // 19: api.walletadmin.v1.SwapService.ListToken:output_type -> api.walletadmin.v1.ListTokenReply
	12, // 20: api.walletadmin.v1.SwapService.UpdateToken:output_type -> api.walletadmin.v1.UpdateTokenReply
	14, // 21: api.walletadmin.v1.SwapService.ListHotToken:output_type -> api.walletadmin.v1.ListHotTokenReply
	16, // 22: api.walletadmin.v1.SwapService.CreateHotToken:output_type -> api.walletadmin.v1.CreateHotTokenReply
	18, // 23: api.walletadmin.v1.SwapService.DeleteHotToken:output_type -> api.walletadmin.v1.DeleteHotTokenReply
	23, // 24: api.walletadmin.v1.SwapService.SortHotToken:output_type -> api.walletadmin.v1.CommonSortReply
	2,  // 25: api.walletadmin.v1.SwapService.GetSwapRecord:output_type -> api.walletadmin.v1.SwapRecord
	21, // 26: api.walletadmin.v1.SwapService.ListSwapRecord:output_type -> api.walletadmin.v1.ListSwapRecordReply
	17, // [17:27] is the sub-list for method output_type
	7,  // [7:17] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_swap_proto_init() }
func file_api_walletadmin_v1_swap_proto_init() {
	if File_api_walletadmin_v1_swap_proto != nil {
		return
	}
	file_api_walletadmin_v1_sort_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_swap_proto_rawDesc), len(file_api_walletadmin_v1_swap_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_swap_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_swap_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_swap_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_swap_proto = out.File
	file_api_walletadmin_v1_swap_proto_goTypes = nil
	file_api_walletadmin_v1_swap_proto_depIdxs = nil
}
