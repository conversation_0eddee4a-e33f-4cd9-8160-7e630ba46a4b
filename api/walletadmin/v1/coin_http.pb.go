// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/walletadmin/v1/coin.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationCoinSrvCreateCoinStar = "/api.walletadmin.v1.CoinSrv/CreateCoinStar"
const OperationCoinSrvDeleteCoinStar = "/api.walletadmin.v1.CoinSrv/DeleteCoinStar"
const OperationCoinSrvListCoin = "/api.walletadmin.v1.CoinSrv/ListCoin"
const OperationCoinSrvListCoinStar = "/api.walletadmin.v1.CoinSrv/ListCoinStar"
const OperationCoinSrvUpdateCoin = "/api.walletadmin.v1.CoinSrv/UpdateCoin"
const OperationCoinSrvUpdateCoinStarSort = "/api.walletadmin.v1.CoinSrv/UpdateCoinStarSort"

type CoinSrvHTTPServer interface {
	// CreateCoinStar 加入添加货币列表
	CreateCoinStar(context.Context, *CreateCoinStarReq) (*emptypb.Empty, error)
	// DeleteCoinStar 移除添加货币列表
	DeleteCoinStar(context.Context, *DeleteCoinStarReq) (*emptypb.Empty, error)
	// ListCoin 币种列表
	ListCoin(context.Context, *ListCoinReq) (*ListCoinReply, error)
	// ListCoinStar 添加货币列表
	ListCoinStar(context.Context, *ListCoinStarReq) (*ListCoinStarReply, error)
	// UpdateCoin 更新币种
	UpdateCoin(context.Context, *UpdateCoinReq) (*emptypb.Empty, error)
	// UpdateCoinStarSort 更新添加货币列表排序
	UpdateCoinStarSort(context.Context, *UpdateCoinStarSortReq) (*emptypb.Empty, error)
}

func RegisterCoinSrvHTTPServer(s *http.Server, srv CoinSrvHTTPServer) {
	r := s.Route("/")
	r.GET("/admin/v1/coin/list_coin", _CoinSrv_ListCoin0_HTTP_Handler(srv))
	r.PUT("/admin/v1/coin/update_coin", _CoinSrv_UpdateCoin0_HTTP_Handler(srv))
	r.POST("/admin/v1/coin/create_coin_star", _CoinSrv_CreateCoinStar0_HTTP_Handler(srv))
	r.GET("/admin/v1/coin/list_coin_star", _CoinSrv_ListCoinStar0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/coin/delete_coin_star", _CoinSrv_DeleteCoinStar0_HTTP_Handler(srv))
	r.PATCH("/admin/v1/coin/update_coin_star_sort", _CoinSrv_UpdateCoinStarSort0_HTTP_Handler(srv))
}

func _CoinSrv_ListCoin0_HTTP_Handler(srv CoinSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListCoinReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCoinSrvListCoin)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCoin(ctx, req.(*ListCoinReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListCoinReply)
		return ctx.Result(200, reply)
	}
}

func _CoinSrv_UpdateCoin0_HTTP_Handler(srv CoinSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateCoinReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCoinSrvUpdateCoin)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateCoin(ctx, req.(*UpdateCoinReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _CoinSrv_CreateCoinStar0_HTTP_Handler(srv CoinSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateCoinStarReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCoinSrvCreateCoinStar)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateCoinStar(ctx, req.(*CreateCoinStarReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _CoinSrv_ListCoinStar0_HTTP_Handler(srv CoinSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListCoinStarReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCoinSrvListCoinStar)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCoinStar(ctx, req.(*ListCoinStarReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListCoinStarReply)
		return ctx.Result(200, reply)
	}
}

func _CoinSrv_DeleteCoinStar0_HTTP_Handler(srv CoinSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteCoinStarReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCoinSrvDeleteCoinStar)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteCoinStar(ctx, req.(*DeleteCoinStarReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _CoinSrv_UpdateCoinStarSort0_HTTP_Handler(srv CoinSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateCoinStarSortReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCoinSrvUpdateCoinStarSort)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateCoinStarSort(ctx, req.(*UpdateCoinStarSortReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

type CoinSrvHTTPClient interface {
	CreateCoinStar(ctx context.Context, req *CreateCoinStarReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteCoinStar(ctx context.Context, req *DeleteCoinStarReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ListCoin(ctx context.Context, req *ListCoinReq, opts ...http.CallOption) (rsp *ListCoinReply, err error)
	ListCoinStar(ctx context.Context, req *ListCoinStarReq, opts ...http.CallOption) (rsp *ListCoinStarReply, err error)
	UpdateCoin(ctx context.Context, req *UpdateCoinReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateCoinStarSort(ctx context.Context, req *UpdateCoinStarSortReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type CoinSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewCoinSrvHTTPClient(client *http.Client) CoinSrvHTTPClient {
	return &CoinSrvHTTPClientImpl{client}
}

func (c *CoinSrvHTTPClientImpl) CreateCoinStar(ctx context.Context, in *CreateCoinStarReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/coin/create_coin_star"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCoinSrvCreateCoinStar))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CoinSrvHTTPClientImpl) DeleteCoinStar(ctx context.Context, in *DeleteCoinStarReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/coin/delete_coin_star"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCoinSrvDeleteCoinStar))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CoinSrvHTTPClientImpl) ListCoin(ctx context.Context, in *ListCoinReq, opts ...http.CallOption) (*ListCoinReply, error) {
	var out ListCoinReply
	pattern := "/admin/v1/coin/list_coin"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCoinSrvListCoin))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CoinSrvHTTPClientImpl) ListCoinStar(ctx context.Context, in *ListCoinStarReq, opts ...http.CallOption) (*ListCoinStarReply, error) {
	var out ListCoinStarReply
	pattern := "/admin/v1/coin/list_coin_star"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCoinSrvListCoinStar))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CoinSrvHTTPClientImpl) UpdateCoin(ctx context.Context, in *UpdateCoinReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/coin/update_coin"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCoinSrvUpdateCoin))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *CoinSrvHTTPClientImpl) UpdateCoinStarSort(ctx context.Context, in *UpdateCoinStarSortReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/coin/update_coin_star_sort"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationCoinSrvUpdateCoinStarSort))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PATCH", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
