// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/walletadmin/v1/file.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationFileSrvGeneratePresignedRequest = "/api.walletadmin.v1.FileSrv/GeneratePresignedRequest"

type FileSrvHTTPServer interface {
	// GeneratePresignedRequest GeneratePresignedRequest 生成预签名的请求
	// 相关文档: https://docs.aws.amazon.com/AmazonS3/latest/API/s3_example_s3_Scenario_PresignedUrl_section.html
	GeneratePresignedRequest(context.Context, *GeneratePresignedRequestReq) (*GeneratePresignedRequestReply, error)
}

func RegisterFileSrvHTTPServer(s *http.Server, srv FileSrvHTTPServer) {
	r := s.Route("/")
	r.POST("/admin/v1/presign", _FileSrv_GeneratePresignedRequest0_HTTP_Handler(srv))
}

func _FileSrv_GeneratePresignedRequest0_HTTP_Handler(srv FileSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GeneratePresignedRequestReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFileSrvGeneratePresignedRequest)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GeneratePresignedRequest(ctx, req.(*GeneratePresignedRequestReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GeneratePresignedRequestReply)
		return ctx.Result(200, reply)
	}
}

type FileSrvHTTPClient interface {
	GeneratePresignedRequest(ctx context.Context, req *GeneratePresignedRequestReq, opts ...http.CallOption) (rsp *GeneratePresignedRequestReply, err error)
}

type FileSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewFileSrvHTTPClient(client *http.Client) FileSrvHTTPClient {
	return &FileSrvHTTPClientImpl{client}
}

func (c *FileSrvHTTPClientImpl) GeneratePresignedRequest(ctx context.Context, in *GeneratePresignedRequestReq, opts ...http.CallOption) (*GeneratePresignedRequestReply, error) {
	var out GeneratePresignedRequestReply
	pattern := "/admin/v1/presign"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFileSrvGeneratePresignedRequest))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
