// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/walletadmin/v1/app_version.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAppVersionServiceCreateAppVersion = "/api.walletadmin.v1.AppVersionService/CreateAppVersion"
const OperationAppVersionServiceDeleteAppVersion = "/api.walletadmin.v1.AppVersionService/DeleteAppVersion"
const OperationAppVersionServiceGetAppVersion = "/api.walletadmin.v1.AppVersionService/GetAppVersion"
const OperationAppVersionServiceListAppVersions = "/api.walletadmin.v1.AppVersionService/ListAppVersions"
const OperationAppVersionServiceListPublishedVersions = "/api.walletadmin.v1.AppVersionService/ListPublishedVersions"
const OperationAppVersionServiceUpdateAppVersion = "/api.walletadmin.v1.AppVersionService/UpdateAppVersion"

type AppVersionServiceHTTPServer interface {
	// CreateAppVersion 创建版本
	CreateAppVersion(context.Context, *CreateAppVersionReq) (*CreateAppVersionReply, error)
	// DeleteAppVersion 删除版本
	DeleteAppVersion(context.Context, *DeleteAppVersionReq) (*emptypb.Empty, error)
	// GetAppVersion 获取版本详情
	GetAppVersion(context.Context, *GetAppVersionReq) (*AppVersionInfo, error)
	// ListAppVersions 版本列表
	ListAppVersions(context.Context, *ListAppVersionsReq) (*ListAppVersionsReply, error)
	// ListPublishedVersions 获取已发布版本列表
	ListPublishedVersions(context.Context, *ListPublishedVersionsReq) (*ListPublishedVersionsReply, error)
	// UpdateAppVersion 更新版本
	UpdateAppVersion(context.Context, *UpdateAppVersionReq) (*emptypb.Empty, error)
}

func RegisterAppVersionServiceHTTPServer(s *http.Server, srv AppVersionServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/admin/v1/app-versions", _AppVersionService_CreateAppVersion0_HTTP_Handler(srv))
	r.PUT("/admin/v1/app-versions/{id}", _AppVersionService_UpdateAppVersion0_HTTP_Handler(srv))
	r.DELETE("/admin/v1/app-versions/{id}", _AppVersionService_DeleteAppVersion0_HTTP_Handler(srv))
	r.GET("/admin/v1/app-versions", _AppVersionService_ListAppVersions0_HTTP_Handler(srv))
	r.GET("/admin/v1/app-versions/{id}", _AppVersionService_GetAppVersion0_HTTP_Handler(srv))
	r.POST("/admin/v1/app-versions/published", _AppVersionService_ListPublishedVersions0_HTTP_Handler(srv))
}

func _AppVersionService_CreateAppVersion0_HTTP_Handler(srv AppVersionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateAppVersionReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAppVersionServiceCreateAppVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAppVersion(ctx, req.(*CreateAppVersionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateAppVersionReply)
		return ctx.Result(200, reply)
	}
}

func _AppVersionService_UpdateAppVersion0_HTTP_Handler(srv AppVersionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateAppVersionReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAppVersionServiceUpdateAppVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAppVersion(ctx, req.(*UpdateAppVersionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _AppVersionService_DeleteAppVersion0_HTTP_Handler(srv AppVersionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteAppVersionReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAppVersionServiceDeleteAppVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteAppVersion(ctx, req.(*DeleteAppVersionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _AppVersionService_ListAppVersions0_HTTP_Handler(srv AppVersionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListAppVersionsReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAppVersionServiceListAppVersions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListAppVersions(ctx, req.(*ListAppVersionsReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListAppVersionsReply)
		return ctx.Result(200, reply)
	}
}

func _AppVersionService_GetAppVersion0_HTTP_Handler(srv AppVersionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAppVersionReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAppVersionServiceGetAppVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAppVersion(ctx, req.(*GetAppVersionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AppVersionInfo)
		return ctx.Result(200, reply)
	}
}

func _AppVersionService_ListPublishedVersions0_HTTP_Handler(srv AppVersionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListPublishedVersionsReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAppVersionServiceListPublishedVersions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListPublishedVersions(ctx, req.(*ListPublishedVersionsReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListPublishedVersionsReply)
		return ctx.Result(200, reply)
	}
}

type AppVersionServiceHTTPClient interface {
	CreateAppVersion(ctx context.Context, req *CreateAppVersionReq, opts ...http.CallOption) (rsp *CreateAppVersionReply, err error)
	DeleteAppVersion(ctx context.Context, req *DeleteAppVersionReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	GetAppVersion(ctx context.Context, req *GetAppVersionReq, opts ...http.CallOption) (rsp *AppVersionInfo, err error)
	ListAppVersions(ctx context.Context, req *ListAppVersionsReq, opts ...http.CallOption) (rsp *ListAppVersionsReply, err error)
	ListPublishedVersions(ctx context.Context, req *ListPublishedVersionsReq, opts ...http.CallOption) (rsp *ListPublishedVersionsReply, err error)
	UpdateAppVersion(ctx context.Context, req *UpdateAppVersionReq, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
}

type AppVersionServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewAppVersionServiceHTTPClient(client *http.Client) AppVersionServiceHTTPClient {
	return &AppVersionServiceHTTPClientImpl{client}
}

func (c *AppVersionServiceHTTPClientImpl) CreateAppVersion(ctx context.Context, in *CreateAppVersionReq, opts ...http.CallOption) (*CreateAppVersionReply, error) {
	var out CreateAppVersionReply
	pattern := "/admin/v1/app-versions"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAppVersionServiceCreateAppVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AppVersionServiceHTTPClientImpl) DeleteAppVersion(ctx context.Context, in *DeleteAppVersionReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/app-versions/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAppVersionServiceDeleteAppVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AppVersionServiceHTTPClientImpl) GetAppVersion(ctx context.Context, in *GetAppVersionReq, opts ...http.CallOption) (*AppVersionInfo, error) {
	var out AppVersionInfo
	pattern := "/admin/v1/app-versions/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAppVersionServiceGetAppVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AppVersionServiceHTTPClientImpl) ListAppVersions(ctx context.Context, in *ListAppVersionsReq, opts ...http.CallOption) (*ListAppVersionsReply, error) {
	var out ListAppVersionsReply
	pattern := "/admin/v1/app-versions"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAppVersionServiceListAppVersions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AppVersionServiceHTTPClientImpl) ListPublishedVersions(ctx context.Context, in *ListPublishedVersionsReq, opts ...http.CallOption) (*ListPublishedVersionsReply, error) {
	var out ListPublishedVersionsReply
	pattern := "/admin/v1/app-versions/published"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAppVersionServiceListPublishedVersions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AppVersionServiceHTTPClientImpl) UpdateAppVersion(ctx context.Context, in *UpdateAppVersionReq, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/admin/v1/app-versions/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAppVersionServiceUpdateAppVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
