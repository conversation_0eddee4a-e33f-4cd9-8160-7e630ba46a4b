// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/admin.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoginReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginReq) Reset() {
	*x = LoginReq{}
	mi := &file_api_walletadmin_v1_admin_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReq) ProtoMessage() {}

func (x *LoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_admin_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReq.ProtoReflect.Descriptor instead.
func (*LoginReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_admin_proto_rawDescGZIP(), []int{0}
}

func (x *LoginReq) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *LoginReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type LoginReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginReply) Reset() {
	*x = LoginReply{}
	mi := &file_api_walletadmin_v1_admin_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReply) ProtoMessage() {}

func (x *LoginReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_admin_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReply.ProtoReflect.Descriptor instead.
func (*LoginReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_admin_proto_rawDescGZIP(), []int{1}
}

func (x *LoginReply) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

var File_api_walletadmin_v1_admin_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_admin_proto_rawDesc = "" +
	"\n" +
	"\x1eapi/walletadmin/v1/admin.proto\x12\x12api.walletadmin.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\"T\n" +
	"\bLoginReq\x12#\n" +
	"\busername\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\busername\x12#\n" +
	"\bpassword\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bpassword\"\"\n" +
	"\n" +
	"LoginReply\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token2s\n" +
	"\bAdminSrv\x12g\n" +
	"\x05Login\x12\x1c.api.walletadmin.v1.LoginReq\x1a\x1e.api.walletadmin.v1.LoginReply\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/admin/v1/admin/loginB\xb0\x01\n" +
	"\x16com.api.walletadmin.v1B\n" +
	"AdminProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_admin_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_admin_proto_rawDescData []byte
)

func file_api_walletadmin_v1_admin_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_admin_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_admin_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_admin_proto_rawDesc), len(file_api_walletadmin_v1_admin_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_admin_proto_rawDescData
}

var file_api_walletadmin_v1_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_walletadmin_v1_admin_proto_goTypes = []any{
	(*LoginReq)(nil),   // 0: api.walletadmin.v1.LoginReq
	(*LoginReply)(nil), // 1: api.walletadmin.v1.LoginReply
}
var file_api_walletadmin_v1_admin_proto_depIdxs = []int32{
	0, // 0: api.walletadmin.v1.AdminSrv.Login:input_type -> api.walletadmin.v1.LoginReq
	1, // 1: api.walletadmin.v1.AdminSrv.Login:output_type -> api.walletadmin.v1.LoginReply
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_admin_proto_init() }
func file_api_walletadmin_v1_admin_proto_init() {
	if File_api_walletadmin_v1_admin_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_admin_proto_rawDesc), len(file_api_walletadmin_v1_admin_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_admin_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_admin_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_admin_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_admin_proto = out.File
	file_api_walletadmin_v1_admin_proto_goTypes = nil
	file_api_walletadmin_v1_admin_proto_depIdxs = nil
}
