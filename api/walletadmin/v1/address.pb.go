// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/walletadmin/v1/address.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListAddressReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 页码
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 页容量
	PageSize int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 链索引(传值-1查询全部链)
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 地址
	Address       *string `protobuf:"bytes,4,opt,name=address,proto3,oneof" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddressReq) Reset() {
	*x = ListAddressReq{}
	mi := &file_api_walletadmin_v1_address_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressReq) ProtoMessage() {}

func (x *ListAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_address_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressReq.ProtoReflect.Descriptor instead.
func (*ListAddressReq) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_address_proto_rawDescGZIP(), []int{0}
}

func (x *ListAddressReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListAddressReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAddressReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListAddressReq) GetAddress() string {
	if x != nil && x.Address != nil {
		return *x.Address
	}
	return ""
}

type Address struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// BOSS ID
	BossId string `protobuf:"bytes,2,opt,name=boss_id,json=bossId,proto3" json:"boss_id,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 地址
	Address string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	// 创建时间(时间戳,单位秒)
	CreatedAt int64 `protobuf:"varint,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 链名称
	ChainName     string `protobuf:"bytes,6,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Address) Reset() {
	*x = Address{}
	mi := &file_api_walletadmin_v1_address_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Address) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Address) ProtoMessage() {}

func (x *Address) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_address_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Address.ProtoReflect.Descriptor instead.
func (*Address) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_address_proto_rawDescGZIP(), []int{1}
}

func (x *Address) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Address) GetBossId() string {
	if x != nil {
		return x.BossId
	}
	return ""
}

func (x *Address) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Address) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Address) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *Address) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

type ListAddressReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*Address             `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总记录数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddressReply) Reset() {
	*x = ListAddressReply{}
	mi := &file_api_walletadmin_v1_address_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressReply) ProtoMessage() {}

func (x *ListAddressReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_walletadmin_v1_address_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressReply.ProtoReflect.Descriptor instead.
func (*ListAddressReply) Descriptor() ([]byte, []int) {
	return file_api_walletadmin_v1_address_proto_rawDescGZIP(), []int{2}
}

func (x *ListAddressReply) GetList() []*Address {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListAddressReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

var File_api_walletadmin_v1_address_proto protoreflect.FileDescriptor

const file_api_walletadmin_v1_address_proto_rawDesc = "" +
	"\n" +
	" api/walletadmin/v1/address.proto\x12\x12api.walletadmin.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\"\xa1\x01\n" +
	"\x0eListAddressReq\x12\x1b\n" +
	"\x04page\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12&\n" +
	"\tpage_size\x18\x02 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\bpageSize\x12\x1f\n" +
	"\vchain_index\x18\x03 \x01(\x03R\n" +
	"chainIndex\x12\x1d\n" +
	"\aaddress\x18\x04 \x01(\tH\x00R\aaddress\x88\x01\x01B\n" +
	"\n" +
	"\b_address\"\xab\x01\n" +
	"\aAddress\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x17\n" +
	"\aboss_id\x18\x02 \x01(\tR\x06bossId\x12\x1f\n" +
	"\vchain_index\x18\x03 \x01(\x03R\n" +
	"chainIndex\x12\x18\n" +
	"\aaddress\x18\x04 \x01(\tR\aaddress\x12\x1d\n" +
	"\n" +
	"created_at\x18\x05 \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"chain_name\x18\x06 \x01(\tR\tchainName\"d\n" +
	"\x10ListAddressReply\x12/\n" +
	"\x04list\x18\x01 \x03(\v2\x1b.api.walletadmin.v1.AddressR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount2\x8d\x01\n" +
	"\n" +
	"AddressSrv\x12\x7f\n" +
	"\vListAddress\x12\".api.walletadmin.v1.ListAddressReq\x1a$.api.walletadmin.v1.ListAddressReply\"&\x82\xd3\xe4\x93\x02 \x12\x1e/admin/v1/address/list_addressB\xb2\x01\n" +
	"\x16com.api.walletadmin.v1B\fAddressProtoP\x01Z byd_wallet/api/walletadmin/v1;v1\xa2\x02\x03AWX\xaa\x02\x12Api.Walletadmin.V1\xca\x02\x12Api\\Walletadmin\\V1\xe2\x02\x1eApi\\Walletadmin\\V1\\GPBMetadata\xea\x02\x14Api::Walletadmin::V1b\x06proto3"

var (
	file_api_walletadmin_v1_address_proto_rawDescOnce sync.Once
	file_api_walletadmin_v1_address_proto_rawDescData []byte
)

func file_api_walletadmin_v1_address_proto_rawDescGZIP() []byte {
	file_api_walletadmin_v1_address_proto_rawDescOnce.Do(func() {
		file_api_walletadmin_v1_address_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_address_proto_rawDesc), len(file_api_walletadmin_v1_address_proto_rawDesc)))
	})
	return file_api_walletadmin_v1_address_proto_rawDescData
}

var file_api_walletadmin_v1_address_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_walletadmin_v1_address_proto_goTypes = []any{
	(*ListAddressReq)(nil),   // 0: api.walletadmin.v1.ListAddressReq
	(*Address)(nil),          // 1: api.walletadmin.v1.Address
	(*ListAddressReply)(nil), // 2: api.walletadmin.v1.ListAddressReply
}
var file_api_walletadmin_v1_address_proto_depIdxs = []int32{
	1, // 0: api.walletadmin.v1.ListAddressReply.list:type_name -> api.walletadmin.v1.Address
	0, // 1: api.walletadmin.v1.AddressSrv.ListAddress:input_type -> api.walletadmin.v1.ListAddressReq
	2, // 2: api.walletadmin.v1.AddressSrv.ListAddress:output_type -> api.walletadmin.v1.ListAddressReply
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_walletadmin_v1_address_proto_init() }
func file_api_walletadmin_v1_address_proto_init() {
	if File_api_walletadmin_v1_address_proto != nil {
		return
	}
	file_api_walletadmin_v1_address_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_walletadmin_v1_address_proto_rawDesc), len(file_api_walletadmin_v1_address_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_walletadmin_v1_address_proto_goTypes,
		DependencyIndexes: file_api_walletadmin_v1_address_proto_depIdxs,
		MessageInfos:      file_api_walletadmin_v1_address_proto_msgTypes,
	}.Build()
	File_api_walletadmin_v1_address_proto = out.File
	file_api_walletadmin_v1_address_proto_goTypes = nil
	file_api_walletadmin_v1_address_proto_depIdxs = nil
}
