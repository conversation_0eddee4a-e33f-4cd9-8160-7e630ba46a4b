// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/rent.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UploadHashReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadHashReply) Reset() {
	*x = UploadHashReply{}
	mi := &file_api_wallet_v1_rent_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadHashReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadHashReply) ProtoMessage() {}

func (x *UploadHashReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadHashReply.ProtoReflect.Descriptor instead.
func (*UploadHashReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{0}
}

type UploadHashReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderId       string                 `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	FromHash      string                 `protobuf:"bytes,2,opt,name=from_hash,json=fromHash,proto3" json:"from_hash,omitempty"`
	SignedData    string                 `protobuf:"bytes,3,opt,name=signed_data,json=signedData,proto3" json:"signed_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadHashReq) Reset() {
	*x = UploadHashReq{}
	mi := &file_api_wallet_v1_rent_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadHashReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadHashReq) ProtoMessage() {}

func (x *UploadHashReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadHashReq.ProtoReflect.Descriptor instead.
func (*UploadHashReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{1}
}

func (x *UploadHashReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *UploadHashReq) GetFromHash() string {
	if x != nil {
		return x.FromHash
	}
	return ""
}

func (x *UploadHashReq) GetSignedData() string {
	if x != nil {
		return x.SignedData
	}
	return ""
}

type AddTronRentRecordReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户地址（创建买单的钱包地址）
	FromAddress string `protobuf:"bytes,1,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 能量/带宽接收地址
	PledgeAddress string `protobuf:"bytes,2,opt,name=pledge_address,json=pledgeAddress,proto3" json:"pledge_address,omitempty"`
	// 能量数量（需大于32000）
	PledgeNum     int64 `protobuf:"varint,3,opt,name=pledge_num,json=pledgeNum,proto3" json:"pledge_num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddTronRentRecordReq) Reset() {
	*x = AddTronRentRecordReq{}
	mi := &file_api_wallet_v1_rent_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTronRentRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTronRentRecordReq) ProtoMessage() {}

func (x *AddTronRentRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTronRentRecordReq.ProtoReflect.Descriptor instead.
func (*AddTronRentRecordReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{2}
}

func (x *AddTronRentRecordReq) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *AddTronRentRecordReq) GetPledgeAddress() string {
	if x != nil {
		return x.PledgeAddress
	}
	return ""
}

func (x *AddTronRentRecordReq) GetPledgeNum() int64 {
	if x != nil {
		return x.PledgeNum
	}
	return 0
}

type AddTronRentRecordReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderId       string                 `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Transaction   string                 `protobuf:"bytes,2,opt,name=transaction,proto3" json:"transaction,omitempty"`
	PledgeTrxNum  string                 `protobuf:"bytes,3,opt,name=pledge_trx_num,json=pledgeTrxNum,proto3" json:"pledge_trx_num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddTronRentRecordReply) Reset() {
	*x = AddTronRentRecordReply{}
	mi := &file_api_wallet_v1_rent_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTronRentRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTronRentRecordReply) ProtoMessage() {}

func (x *AddTronRentRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTronRentRecordReply.ProtoReflect.Descriptor instead.
func (*AddTronRentRecordReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{3}
}

func (x *AddTronRentRecordReply) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *AddTronRentRecordReply) GetTransaction() string {
	if x != nil {
		return x.Transaction
	}
	return ""
}

func (x *AddTronRentRecordReply) GetPledgeTrxNum() string {
	if x != nil {
		return x.PledgeTrxNum
	}
	return ""
}

type QueryPreorderInfoReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户地址（创建买单的钱包地址）
	FromAddress string `protobuf:"bytes,1,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 能量/带宽接收地址
	PledgeAddress string `protobuf:"bytes,2,opt,name=pledge_address,json=pledgeAddress,proto3" json:"pledge_address,omitempty"`
	// 能量数量（需大于32000）
	PledgeNum     int64 `protobuf:"varint,3,opt,name=pledge_num,json=pledgeNum,proto3" json:"pledge_num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryPreorderInfoReq) Reset() {
	*x = QueryPreorderInfoReq{}
	mi := &file_api_wallet_v1_rent_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryPreorderInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPreorderInfoReq) ProtoMessage() {}

func (x *QueryPreorderInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPreorderInfoReq.ProtoReflect.Descriptor instead.
func (*QueryPreorderInfoReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{4}
}

func (x *QueryPreorderInfoReq) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *QueryPreorderInfoReq) GetPledgeAddress() string {
	if x != nil {
		return x.PledgeAddress
	}
	return ""
}

func (x *QueryPreorderInfoReq) GetPledgeNum() int64 {
	if x != nil {
		return x.PledgeNum
	}
	return 0
}

type QueryPreorderInfoReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderPrice    string                 `protobuf:"bytes,1,opt,name=order_price,json=orderPrice,proto3" json:"order_price,omitempty"`
	PledgeNum     string                 `protobuf:"bytes,2,opt,name=pledge_num,json=pledgeNum,proto3" json:"pledge_num,omitempty"`
	PledgeTrxNum  string                 `protobuf:"bytes,3,opt,name=pledge_trx_num,json=pledgeTrxNum,proto3" json:"pledge_trx_num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryPreorderInfoReply) Reset() {
	*x = QueryPreorderInfoReply{}
	mi := &file_api_wallet_v1_rent_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryPreorderInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPreorderInfoReply) ProtoMessage() {}

func (x *QueryPreorderInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_rent_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPreorderInfoReply.ProtoReflect.Descriptor instead.
func (*QueryPreorderInfoReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_rent_proto_rawDescGZIP(), []int{5}
}

func (x *QueryPreorderInfoReply) GetOrderPrice() string {
	if x != nil {
		return x.OrderPrice
	}
	return ""
}

func (x *QueryPreorderInfoReply) GetPledgeNum() string {
	if x != nil {
		return x.PledgeNum
	}
	return ""
}

func (x *QueryPreorderInfoReply) GetPledgeTrxNum() string {
	if x != nil {
		return x.PledgeTrxNum
	}
	return ""
}

var File_api_wallet_v1_rent_proto protoreflect.FileDescriptor

const file_api_wallet_v1_rent_proto_rawDesc = "" +
	"\n" +
	"\x18api/wallet/v1/rent.proto\x12\rapi.wallet.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\"\x11\n" +
	"\x0fUploadHashReply\"\x83\x01\n" +
	"\rUploadHashReq\x12\"\n" +
	"\border_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\aorderId\x12$\n" +
	"\tfrom_hash\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bfromHash\x12(\n" +
	"\vsigned_data\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\n" +
	"signedData\"\x9a\x01\n" +
	"\x14AddTronRentRecordReq\x12*\n" +
	"\ffrom_address\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\vfromAddress\x12.\n" +
	"\x0epledge_address\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\rpledgeAddress\x12&\n" +
	"\n" +
	"pledge_num\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tpledgeNum\"{\n" +
	"\x16AddTronRentRecordReply\x12\x19\n" +
	"\border_id\x18\x01 \x01(\tR\aorderId\x12 \n" +
	"\vtransaction\x18\x02 \x01(\tR\vtransaction\x12$\n" +
	"\x0epledge_trx_num\x18\x03 \x01(\tR\fpledgeTrxNum\"\x9a\x01\n" +
	"\x14QueryPreorderInfoReq\x12*\n" +
	"\ffrom_address\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\vfromAddress\x12.\n" +
	"\x0epledge_address\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\rpledgeAddress\x12&\n" +
	"\n" +
	"pledge_num\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tpledgeNum\"~\n" +
	"\x16QueryPreorderInfoReply\x12\x1f\n" +
	"\vorder_price\x18\x01 \x01(\tR\n" +
	"orderPrice\x12\x1d\n" +
	"\n" +
	"pledge_num\x18\x02 \x01(\tR\tpledgeNum\x12$\n" +
	"\x0epledge_trx_num\x18\x03 \x01(\tR\fpledgeTrxNum2\xff\x02\n" +
	"\aTronSrv\x12}\n" +
	"\x11AddTronRentRecord\x12#.api.wallet.v1.AddTronRentRecordReq\x1a%.api.wallet.v1.AddTronRentRecordReply\"\x1c\x82\xd3\xe4\x93\x02\x16:\x01*\"\x11/v1/tron/rent/add\x12\x82\x01\n" +
	"\x11QueryPreorderInfo\x12#.api.wallet.v1.QueryPreorderInfoReq\x1a%.api.wallet.v1.QueryPreorderInfoReply\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/tron/rent/preorder\x12p\n" +
	"\n" +
	"UploadHash\x12\x1c.api.wallet.v1.UploadHashReq\x1a\x1e.api.wallet.v1.UploadHashReply\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/v1/tron/rent/upload_hashB\x91\x01\n" +
	"\x11com.api.wallet.v1B\tRentProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_rent_proto_rawDescOnce sync.Once
	file_api_wallet_v1_rent_proto_rawDescData []byte
)

func file_api_wallet_v1_rent_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_rent_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_rent_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_rent_proto_rawDesc), len(file_api_wallet_v1_rent_proto_rawDesc)))
	})
	return file_api_wallet_v1_rent_proto_rawDescData
}

var file_api_wallet_v1_rent_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_wallet_v1_rent_proto_goTypes = []any{
	(*UploadHashReply)(nil),        // 0: api.wallet.v1.UploadHashReply
	(*UploadHashReq)(nil),          // 1: api.wallet.v1.UploadHashReq
	(*AddTronRentRecordReq)(nil),   // 2: api.wallet.v1.AddTronRentRecordReq
	(*AddTronRentRecordReply)(nil), // 3: api.wallet.v1.AddTronRentRecordReply
	(*QueryPreorderInfoReq)(nil),   // 4: api.wallet.v1.QueryPreorderInfoReq
	(*QueryPreorderInfoReply)(nil), // 5: api.wallet.v1.QueryPreorderInfoReply
}
var file_api_wallet_v1_rent_proto_depIdxs = []int32{
	2, // 0: api.wallet.v1.TronSrv.AddTronRentRecord:input_type -> api.wallet.v1.AddTronRentRecordReq
	4, // 1: api.wallet.v1.TronSrv.QueryPreorderInfo:input_type -> api.wallet.v1.QueryPreorderInfoReq
	1, // 2: api.wallet.v1.TronSrv.UploadHash:input_type -> api.wallet.v1.UploadHashReq
	3, // 3: api.wallet.v1.TronSrv.AddTronRentRecord:output_type -> api.wallet.v1.AddTronRentRecordReply
	5, // 4: api.wallet.v1.TronSrv.QueryPreorderInfo:output_type -> api.wallet.v1.QueryPreorderInfoReply
	0, // 5: api.wallet.v1.TronSrv.UploadHash:output_type -> api.wallet.v1.UploadHashReply
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_rent_proto_init() }
func file_api_wallet_v1_rent_proto_init() {
	if File_api_wallet_v1_rent_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_rent_proto_rawDesc), len(file_api_wallet_v1_rent_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_rent_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_rent_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_rent_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_rent_proto = out.File
	file_api_wallet_v1_rent_proto_goTypes = nil
	file_api_wallet_v1_rent_proto_depIdxs = nil
}
