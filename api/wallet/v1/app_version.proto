syntax = "proto3";

package api.wallet.v1;

import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "api/walletadmin/v1/app_version.proto";

option go_package = "byd_wallet/api/wallet/v1;v1";

service AppVersionService {
  // 获取最新版本
  rpc GetLatestAppVersion(GetLatestAppVersionReq) returns (walletadmin.v1.AppVersionInfo) {
    option (google.api.http) = {get: "/v1/app-versions/latest"};
  }
}

// 获取最新版本请求
message GetLatestAppVersionReq {
  // APP类型。android,ios
  string app_type = 1 [(buf.validate.field).string.min_len = 1];
}

