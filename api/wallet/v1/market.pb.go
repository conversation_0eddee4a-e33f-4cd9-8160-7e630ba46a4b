// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/market.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TokenWithMarket struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 合约地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 币名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 币精度位数
	Decimals int64 `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 流动性
	CirculatingSupply string `protobuf:"bytes,6,opt,name=circulating_supply,json=circulatingSupply,proto3" json:"circulating_supply,omitempty"`
	// 24小时成交额
	TradingVolume_24H string `protobuf:"bytes,7,opt,name=trading_volume_24h,json=tradingVolume24h,proto3" json:"trading_volume_24h,omitempty"`
	// 24小时涨跌幅（百分比）
	PriceChangePercentage_24H string `protobuf:"bytes,8,opt,name=price_change_percentage_24h,json=priceChangePercentage24h,proto3" json:"price_change_percentage_24h,omitempty"`
	// 币图标
	LogoUrl string `protobuf:"bytes,9,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 币价格(usd)
	Price         string `protobuf:"bytes,10,opt,name=price,proto3" json:"price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenWithMarket) Reset() {
	*x = TokenWithMarket{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenWithMarket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenWithMarket) ProtoMessage() {}

func (x *TokenWithMarket) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenWithMarket.ProtoReflect.Descriptor instead.
func (*TokenWithMarket) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{0}
}

func (x *TokenWithMarket) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TokenWithMarket) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TokenWithMarket) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TokenWithMarket) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TokenWithMarket) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *TokenWithMarket) GetCirculatingSupply() string {
	if x != nil {
		return x.CirculatingSupply
	}
	return ""
}

func (x *TokenWithMarket) GetTradingVolume_24H() string {
	if x != nil {
		return x.TradingVolume_24H
	}
	return ""
}

func (x *TokenWithMarket) GetPriceChangePercentage_24H() string {
	if x != nil {
		return x.PriceChangePercentage_24H
	}
	return ""
}

func (x *TokenWithMarket) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *TokenWithMarket) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

type SearchTokenReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*TokenWithMarket     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchTokenReply) Reset() {
	*x = SearchTokenReply{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTokenReply) ProtoMessage() {}

func (x *SearchTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTokenReply.ProtoReflect.Descriptor instead.
func (*SearchTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{1}
}

func (x *SearchTokenReply) GetList() []*TokenWithMarket {
	if x != nil {
		return x.List
	}
	return nil
}

type ListPopularTokenReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 页码
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 页容量
	Limit int64 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// 关键字(搜索范围 代币符号 和 合约地址)
	Keyword *string `protobuf:"bytes,3,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// 链索引(0,60,...支持查询多个链,查询全部就传空或不传)
	ChainIndexes  string `protobuf:"bytes,4,opt,name=chain_indexes,json=chainIndexes,proto3" json:"chain_indexes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPopularTokenReq) Reset() {
	*x = ListPopularTokenReq{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPopularTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPopularTokenReq) ProtoMessage() {}

func (x *ListPopularTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPopularTokenReq.ProtoReflect.Descriptor instead.
func (*ListPopularTokenReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{2}
}

func (x *ListPopularTokenReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPopularTokenReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListPopularTokenReq) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *ListPopularTokenReq) GetChainIndexes() string {
	if x != nil {
		return x.ChainIndexes
	}
	return ""
}

type ListPopularTokenReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*TokenWithMarket     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总记录数
	TotalCount    int64 `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPopularTokenReply) Reset() {
	*x = ListPopularTokenReply{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPopularTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPopularTokenReply) ProtoMessage() {}

func (x *ListPopularTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPopularTokenReply.ProtoReflect.Descriptor instead.
func (*ListPopularTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{3}
}

func (x *ListPopularTokenReply) GetList() []*TokenWithMarket {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListPopularTokenReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type QuerySimpleKlineReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询指定的token
	TokenIds      []*TokenID `protobuf:"bytes,1,rep,name=token_ids,json=tokenIds,proto3" json:"token_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuerySimpleKlineReq) Reset() {
	*x = QuerySimpleKlineReq{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuerySimpleKlineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySimpleKlineReq) ProtoMessage() {}

func (x *QuerySimpleKlineReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySimpleKlineReq.ProtoReflect.Descriptor instead.
func (*QuerySimpleKlineReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{4}
}

func (x *QuerySimpleKlineReq) GetTokenIds() []*TokenID {
	if x != nil {
		return x.TokenIds
	}
	return nil
}

type SimpleKline struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 合约地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// k线时间（时间戳，单位秒） // 升序
	Times []int64 `protobuf:"varint,3,rep,packed,name=times,proto3" json:"times,omitempty"`
	// 收盘价(USD) // 按时间升序
	ClosePrices   []string `protobuf:"bytes,4,rep,name=close_prices,json=closePrices,proto3" json:"close_prices,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimpleKline) Reset() {
	*x = SimpleKline{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimpleKline) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleKline) ProtoMessage() {}

func (x *SimpleKline) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleKline.ProtoReflect.Descriptor instead.
func (*SimpleKline) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{5}
}

func (x *SimpleKline) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SimpleKline) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SimpleKline) GetTimes() []int64 {
	if x != nil {
		return x.Times
	}
	return nil
}

func (x *SimpleKline) GetClosePrices() []string {
	if x != nil {
		return x.ClosePrices
	}
	return nil
}

type QuerySimpleKlineReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*SimpleKline         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuerySimpleKlineReply) Reset() {
	*x = QuerySimpleKlineReply{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuerySimpleKlineReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySimpleKlineReply) ProtoMessage() {}

func (x *QuerySimpleKlineReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySimpleKlineReply.ProtoReflect.Descriptor instead.
func (*QuerySimpleKlineReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{6}
}

func (x *QuerySimpleKlineReply) GetList() []*SimpleKline {
	if x != nil {
		return x.List
	}
	return nil
}

type QueryCurrencyUSDRateReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 法币 // cny,jpy,...
	Currencies    string `protobuf:"bytes,1,opt,name=currencies,proto3" json:"currencies,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryCurrencyUSDRateReq) Reset() {
	*x = QueryCurrencyUSDRateReq{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryCurrencyUSDRateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCurrencyUSDRateReq) ProtoMessage() {}

func (x *QueryCurrencyUSDRateReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCurrencyUSDRateReq.ProtoReflect.Descriptor instead.
func (*QueryCurrencyUSDRateReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{7}
}

func (x *QueryCurrencyUSDRateReq) GetCurrencies() string {
	if x != nil {
		return x.Currencies
	}
	return ""
}

type CurrencyUSDRate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// key:法币(cny,jpy等小写) value:汇率
	Value         map[string]string `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CurrencyUSDRate) Reset() {
	*x = CurrencyUSDRate{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CurrencyUSDRate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CurrencyUSDRate) ProtoMessage() {}

func (x *CurrencyUSDRate) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CurrencyUSDRate.ProtoReflect.Descriptor instead.
func (*CurrencyUSDRate) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{8}
}

func (x *CurrencyUSDRate) GetValue() map[string]string {
	if x != nil {
		return x.Value
	}
	return nil
}

type TokenID struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 合约地址
	Address       string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenID) Reset() {
	*x = TokenID{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenID) ProtoMessage() {}

func (x *TokenID) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenID.ProtoReflect.Descriptor instead.
func (*TokenID) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{9}
}

func (x *TokenID) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TokenID) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type QueryCoinPriceReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询指定的token
	TokenIds      []*TokenID `protobuf:"bytes,1,rep,name=token_ids,json=tokenIds,proto3" json:"token_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryCoinPriceReq) Reset() {
	*x = QueryCoinPriceReq{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryCoinPriceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCoinPriceReq) ProtoMessage() {}

func (x *QueryCoinPriceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCoinPriceReq.ProtoReflect.Descriptor instead.
func (*QueryCoinPriceReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{10}
}

func (x *QueryCoinPriceReq) GetTokenIds() []*TokenID {
	if x != nil {
		return x.TokenIds
	}
	return nil
}

type CoinPrice struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 合约地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 价格(USD)
	Price string `protobuf:"bytes,3,opt,name=price,proto3" json:"price,omitempty"`
	// 更新时间（时间戳，单位秒）
	LastUpdatedAt int64 `protobuf:"varint,4,opt,name=last_updated_at,json=lastUpdatedAt,proto3" json:"last_updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CoinPrice) Reset() {
	*x = CoinPrice{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CoinPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoinPrice) ProtoMessage() {}

func (x *CoinPrice) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoinPrice.ProtoReflect.Descriptor instead.
func (*CoinPrice) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{11}
}

func (x *CoinPrice) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *CoinPrice) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *CoinPrice) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *CoinPrice) GetLastUpdatedAt() int64 {
	if x != nil {
		return x.LastUpdatedAt
	}
	return 0
}

type QueryCoinPriceReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*CoinPrice           `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryCoinPriceReply) Reset() {
	*x = QueryCoinPriceReply{}
	mi := &file_api_wallet_v1_market_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryCoinPriceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCoinPriceReply) ProtoMessage() {}

func (x *QueryCoinPriceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_market_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCoinPriceReply.ProtoReflect.Descriptor instead.
func (*QueryCoinPriceReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_market_proto_rawDescGZIP(), []int{12}
}

func (x *QueryCoinPriceReply) GetList() []*CoinPrice {
	if x != nil {
		return x.List
	}
	return nil
}

var File_api_wallet_v1_market_proto protoreflect.FileDescriptor

const file_api_wallet_v1_market_proto_rawDesc = "" +
	"\n" +
	"\x1aapi/wallet/v1/market.proto\x12\rapi.wallet.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\"\xe1\x02\n" +
	"\x0fTokenWithMarket\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x04 \x01(\tR\x06symbol\x12\x1a\n" +
	"\bdecimals\x18\x05 \x01(\x03R\bdecimals\x12-\n" +
	"\x12circulating_supply\x18\x06 \x01(\tR\x11circulatingSupply\x12,\n" +
	"\x12trading_volume_24h\x18\a \x01(\tR\x10tradingVolume24h\x12=\n" +
	"\x1bprice_change_percentage_24h\x18\b \x01(\tR\x18priceChangePercentage24h\x12\x19\n" +
	"\blogo_url\x18\t \x01(\tR\alogoUrl\x12\x14\n" +
	"\x05price\x18\n" +
	" \x01(\tR\x05price\"F\n" +
	"\x10SearchTokenReply\x122\n" +
	"\x04list\x18\x01 \x03(\v2\x1e.api.wallet.v1.TokenWithMarketR\x04list\"\xa3\x01\n" +
	"\x13ListPopularTokenReq\x12\x1b\n" +
	"\x04page\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12\x1f\n" +
	"\x05limit\x18\x02 \x01(\x03B\t\xbaH\x06\"\x04\x18\x14 \x00R\x05limit\x12\x1d\n" +
	"\akeyword\x18\x03 \x01(\tH\x00R\akeyword\x88\x01\x01\x12#\n" +
	"\rchain_indexes\x18\x04 \x01(\tR\fchainIndexesB\n" +
	"\n" +
	"\b_keyword\"l\n" +
	"\x15ListPopularTokenReply\x122\n" +
	"\x04list\x18\x01 \x03(\v2\x1e.api.wallet.v1.TokenWithMarketR\x04list\x12\x1f\n" +
	"\vtotal_count\x18\x02 \x01(\x03R\n" +
	"totalCount\"T\n" +
	"\x13QuerySimpleKlineReq\x12=\n" +
	"\ttoken_ids\x18\x01 \x03(\v2\x16.api.wallet.v1.TokenIDB\b\xbaH\x05\x92\x01\x02\b\x01R\btokenIds\"\x81\x01\n" +
	"\vSimpleKline\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\x12\x14\n" +
	"\x05times\x18\x03 \x03(\x03R\x05times\x12!\n" +
	"\fclose_prices\x18\x04 \x03(\tR\vclosePrices\"G\n" +
	"\x15QuerySimpleKlineReply\x12.\n" +
	"\x04list\x18\x01 \x03(\v2\x1a.api.wallet.v1.SimpleKlineR\x04list\"9\n" +
	"\x17QueryCurrencyUSDRateReq\x12\x1e\n" +
	"\n" +
	"currencies\x18\x01 \x01(\tR\n" +
	"currencies\"\x8c\x01\n" +
	"\x0fCurrencyUSDRate\x12?\n" +
	"\x05value\x18\x01 \x03(\v2).api.wallet.v1.CurrencyUSDRate.ValueEntryR\x05value\x1a8\n" +
	"\n" +
	"ValueEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"D\n" +
	"\aTokenID\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\"R\n" +
	"\x11QueryCoinPriceReq\x12=\n" +
	"\ttoken_ids\x18\x01 \x03(\v2\x16.api.wallet.v1.TokenIDB\b\xbaH\x05\x92\x01\x02\b\x01R\btokenIds\"\x84\x01\n" +
	"\tCoinPrice\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\x12\x14\n" +
	"\x05price\x18\x03 \x01(\tR\x05price\x12&\n" +
	"\x0flast_updated_at\x18\x04 \x01(\x03R\rlastUpdatedAt\"C\n" +
	"\x13QueryCoinPriceReply\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.api.wallet.v1.CoinPriceR\x04list2\xa6\x04\n" +
	"\tMarketSrv\x12~\n" +
	"\x0eQueryCoinPrice\x12 .api.wallet.v1.QueryCoinPriceReq\x1a\".api.wallet.v1.QueryCoinPriceReply\"&\x82\xd3\xe4\x93\x02 :\x01*\"\x1b/v1/market/query_coin_price\x12\x89\x01\n" +
	"\x14QueryCurrencyUSDRate\x12&.api.wallet.v1.QueryCurrencyUSDRateReq\x1a\x1e.api.wallet.v1.CurrencyUSDRate\")\x82\xd3\xe4\x93\x02#\x12!/v1/market/query_currency_usdrate\x12\x86\x01\n" +
	"\x10QuerySimpleKline\x12\".api.wallet.v1.QuerySimpleKlineReq\x1a$.api.wallet.v1.QuerySimpleKlineReply\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/v1/market/query_simple_kline\x12\x83\x01\n" +
	"\x10ListPopularToken\x12\".api.wallet.v1.ListPopularTokenReq\x1a$.api.wallet.v1.ListPopularTokenReply\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/v1/market/list_popular_tokenB\x93\x01\n" +
	"\x11com.api.wallet.v1B\vMarketProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_market_proto_rawDescOnce sync.Once
	file_api_wallet_v1_market_proto_rawDescData []byte
)

func file_api_wallet_v1_market_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_market_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_market_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_market_proto_rawDesc), len(file_api_wallet_v1_market_proto_rawDesc)))
	})
	return file_api_wallet_v1_market_proto_rawDescData
}

var file_api_wallet_v1_market_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_wallet_v1_market_proto_goTypes = []any{
	(*TokenWithMarket)(nil),         // 0: api.wallet.v1.TokenWithMarket
	(*SearchTokenReply)(nil),        // 1: api.wallet.v1.SearchTokenReply
	(*ListPopularTokenReq)(nil),     // 2: api.wallet.v1.ListPopularTokenReq
	(*ListPopularTokenReply)(nil),   // 3: api.wallet.v1.ListPopularTokenReply
	(*QuerySimpleKlineReq)(nil),     // 4: api.wallet.v1.QuerySimpleKlineReq
	(*SimpleKline)(nil),             // 5: api.wallet.v1.SimpleKline
	(*QuerySimpleKlineReply)(nil),   // 6: api.wallet.v1.QuerySimpleKlineReply
	(*QueryCurrencyUSDRateReq)(nil), // 7: api.wallet.v1.QueryCurrencyUSDRateReq
	(*CurrencyUSDRate)(nil),         // 8: api.wallet.v1.CurrencyUSDRate
	(*TokenID)(nil),                 // 9: api.wallet.v1.TokenID
	(*QueryCoinPriceReq)(nil),       // 10: api.wallet.v1.QueryCoinPriceReq
	(*CoinPrice)(nil),               // 11: api.wallet.v1.CoinPrice
	(*QueryCoinPriceReply)(nil),     // 12: api.wallet.v1.QueryCoinPriceReply
	nil,                             // 13: api.wallet.v1.CurrencyUSDRate.ValueEntry
}
var file_api_wallet_v1_market_proto_depIdxs = []int32{
	0,  // 0: api.wallet.v1.SearchTokenReply.list:type_name -> api.wallet.v1.TokenWithMarket
	0,  // 1: api.wallet.v1.ListPopularTokenReply.list:type_name -> api.wallet.v1.TokenWithMarket
	9,  // 2: api.wallet.v1.QuerySimpleKlineReq.token_ids:type_name -> api.wallet.v1.TokenID
	5,  // 3: api.wallet.v1.QuerySimpleKlineReply.list:type_name -> api.wallet.v1.SimpleKline
	13, // 4: api.wallet.v1.CurrencyUSDRate.value:type_name -> api.wallet.v1.CurrencyUSDRate.ValueEntry
	9,  // 5: api.wallet.v1.QueryCoinPriceReq.token_ids:type_name -> api.wallet.v1.TokenID
	11, // 6: api.wallet.v1.QueryCoinPriceReply.list:type_name -> api.wallet.v1.CoinPrice
	10, // 7: api.wallet.v1.MarketSrv.QueryCoinPrice:input_type -> api.wallet.v1.QueryCoinPriceReq
	7,  // 8: api.wallet.v1.MarketSrv.QueryCurrencyUSDRate:input_type -> api.wallet.v1.QueryCurrencyUSDRateReq
	4,  // 9: api.wallet.v1.MarketSrv.QuerySimpleKline:input_type -> api.wallet.v1.QuerySimpleKlineReq
	2,  // 10: api.wallet.v1.MarketSrv.ListPopularToken:input_type -> api.wallet.v1.ListPopularTokenReq
	12, // 11: api.wallet.v1.MarketSrv.QueryCoinPrice:output_type -> api.wallet.v1.QueryCoinPriceReply
	8,  // 12: api.wallet.v1.MarketSrv.QueryCurrencyUSDRate:output_type -> api.wallet.v1.CurrencyUSDRate
	6,  // 13: api.wallet.v1.MarketSrv.QuerySimpleKline:output_type -> api.wallet.v1.QuerySimpleKlineReply
	3,  // 14: api.wallet.v1.MarketSrv.ListPopularToken:output_type -> api.wallet.v1.ListPopularTokenReply
	11, // [11:15] is the sub-list for method output_type
	7,  // [7:11] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_market_proto_init() }
func file_api_wallet_v1_market_proto_init() {
	if File_api_wallet_v1_market_proto != nil {
		return
	}
	file_api_wallet_v1_market_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_market_proto_rawDesc), len(file_api_wallet_v1_market_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_market_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_market_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_market_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_market_proto = out.File
	file_api_wallet_v1_market_proto_goTypes = nil
	file_api_wallet_v1_market_proto_depIdxs = nil
}
