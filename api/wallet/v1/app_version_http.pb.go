// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/wallet/v1/app_version.proto

package v1

import (
	v1 "byd_wallet/api/walletadmin/v1"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAppVersionServiceGetLatestAppVersion = "/api.wallet.v1.AppVersionService/GetLatestAppVersion"

type AppVersionServiceHTTPServer interface {
	// GetLatestAppVersion 获取最新版本
	GetLatestAppVersion(context.Context, *GetLatestAppVersionReq) (*v1.AppVersionInfo, error)
}

func RegisterAppVersionServiceHTTPServer(s *http.Server, srv AppVersionServiceHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/app-versions/latest", _AppVersionService_GetLatestAppVersion0_HTTP_Handler(srv))
}

func _AppVersionService_GetLatestAppVersion0_HTTP_Handler(srv AppVersionServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetLatestAppVersionReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAppVersionServiceGetLatestAppVersion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetLatestAppVersion(ctx, req.(*GetLatestAppVersionReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.AppVersionInfo)
		return ctx.Result(200, reply)
	}
}

type AppVersionServiceHTTPClient interface {
	GetLatestAppVersion(ctx context.Context, req *GetLatestAppVersionReq, opts ...http.CallOption) (rsp *v1.AppVersionInfo, err error)
}

type AppVersionServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewAppVersionServiceHTTPClient(client *http.Client) AppVersionServiceHTTPClient {
	return &AppVersionServiceHTTPClientImpl{client}
}

func (c *AppVersionServiceHTTPClientImpl) GetLatestAppVersion(ctx context.Context, in *GetLatestAppVersionReq, opts ...http.CallOption) (*v1.AppVersionInfo, error) {
	var out v1.AppVersionInfo
	pattern := "/v1/app-versions/latest"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAppVersionServiceGetLatestAppVersion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
