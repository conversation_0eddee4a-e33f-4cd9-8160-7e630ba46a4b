// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/gaspool.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetGasPoolTokenPriceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChainIndex    int64                  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	Address       string                 `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGasPoolTokenPriceReq) Reset() {
	*x = GetGasPoolTokenPriceReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGasPoolTokenPriceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasPoolTokenPriceReq) ProtoMessage() {}

func (x *GetGasPoolTokenPriceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasPoolTokenPriceReq.ProtoReflect.Descriptor instead.
func (*GetGasPoolTokenPriceReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{0}
}

func (x *GetGasPoolTokenPriceReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GetGasPoolTokenPriceReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type GetGasPoolTokenPriceReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 价格(USDT,带精度)
	Price string `protobuf:"bytes,1,opt,name=price,proto3" json:"price,omitempty"`
	// 时间戳(秒)
	Timestamp     int64 `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGasPoolTokenPriceReply) Reset() {
	*x = GetGasPoolTokenPriceReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGasPoolTokenPriceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasPoolTokenPriceReply) ProtoMessage() {}

func (x *GetGasPoolTokenPriceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasPoolTokenPriceReply.ProtoReflect.Descriptor instead.
func (*GetGasPoolTokenPriceReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{1}
}

func (x *GetGasPoolTokenPriceReply) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *GetGasPoolTokenPriceReply) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type SendTxReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 原始交易
	RawTx string `protobuf:"bytes,2,opt,name=raw_tx,json=rawTx,proto3" json:"raw_tx,omitempty"`
	// 交易类型 (
	// deposit:充值,deposit_pre_reduce_gas:预扣GasPool充值,
	// transfer:转账,swap:兑换
	// )
	TxType        string `protobuf:"bytes,3,opt,name=tx_type,json=txType,proto3" json:"tx_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTxReq) Reset() {
	*x = SendTxReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTxReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTxReq) ProtoMessage() {}

func (x *SendTxReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTxReq.ProtoReflect.Descriptor instead.
func (*SendTxReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{2}
}

func (x *SendTxReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SendTxReq) GetRawTx() string {
	if x != nil {
		return x.RawTx
	}
	return ""
}

func (x *SendTxReq) GetTxType() string {
	if x != nil {
		return x.TxType
	}
	return ""
}

type SendTxReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TxHash        string                 `protobuf:"bytes,1,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendTxReply) Reset() {
	*x = SendTxReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendTxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendTxReply) ProtoMessage() {}

func (x *SendTxReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendTxReply.ProtoReflect.Descriptor instead.
func (*SendTxReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{3}
}

func (x *SendTxReply) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

type GasPoolDepositToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 币名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 币符号
	Symbol string `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 币精度
	Decimals int64 `protobuf:"varint,3,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 币图标
	LogoUrl string `protobuf:"bytes,4,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	// 币地址
	Address string `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,6,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 链ID
	ChainId string `protobuf:"bytes,7,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// 最小充值金额(不带精度)
	MinDepositAmount string `protobuf:"bytes,8,opt,name=min_deposit_amount,json=minDepositAmount,proto3" json:"min_deposit_amount,omitempty"`
	// 充值地址
	DepositAddress string `protobuf:"bytes,9,opt,name=deposit_address,json=depositAddress,proto3" json:"deposit_address,omitempty"`
	// 价格(USDT,带精度)
	Price string `protobuf:"bytes,10,opt,name=price,proto3" json:"price,omitempty"`
	// 价格更新时间戳(秒)
	PriceTime     int64 `protobuf:"varint,11,opt,name=price_time,json=priceTime,proto3" json:"price_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GasPoolDepositToken) Reset() {
	*x = GasPoolDepositToken{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GasPoolDepositToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPoolDepositToken) ProtoMessage() {}

func (x *GasPoolDepositToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPoolDepositToken.ProtoReflect.Descriptor instead.
func (*GasPoolDepositToken) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{4}
}

func (x *GasPoolDepositToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GasPoolDepositToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *GasPoolDepositToken) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *GasPoolDepositToken) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *GasPoolDepositToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *GasPoolDepositToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GasPoolDepositToken) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *GasPoolDepositToken) GetMinDepositAmount() string {
	if x != nil {
		return x.MinDepositAmount
	}
	return ""
}

func (x *GasPoolDepositToken) GetDepositAddress() string {
	if x != nil {
		return x.DepositAddress
	}
	return ""
}

func (x *GasPoolDepositToken) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *GasPoolDepositToken) GetPriceTime() int64 {
	if x != nil {
		return x.PriceTime
	}
	return 0
}

type ListGasPoolDepositTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolDepositTokenReq) Reset() {
	*x = ListGasPoolDepositTokenReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolDepositTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolDepositTokenReq) ProtoMessage() {}

func (x *ListGasPoolDepositTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolDepositTokenReq.ProtoReflect.Descriptor instead.
func (*ListGasPoolDepositTokenReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{5}
}

type ListGasPoolDepositTokenReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*GasPoolDepositToken `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolDepositTokenReply) Reset() {
	*x = ListGasPoolDepositTokenReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolDepositTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolDepositTokenReply) ProtoMessage() {}

func (x *ListGasPoolDepositTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolDepositTokenReply.ProtoReflect.Descriptor instead.
func (*ListGasPoolDepositTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{6}
}

func (x *ListGasPoolDepositTokenReply) GetList() []*GasPoolDepositToken {
	if x != nil {
		return x.List
	}
	return nil
}

type GetGasPoolBalanceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WalletId      string                 `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGasPoolBalanceReq) Reset() {
	*x = GetGasPoolBalanceReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGasPoolBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasPoolBalanceReq) ProtoMessage() {}

func (x *GetGasPoolBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasPoolBalanceReq.ProtoReflect.Descriptor instead.
func (*GetGasPoolBalanceReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{7}
}

func (x *GetGasPoolBalanceReq) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

type GasPoolUserStats struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 充值额度
	TotalDepositAmount string `protobuf:"bytes,1,opt,name=total_deposit_amount,json=totalDepositAmount,proto3" json:"total_deposit_amount,omitempty"`
	// 累计消耗
	TotalReduceAmount string `protobuf:"bytes,2,opt,name=total_reduce_amount,json=totalReduceAmount,proto3" json:"total_reduce_amount,omitempty"`
	// 积分额度 // 暂无
	TotalCreditAmount string `protobuf:"bytes,3,opt,name=total_credit_amount,json=totalCreditAmount,proto3" json:"total_credit_amount,omitempty"`
	// 累计使用次数
	TotalReduceCount int64 `protobuf:"varint,4,opt,name=total_reduce_count,json=totalReduceCount,proto3" json:"total_reduce_count,omitempty"`
	// 支持公链数
	ChainCount    int64 `protobuf:"varint,5,opt,name=chain_count,json=chainCount,proto3" json:"chain_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GasPoolUserStats) Reset() {
	*x = GasPoolUserStats{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GasPoolUserStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPoolUserStats) ProtoMessage() {}

func (x *GasPoolUserStats) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPoolUserStats.ProtoReflect.Descriptor instead.
func (*GasPoolUserStats) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{8}
}

func (x *GasPoolUserStats) GetTotalDepositAmount() string {
	if x != nil {
		return x.TotalDepositAmount
	}
	return ""
}

func (x *GasPoolUserStats) GetTotalReduceAmount() string {
	if x != nil {
		return x.TotalReduceAmount
	}
	return ""
}

func (x *GasPoolUserStats) GetTotalCreditAmount() string {
	if x != nil {
		return x.TotalCreditAmount
	}
	return ""
}

func (x *GasPoolUserStats) GetTotalReduceCount() int64 {
	if x != nil {
		return x.TotalReduceCount
	}
	return 0
}

func (x *GasPoolUserStats) GetChainCount() int64 {
	if x != nil {
		return x.ChainCount
	}
	return 0
}

type GetGasPoolBalanceReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 余额(USDT,不带精度)
	Balance string `protobuf:"bytes,1,opt,name=balance,proto3" json:"balance,omitempty"`
	// 统计数据
	Stats         *GasPoolUserStats `protobuf:"bytes,2,opt,name=stats,proto3" json:"stats,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGasPoolBalanceReply) Reset() {
	*x = GetGasPoolBalanceReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGasPoolBalanceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGasPoolBalanceReply) ProtoMessage() {}

func (x *GetGasPoolBalanceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGasPoolBalanceReply.ProtoReflect.Descriptor instead.
func (*GetGasPoolBalanceReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{9}
}

func (x *GetGasPoolBalanceReply) GetBalance() string {
	if x != nil {
		return x.Balance
	}
	return ""
}

func (x *GetGasPoolBalanceReply) GetStats() *GasPoolUserStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

type ListGasPoolConsumeRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WalletId      string                 `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Page          int64                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int64                  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolConsumeRecordReq) Reset() {
	*x = ListGasPoolConsumeRecordReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolConsumeRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolConsumeRecordReq) ProtoMessage() {}

func (x *ListGasPoolConsumeRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolConsumeRecordReq.ProtoReflect.Descriptor instead.
func (*ListGasPoolConsumeRecordReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{10}
}

func (x *ListGasPoolConsumeRecordReq) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

func (x *ListGasPoolConsumeRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGasPoolConsumeRecordReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GasPoolConsumeRecord struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 创建时间(时间戳,秒)
	CreatedAt int64 `protobuf:"varint,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 记录状态(success:执行成功, fail:执行失败, pending:执行中)
	Status string `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	// 用户交易hash
	TxHash string `protobuf:"bytes,4,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	// GasPool扣款金额(USDT,不带精度)
	Amount string `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// 记录类型(transfer:转账, swap:兑换, deposit_without_gas:充值预扣)
	RecordType    string `protobuf:"bytes,6,opt,name=record_type,json=recordType,proto3" json:"record_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GasPoolConsumeRecord) Reset() {
	*x = GasPoolConsumeRecord{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GasPoolConsumeRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPoolConsumeRecord) ProtoMessage() {}

func (x *GasPoolConsumeRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPoolConsumeRecord.ProtoReflect.Descriptor instead.
func (*GasPoolConsumeRecord) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{11}
}

func (x *GasPoolConsumeRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *GasPoolConsumeRecord) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GasPoolConsumeRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GasPoolConsumeRecord) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *GasPoolConsumeRecord) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *GasPoolConsumeRecord) GetRecordType() string {
	if x != nil {
		return x.RecordType
	}
	return ""
}

type ListGasPoolConsumeRecordReply struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	TotalCount    int64                   `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	List          []*GasPoolConsumeRecord `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolConsumeRecordReply) Reset() {
	*x = ListGasPoolConsumeRecordReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolConsumeRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolConsumeRecordReply) ProtoMessage() {}

func (x *ListGasPoolConsumeRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolConsumeRecordReply.ProtoReflect.Descriptor instead.
func (*ListGasPoolConsumeRecordReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{12}
}

func (x *ListGasPoolConsumeRecordReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ListGasPoolConsumeRecordReply) GetList() []*GasPoolConsumeRecord {
	if x != nil {
		return x.List
	}
	return nil
}

type ListGasPoolCashFlowRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WalletId      string                 `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Page          int64                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit         int64                  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolCashFlowRecordReq) Reset() {
	*x = ListGasPoolCashFlowRecordReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolCashFlowRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolCashFlowRecordReq) ProtoMessage() {}

func (x *ListGasPoolCashFlowRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolCashFlowRecordReq.ProtoReflect.Descriptor instead.
func (*ListGasPoolCashFlowRecordReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{13}
}

func (x *ListGasPoolCashFlowRecordReq) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

func (x *ListGasPoolCashFlowRecordReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGasPoolCashFlowRecordReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GasPoolCashFlowRecord struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 创建时间(时间戳,秒)
	CreatedAt int64 `protobuf:"varint,1,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 币合约地址(矿币为空)
	TokenAddress string `protobuf:"bytes,3,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	// 用户交易hash
	TxHash string `protobuf:"bytes,4,opt,name=tx_hash,json=txHash,proto3" json:"tx_hash,omitempty"`
	// GasPool出入金额(USDT,不带精度)
	Amount string `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// 记录类型(deposit:充值, refund:退回)
	RecordType string `protobuf:"bytes,6,opt,name=record_type,json=recordType,proto3" json:"record_type,omitempty"`
	// GasPool出入币种数量(不带精度)
	TokenAmount string `protobuf:"bytes,7,opt,name=token_amount,json=tokenAmount,proto3" json:"token_amount,omitempty"`
	// 记录状态(success:执行成功, fail:执行失败, pending:执行中)
	Status        string `protobuf:"bytes,8,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GasPoolCashFlowRecord) Reset() {
	*x = GasPoolCashFlowRecord{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GasPoolCashFlowRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GasPoolCashFlowRecord) ProtoMessage() {}

func (x *GasPoolCashFlowRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GasPoolCashFlowRecord.ProtoReflect.Descriptor instead.
func (*GasPoolCashFlowRecord) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{14}
}

func (x *GasPoolCashFlowRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *GasPoolCashFlowRecord) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GasPoolCashFlowRecord) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *GasPoolCashFlowRecord) GetTxHash() string {
	if x != nil {
		return x.TxHash
	}
	return ""
}

func (x *GasPoolCashFlowRecord) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *GasPoolCashFlowRecord) GetRecordType() string {
	if x != nil {
		return x.RecordType
	}
	return ""
}

func (x *GasPoolCashFlowRecord) GetTokenAmount() string {
	if x != nil {
		return x.TokenAmount
	}
	return ""
}

func (x *GasPoolCashFlowRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ListGasPoolCashFlowRecordReply struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	TotalCount    int64                    `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	List          []*GasPoolCashFlowRecord `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	TokenList     []*GasPoolDepositToken   `protobuf:"bytes,3,rep,name=token_list,json=tokenList,proto3" json:"token_list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGasPoolCashFlowRecordReply) Reset() {
	*x = ListGasPoolCashFlowRecordReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGasPoolCashFlowRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGasPoolCashFlowRecordReply) ProtoMessage() {}

func (x *ListGasPoolCashFlowRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGasPoolCashFlowRecordReply.ProtoReflect.Descriptor instead.
func (*ListGasPoolCashFlowRecordReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{15}
}

func (x *ListGasPoolCashFlowRecordReply) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ListGasPoolCashFlowRecordReply) GetList() []*GasPoolCashFlowRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListGasPoolCashFlowRecordReply) GetTokenList() []*GasPoolDepositToken {
	if x != nil {
		return x.TokenList
	}
	return nil
}

type GetPaymasterReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex    int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymasterReq) Reset() {
	*x = GetPaymasterReq{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymasterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymasterReq) ProtoMessage() {}

func (x *GetPaymasterReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymasterReq.ProtoReflect.Descriptor instead.
func (*GetPaymasterReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{16}
}

func (x *GetPaymasterReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type GetPaymasterReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymasterReply) Reset() {
	*x = GetPaymasterReply{}
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymasterReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymasterReply) ProtoMessage() {}

func (x *GetPaymasterReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_gaspool_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymasterReply.ProtoReflect.Descriptor instead.
func (*GetPaymasterReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_gaspool_proto_rawDescGZIP(), []int{17}
}

func (x *GetPaymasterReply) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

var File_api_wallet_v1_gaspool_proto protoreflect.FileDescriptor

const file_api_wallet_v1_gaspool_proto_rawDesc = "" +
	"\n" +
	"\x1bapi/wallet/v1/gaspool.proto\x12\rapi.wallet.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\"T\n" +
	"\x17GetGasPoolTokenPriceReq\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\"O\n" +
	"\x19GetGasPoolTokenPriceReply\x12\x14\n" +
	"\x05price\x18\x01 \x01(\tR\x05price\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\"\\\n" +
	"\tSendTxReq\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x15\n" +
	"\x06raw_tx\x18\x02 \x01(\tR\x05rawTx\x12\x17\n" +
	"\atx_type\x18\x03 \x01(\tR\x06txType\"&\n" +
	"\vSendTxReply\x12\x17\n" +
	"\atx_hash\x18\x01 \x01(\tR\x06txHash\"\xda\x02\n" +
	"\x13GasPoolDepositToken\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x02 \x01(\tR\x06symbol\x12\x1a\n" +
	"\bdecimals\x18\x03 \x01(\x03R\bdecimals\x12\x19\n" +
	"\blogo_url\x18\x04 \x01(\tR\alogoUrl\x12\x18\n" +
	"\aaddress\x18\x05 \x01(\tR\aaddress\x12\x1f\n" +
	"\vchain_index\x18\x06 \x01(\x03R\n" +
	"chainIndex\x12\x19\n" +
	"\bchain_id\x18\a \x01(\tR\achainId\x12,\n" +
	"\x12min_deposit_amount\x18\b \x01(\tR\x10minDepositAmount\x12'\n" +
	"\x0fdeposit_address\x18\t \x01(\tR\x0edepositAddress\x12\x14\n" +
	"\x05price\x18\n" +
	" \x01(\tR\x05price\x12\x1d\n" +
	"\n" +
	"price_time\x18\v \x01(\x03R\tpriceTime\"\x1c\n" +
	"\x1aListGasPoolDepositTokenReq\"V\n" +
	"\x1cListGasPoolDepositTokenReply\x126\n" +
	"\x04list\x18\x01 \x03(\v2\".api.wallet.v1.GasPoolDepositTokenR\x04list\"<\n" +
	"\x14GetGasPoolBalanceReq\x12$\n" +
	"\twallet_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bwalletId\"\xf3\x01\n" +
	"\x10GasPoolUserStats\x120\n" +
	"\x14total_deposit_amount\x18\x01 \x01(\tR\x12totalDepositAmount\x12.\n" +
	"\x13total_reduce_amount\x18\x02 \x01(\tR\x11totalReduceAmount\x12.\n" +
	"\x13total_credit_amount\x18\x03 \x01(\tR\x11totalCreditAmount\x12,\n" +
	"\x12total_reduce_count\x18\x04 \x01(\x03R\x10totalReduceCount\x12\x1f\n" +
	"\vchain_count\x18\x05 \x01(\x03R\n" +
	"chainCount\"i\n" +
	"\x16GetGasPoolBalanceReply\x12\x18\n" +
	"\abalance\x18\x01 \x01(\tR\abalance\x125\n" +
	"\x05stats\x18\x02 \x01(\v2\x1f.api.wallet.v1.GasPoolUserStatsR\x05stats\"\x81\x01\n" +
	"\x1bListGasPoolConsumeRecordReq\x12$\n" +
	"\twallet_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bwalletId\x12\x1b\n" +
	"\x04page\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12\x1f\n" +
	"\x05limit\x18\x03 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\x05limit\"\xc0\x01\n" +
	"\x14GasPoolConsumeRecord\x12\x1d\n" +
	"\n" +
	"created_at\x18\x01 \x01(\x03R\tcreatedAt\x12\x1f\n" +
	"\vchain_index\x18\x02 \x01(\x03R\n" +
	"chainIndex\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12\x17\n" +
	"\atx_hash\x18\x04 \x01(\tR\x06txHash\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\tR\x06amount\x12\x1f\n" +
	"\vrecord_type\x18\x06 \x01(\tR\n" +
	"recordType\"y\n" +
	"\x1dListGasPoolConsumeRecordReply\x12\x1f\n" +
	"\vtotal_count\x18\x01 \x01(\x03R\n" +
	"totalCount\x127\n" +
	"\x04list\x18\x02 \x03(\v2#.api.wallet.v1.GasPoolConsumeRecordR\x04list\"\x82\x01\n" +
	"\x1cListGasPoolCashFlowRecordReq\x12$\n" +
	"\twallet_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bwalletId\x12\x1b\n" +
	"\x04page\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12\x1f\n" +
	"\x05limit\x18\x03 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\x05limit\"\x89\x02\n" +
	"\x15GasPoolCashFlowRecord\x12\x1d\n" +
	"\n" +
	"created_at\x18\x01 \x01(\x03R\tcreatedAt\x12\x1f\n" +
	"\vchain_index\x18\x02 \x01(\x03R\n" +
	"chainIndex\x12#\n" +
	"\rtoken_address\x18\x03 \x01(\tR\ftokenAddress\x12\x17\n" +
	"\atx_hash\x18\x04 \x01(\tR\x06txHash\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\tR\x06amount\x12\x1f\n" +
	"\vrecord_type\x18\x06 \x01(\tR\n" +
	"recordType\x12!\n" +
	"\ftoken_amount\x18\a \x01(\tR\vtokenAmount\x12\x16\n" +
	"\x06status\x18\b \x01(\tR\x06status\"\xbe\x01\n" +
	"\x1eListGasPoolCashFlowRecordReply\x12\x1f\n" +
	"\vtotal_count\x18\x01 \x01(\x03R\n" +
	"totalCount\x128\n" +
	"\x04list\x18\x02 \x03(\v2$.api.wallet.v1.GasPoolCashFlowRecordR\x04list\x12A\n" +
	"\n" +
	"token_list\x18\x03 \x03(\v2\".api.wallet.v1.GasPoolDepositTokenR\ttokenList\"2\n" +
	"\x0fGetPaymasterReq\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\"-\n" +
	"\x11GetPaymasterReply\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\tR\aaddress2\xbc\a\n" +
	"\n" +
	"GasPoolSrv\x12\x95\x01\n" +
	"\x17ListGasPoolDepositToken\x12).api.wallet.v1.ListGasPoolDepositTokenReq\x1a+.api.wallet.v1.ListGasPoolDepositTokenReply\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/v1/gaspool/deposit_tokens\x12|\n" +
	"\x11GetGasPoolBalance\x12#.api.wallet.v1.GetGasPoolBalanceReq\x1a%.api.wallet.v1.GetGasPoolBalanceReply\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/v1/gaspool/balance\x12\x99\x01\n" +
	"\x18ListGasPoolConsumeRecord\x12*.api.wallet.v1.ListGasPoolConsumeRecordReq\x1a,.api.wallet.v1.ListGasPoolConsumeRecordReply\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/v1/gaspool/consume_records\x12\x9e\x01\n" +
	"\x19ListGasPoolCashFlowRecord\x12+.api.wallet.v1.ListGasPoolCashFlowRecordReq\x1a-.api.wallet.v1.ListGasPoolCashFlowRecordReply\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/v1/gaspool/cash_flow_records\x12o\n" +
	"\fGetPaymaster\x12\x1e.api.wallet.v1.GetPaymasterReq\x1a .api.wallet.v1.GetPaymasterReply\"\x1d\x82\xd3\xe4\x93\x02\x17\x12\x15/v1/gaspool/paymaster\x12^\n" +
	"\x06SendTx\x12\x18.api.wallet.v1.SendTxReq\x1a\x1a.api.wallet.v1.SendTxReply\"\x1e\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/v1/gaspool/send_tx\x12\x89\x01\n" +
	"\x14GetGasPoolTokenPrice\x12&.api.wallet.v1.GetGasPoolTokenPriceReq\x1a(.api.wallet.v1.GetGasPoolTokenPriceReply\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/v1/gaspool/token_priceB\x94\x01\n" +
	"\x11com.api.wallet.v1B\fGaspoolProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_gaspool_proto_rawDescOnce sync.Once
	file_api_wallet_v1_gaspool_proto_rawDescData []byte
)

func file_api_wallet_v1_gaspool_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_gaspool_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_gaspool_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_gaspool_proto_rawDesc), len(file_api_wallet_v1_gaspool_proto_rawDesc)))
	})
	return file_api_wallet_v1_gaspool_proto_rawDescData
}

var file_api_wallet_v1_gaspool_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_api_wallet_v1_gaspool_proto_goTypes = []any{
	(*GetGasPoolTokenPriceReq)(nil),        // 0: api.wallet.v1.GetGasPoolTokenPriceReq
	(*GetGasPoolTokenPriceReply)(nil),      // 1: api.wallet.v1.GetGasPoolTokenPriceReply
	(*SendTxReq)(nil),                      // 2: api.wallet.v1.SendTxReq
	(*SendTxReply)(nil),                    // 3: api.wallet.v1.SendTxReply
	(*GasPoolDepositToken)(nil),            // 4: api.wallet.v1.GasPoolDepositToken
	(*ListGasPoolDepositTokenReq)(nil),     // 5: api.wallet.v1.ListGasPoolDepositTokenReq
	(*ListGasPoolDepositTokenReply)(nil),   // 6: api.wallet.v1.ListGasPoolDepositTokenReply
	(*GetGasPoolBalanceReq)(nil),           // 7: api.wallet.v1.GetGasPoolBalanceReq
	(*GasPoolUserStats)(nil),               // 8: api.wallet.v1.GasPoolUserStats
	(*GetGasPoolBalanceReply)(nil),         // 9: api.wallet.v1.GetGasPoolBalanceReply
	(*ListGasPoolConsumeRecordReq)(nil),    // 10: api.wallet.v1.ListGasPoolConsumeRecordReq
	(*GasPoolConsumeRecord)(nil),           // 11: api.wallet.v1.GasPoolConsumeRecord
	(*ListGasPoolConsumeRecordReply)(nil),  // 12: api.wallet.v1.ListGasPoolConsumeRecordReply
	(*ListGasPoolCashFlowRecordReq)(nil),   // 13: api.wallet.v1.ListGasPoolCashFlowRecordReq
	(*GasPoolCashFlowRecord)(nil),          // 14: api.wallet.v1.GasPoolCashFlowRecord
	(*ListGasPoolCashFlowRecordReply)(nil), // 15: api.wallet.v1.ListGasPoolCashFlowRecordReply
	(*GetPaymasterReq)(nil),                // 16: api.wallet.v1.GetPaymasterReq
	(*GetPaymasterReply)(nil),              // 17: api.wallet.v1.GetPaymasterReply
}
var file_api_wallet_v1_gaspool_proto_depIdxs = []int32{
	4,  // 0: api.wallet.v1.ListGasPoolDepositTokenReply.list:type_name -> api.wallet.v1.GasPoolDepositToken
	8,  // 1: api.wallet.v1.GetGasPoolBalanceReply.stats:type_name -> api.wallet.v1.GasPoolUserStats
	11, // 2: api.wallet.v1.ListGasPoolConsumeRecordReply.list:type_name -> api.wallet.v1.GasPoolConsumeRecord
	14, // 3: api.wallet.v1.ListGasPoolCashFlowRecordReply.list:type_name -> api.wallet.v1.GasPoolCashFlowRecord
	4,  // 4: api.wallet.v1.ListGasPoolCashFlowRecordReply.token_list:type_name -> api.wallet.v1.GasPoolDepositToken
	5,  // 5: api.wallet.v1.GasPoolSrv.ListGasPoolDepositToken:input_type -> api.wallet.v1.ListGasPoolDepositTokenReq
	7,  // 6: api.wallet.v1.GasPoolSrv.GetGasPoolBalance:input_type -> api.wallet.v1.GetGasPoolBalanceReq
	10, // 7: api.wallet.v1.GasPoolSrv.ListGasPoolConsumeRecord:input_type -> api.wallet.v1.ListGasPoolConsumeRecordReq
	13, // 8: api.wallet.v1.GasPoolSrv.ListGasPoolCashFlowRecord:input_type -> api.wallet.v1.ListGasPoolCashFlowRecordReq
	16, // 9: api.wallet.v1.GasPoolSrv.GetPaymaster:input_type -> api.wallet.v1.GetPaymasterReq
	2,  // 10: api.wallet.v1.GasPoolSrv.SendTx:input_type -> api.wallet.v1.SendTxReq
	0,  // 11: api.wallet.v1.GasPoolSrv.GetGasPoolTokenPrice:input_type -> api.wallet.v1.GetGasPoolTokenPriceReq
	6,  // 12: api.wallet.v1.GasPoolSrv.ListGasPoolDepositToken:output_type -> api.wallet.v1.ListGasPoolDepositTokenReply
	9,  // 13: api.wallet.v1.GasPoolSrv.GetGasPoolBalance:output_type -> api.wallet.v1.GetGasPoolBalanceReply
	12, // 14: api.wallet.v1.GasPoolSrv.ListGasPoolConsumeRecord:output_type -> api.wallet.v1.ListGasPoolConsumeRecordReply
	15, // 15: api.wallet.v1.GasPoolSrv.ListGasPoolCashFlowRecord:output_type -> api.wallet.v1.ListGasPoolCashFlowRecordReply
	17, // 16: api.wallet.v1.GasPoolSrv.GetPaymaster:output_type -> api.wallet.v1.GetPaymasterReply
	3,  // 17: api.wallet.v1.GasPoolSrv.SendTx:output_type -> api.wallet.v1.SendTxReply
	1,  // 18: api.wallet.v1.GasPoolSrv.GetGasPoolTokenPrice:output_type -> api.wallet.v1.GetGasPoolTokenPriceReply
	12, // [12:19] is the sub-list for method output_type
	5,  // [5:12] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_gaspool_proto_init() }
func file_api_wallet_v1_gaspool_proto_init() {
	if File_api_wallet_v1_gaspool_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_gaspool_proto_rawDesc), len(file_api_wallet_v1_gaspool_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_gaspool_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_gaspool_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_gaspool_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_gaspool_proto = out.File
	file_api_wallet_v1_gaspool_proto_goTypes = nil
	file_api_wallet_v1_gaspool_proto_depIdxs = nil
}
