// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/dapp.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListApprovedAddressesReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Addresses     []*UserAddress         `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListApprovedAddressesReq) Reset() {
	*x = ListApprovedAddressesReq{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListApprovedAddressesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovedAddressesReq) ProtoMessage() {}

func (x *ListApprovedAddressesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovedAddressesReq.ProtoReflect.Descriptor instead.
func (*ListApprovedAddressesReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{0}
}

func (x *ListApprovedAddressesReq) GetAddresses() []*UserAddress {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type ListApprovedAddressesReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*UserAddress         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListApprovedAddressesReply) Reset() {
	*x = ListApprovedAddressesReply{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListApprovedAddressesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovedAddressesReply) ProtoMessage() {}

func (x *ListApprovedAddressesReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovedAddressesReply.ProtoReflect.Descriptor instead.
func (*ListApprovedAddressesReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{1}
}

func (x *ListApprovedAddressesReply) GetList() []*UserAddress {
	if x != nil {
		return x.List
	}
	return nil
}

type ListTokenApprovalsByDappReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	SpenderAddress string                 `protobuf:"bytes,1,opt,name=spender_address,json=spenderAddress,proto3" json:"spender_address,omitempty"`
	ChainIndex     int64                  `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 用户钱包地址
	Addresses     []string `protobuf:"bytes,3,rep,name=addresses,proto3" json:"addresses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTokenApprovalsByDappReq) Reset() {
	*x = ListTokenApprovalsByDappReq{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTokenApprovalsByDappReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenApprovalsByDappReq) ProtoMessage() {}

func (x *ListTokenApprovalsByDappReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenApprovalsByDappReq.ProtoReflect.Descriptor instead.
func (*ListTokenApprovalsByDappReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{2}
}

func (x *ListTokenApprovalsByDappReq) GetSpenderAddress() string {
	if x != nil {
		return x.SpenderAddress
	}
	return ""
}

func (x *ListTokenApprovalsByDappReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListTokenApprovalsByDappReq) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type ListTokenApprovalsByDappReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*TokenApproval       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTokenApprovalsByDappReply) Reset() {
	*x = ListTokenApprovalsByDappReply{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTokenApprovalsByDappReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenApprovalsByDappReply) ProtoMessage() {}

func (x *ListTokenApprovalsByDappReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenApprovalsByDappReply.ProtoReflect.Descriptor instead.
func (*ListTokenApprovalsByDappReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{3}
}

func (x *ListTokenApprovalsByDappReply) GetList() []*TokenApproval {
	if x != nil {
		return x.List
	}
	return nil
}

type ListApprovedDappsByUserAddressesReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*UserApprovedDapp    `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListApprovedDappsByUserAddressesReply) Reset() {
	*x = ListApprovedDappsByUserAddressesReply{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListApprovedDappsByUserAddressesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovedDappsByUserAddressesReply) ProtoMessage() {}

func (x *ListApprovedDappsByUserAddressesReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovedDappsByUserAddressesReply.ProtoReflect.Descriptor instead.
func (*ListApprovedDappsByUserAddressesReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{4}
}

func (x *ListApprovedDappsByUserAddressesReply) GetList() []*UserApprovedDapp {
	if x != nil {
		return x.List
	}
	return nil
}

type ListApprovalByUserAddressesReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Addresses     []*UserAddress         `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListApprovalByUserAddressesReq) Reset() {
	*x = ListApprovalByUserAddressesReq{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListApprovalByUserAddressesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovalByUserAddressesReq) ProtoMessage() {}

func (x *ListApprovalByUserAddressesReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovalByUserAddressesReq.ProtoReflect.Descriptor instead.
func (*ListApprovalByUserAddressesReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{5}
}

func (x *ListApprovalByUserAddressesReq) GetAddresses() []*UserAddress {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type UserAddress struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	ChainIndex    int64                  `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserAddress) Reset() {
	*x = UserAddress{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserAddress) ProtoMessage() {}

func (x *UserAddress) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserAddress.ProtoReflect.Descriptor instead.
func (*UserAddress) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{6}
}

func (x *UserAddress) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UserAddress) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type ListApprovalByUserAddressReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户钱包地址
	Address       string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	ChainIndex    int64  `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListApprovalByUserAddressReq) Reset() {
	*x = ListApprovalByUserAddressReq{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListApprovalByUserAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovalByUserAddressReq) ProtoMessage() {}

func (x *ListApprovalByUserAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovalByUserAddressReq.ProtoReflect.Descriptor instead.
func (*ListApprovalByUserAddressReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{7}
}

func (x *ListApprovalByUserAddressReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *ListApprovalByUserAddressReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type ListApprovalByUserAddressReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       *UserAddress           `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Network       *BlockchainNetwork     `protobuf:"bytes,2,opt,name=network,proto3" json:"network,omitempty"`
	List          []*TokenDappApproval   `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListApprovalByUserAddressReply) Reset() {
	*x = ListApprovalByUserAddressReply{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListApprovalByUserAddressReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListApprovalByUserAddressReply) ProtoMessage() {}

func (x *ListApprovalByUserAddressReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListApprovalByUserAddressReply.ProtoReflect.Descriptor instead.
func (*ListApprovalByUserAddressReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{8}
}

func (x *ListApprovalByUserAddressReply) GetAddress() *UserAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *ListApprovalByUserAddressReply) GetNetwork() *BlockchainNetwork {
	if x != nil {
		return x.Network
	}
	return nil
}

func (x *ListApprovalByUserAddressReply) GetList() []*TokenDappApproval {
	if x != nil {
		return x.List
	}
	return nil
}

type SearchDappReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 搜索关键词
	Key           string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchDappReq) Reset() {
	*x = SearchDappReq{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchDappReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDappReq) ProtoMessage() {}

func (x *SearchDappReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDappReq.ProtoReflect.Descriptor instead.
func (*SearchDappReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{9}
}

func (x *SearchDappReq) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type SearchDappReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Dapp                `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchDappReply) Reset() {
	*x = SearchDappReply{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchDappReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDappReply) ProtoMessage() {}

func (x *SearchDappReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDappReply.ProtoReflect.Descriptor instead.
func (*SearchDappReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{10}
}

func (x *SearchDappReply) GetList() []*Dapp {
	if x != nil {
		return x.List
	}
	return nil
}

type GetDappCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ChainIndex    int64                  `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	ChainId       string                 `protobuf:"bytes,3,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDappCategoryReq) Reset() {
	*x = GetDappCategoryReq{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDappCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDappCategoryReq) ProtoMessage() {}

func (x *GetDappCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDappCategoryReq.ProtoReflect.Descriptor instead.
func (*GetDappCategoryReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{11}
}

func (x *GetDappCategoryReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetDappCategoryReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GetDappCategoryReq) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

type GetDappCategoryReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Dapp                `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDappCategoryReply) Reset() {
	*x = GetDappCategoryReply{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDappCategoryReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDappCategoryReply) ProtoMessage() {}

func (x *GetDappCategoryReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDappCategoryReply.ProtoReflect.Descriptor instead.
func (*GetDappCategoryReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{12}
}

func (x *GetDappCategoryReply) GetList() []*Dapp {
	if x != nil {
		return x.List
	}
	return nil
}

type GetDappTopicReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDappTopicReq) Reset() {
	*x = GetDappTopicReq{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDappTopicReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDappTopicReq) ProtoMessage() {}

func (x *GetDappTopicReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDappTopicReq.ProtoReflect.Descriptor instead.
func (*GetDappTopicReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{13}
}

func (x *GetDappTopicReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetDappTopicReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Topic         *DappTopic             `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDappTopicReply) Reset() {
	*x = GetDappTopicReply{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDappTopicReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDappTopicReply) ProtoMessage() {}

func (x *GetDappTopicReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDappTopicReply.ProtoReflect.Descriptor instead.
func (*GetDappTopicReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{14}
}

func (x *GetDappTopicReply) GetTopic() *DappTopic {
	if x != nil {
		return x.Topic
	}
	return nil
}

type ListDappIndexReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappIndexReq) Reset() {
	*x = ListDappIndexReq{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappIndexReq) ProtoMessage() {}

func (x *ListDappIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappIndexReq.ProtoReflect.Descriptor instead.
func (*ListDappIndexReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{15}
}

type Dapp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Logo          string                 `protobuf:"bytes,2,opt,name=logo,proto3" json:"logo,omitempty"`
	Link          string                 `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
	Tags          string                 `protobuf:"bytes,4,opt,name=tags,proto3" json:"tags,omitempty"`
	Hot           bool                   `protobuf:"varint,5,opt,name=hot,proto3" json:"hot,omitempty"`
	I18Ns         []*DappI18N            `protobuf:"bytes,6,rep,name=i18ns,proto3" json:"i18ns,omitempty"`
	Networks      []*BlockchainNetwork   `protobuf:"bytes,7,rep,name=networks,proto3" json:"networks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Dapp) Reset() {
	*x = Dapp{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Dapp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dapp) ProtoMessage() {}

func (x *Dapp) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dapp.ProtoReflect.Descriptor instead.
func (*Dapp) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{16}
}

func (x *Dapp) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Dapp) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *Dapp) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

func (x *Dapp) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

func (x *Dapp) GetHot() bool {
	if x != nil {
		return x.Hot
	}
	return false
}

func (x *Dapp) GetI18Ns() []*DappI18N {
	if x != nil {
		return x.I18Ns
	}
	return nil
}

func (x *Dapp) GetNetworks() []*BlockchainNetwork {
	if x != nil {
		return x.Networks
	}
	return nil
}

type DappI18N struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Summary       string                 `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappI18N) Reset() {
	*x = DappI18N{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappI18N) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappI18N) ProtoMessage() {}

func (x *DappI18N) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappI18N.ProtoReflect.Descriptor instead.
func (*DappI18N) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{17}
}

func (x *DappI18N) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DappI18N) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

type BlockchainNetwork struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChainId       string                 `protobuf:"bytes,1,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BlockchainNetwork) Reset() {
	*x = BlockchainNetwork{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockchainNetwork) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockchainNetwork) ProtoMessage() {}

func (x *BlockchainNetwork) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockchainNetwork.ProtoReflect.Descriptor instead.
func (*BlockchainNetwork) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{18}
}

func (x *BlockchainNetwork) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

type DappCategory struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Summary       string                 `protobuf:"bytes,3,opt,name=summary,proto3" json:"summary,omitempty"`
	Language      string                 `protobuf:"bytes,4,opt,name=language,proto3" json:"language,omitempty"`
	Dapps         []*Dapp                `protobuf:"bytes,5,rep,name=dapps,proto3" json:"dapps,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappCategory) Reset() {
	*x = DappCategory{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappCategory) ProtoMessage() {}

func (x *DappCategory) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappCategory.ProtoReflect.Descriptor instead.
func (*DappCategory) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{19}
}

func (x *DappCategory) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DappCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DappCategory) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *DappCategory) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *DappCategory) GetDapps() []*Dapp {
	if x != nil {
		return x.Dapps
	}
	return nil
}

type DappTopic struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BackgroundUrl string                 `protobuf:"bytes,2,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Summary       string                 `protobuf:"bytes,4,opt,name=summary,proto3" json:"summary,omitempty"`
	Language      string                 `protobuf:"bytes,5,opt,name=language,proto3" json:"language,omitempty"`
	Title         string                 `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	TopTitle      string                 `protobuf:"bytes,7,opt,name=top_title,json=topTitle,proto3" json:"top_title,omitempty"`
	BottomTitle   string                 `protobuf:"bytes,8,opt,name=bottom_title,json=bottomTitle,proto3" json:"bottom_title,omitempty"`
	Dapps         []*Dapp                `protobuf:"bytes,9,rep,name=dapps,proto3" json:"dapps,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappTopic) Reset() {
	*x = DappTopic{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappTopic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappTopic) ProtoMessage() {}

func (x *DappTopic) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappTopic.ProtoReflect.Descriptor instead.
func (*DappTopic) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{20}
}

func (x *DappTopic) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DappTopic) GetBackgroundUrl() string {
	if x != nil {
		return x.BackgroundUrl
	}
	return ""
}

func (x *DappTopic) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DappTopic) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *DappTopic) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *DappTopic) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *DappTopic) GetTopTitle() string {
	if x != nil {
		return x.TopTitle
	}
	return ""
}

func (x *DappTopic) GetBottomTitle() string {
	if x != nil {
		return x.BottomTitle
	}
	return ""
}

func (x *DappTopic) GetDapps() []*Dapp {
	if x != nil {
		return x.Dapps
	}
	return nil
}

type DappIndex struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Topic         *DappTopic             `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	Category      *DappCategory          `protobuf:"bytes,2,opt,name=category,proto3" json:"category,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DappIndex) Reset() {
	*x = DappIndex{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappIndex) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappIndex) ProtoMessage() {}

func (x *DappIndex) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappIndex.ProtoReflect.Descriptor instead.
func (*DappIndex) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{21}
}

func (x *DappIndex) GetTopic() *DappTopic {
	if x != nil {
		return x.Topic
	}
	return nil
}

func (x *DappIndex) GetCategory() *DappCategory {
	if x != nil {
		return x.Category
	}
	return nil
}

type ListDappIndexReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DappIndex           `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDappIndexReply) Reset() {
	*x = ListDappIndexReply{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDappIndexReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDappIndexReply) ProtoMessage() {}

func (x *ListDappIndexReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDappIndexReply.ProtoReflect.Descriptor instead.
func (*ListDappIndexReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{22}
}

func (x *ListDappIndexReply) GetList() []*DappIndex {
	if x != nil {
		return x.List
	}
	return nil
}

type ListNavigationReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNavigationReq) Reset() {
	*x = ListNavigationReq{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNavigationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNavigationReq) ProtoMessage() {}

func (x *ListNavigationReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNavigationReq.ProtoReflect.Descriptor instead.
func (*ListNavigationReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{23}
}

type ListNavigationReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DappNavigation      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNavigationReply) Reset() {
	*x = ListNavigationReply{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNavigationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNavigationReply) ProtoMessage() {}

func (x *ListNavigationReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNavigationReply.ProtoReflect.Descriptor instead.
func (*ListNavigationReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{24}
}

func (x *ListNavigationReply) GetList() []*DappNavigation {
	if x != nil {
		return x.List
	}
	return nil
}

type DappNavigation struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	DappCategoryId uint64                 `protobuf:"varint,1,opt,name=dapp_category_id,json=dappCategoryId,proto3" json:"dapp_category_id,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DappNavigation) Reset() {
	*x = DappNavigation{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DappNavigation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DappNavigation) ProtoMessage() {}

func (x *DappNavigation) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DappNavigation.ProtoReflect.Descriptor instead.
func (*DappNavigation) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{25}
}

func (x *DappNavigation) GetDappCategoryId() uint64 {
	if x != nil {
		return x.DappCategoryId
	}
	return 0
}

func (x *DappNavigation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Approval struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ChainIndex     int64                  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	OwnerAddress   string                 `protobuf:"bytes,2,opt,name=owner_address,json=ownerAddress,proto3" json:"owner_address,omitempty"`
	SpenderAddress string                 `protobuf:"bytes,3,opt,name=spender_address,json=spenderAddress,proto3" json:"spender_address,omitempty"`
	TokenAddress   string                 `protobuf:"bytes,4,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	Value          string                 `protobuf:"bytes,5,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Approval) Reset() {
	*x = Approval{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Approval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Approval) ProtoMessage() {}

func (x *Approval) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Approval.ProtoReflect.Descriptor instead.
func (*Approval) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{26}
}

func (x *Approval) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Approval) GetOwnerAddress() string {
	if x != nil {
		return x.OwnerAddress
	}
	return ""
}

func (x *Approval) GetSpenderAddress() string {
	if x != nil {
		return x.SpenderAddress
	}
	return ""
}

func (x *Approval) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *Approval) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type TokenDappApproval struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         *Token                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Dapp          *Dapp                  `protobuf:"bytes,2,opt,name=dapp,proto3" json:"dapp,omitempty"`
	Approval      *Approval              `protobuf:"bytes,3,opt,name=approval,proto3" json:"approval,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenDappApproval) Reset() {
	*x = TokenDappApproval{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenDappApproval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenDappApproval) ProtoMessage() {}

func (x *TokenDappApproval) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenDappApproval.ProtoReflect.Descriptor instead.
func (*TokenDappApproval) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{27}
}

func (x *TokenDappApproval) GetToken() *Token {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *TokenDappApproval) GetDapp() *Dapp {
	if x != nil {
		return x.Dapp
	}
	return nil
}

func (x *TokenDappApproval) GetApproval() *Approval {
	if x != nil {
		return x.Approval
	}
	return nil
}

type UserApprovedDapp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Addresses     []string               `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	Dapp          *Dapp                  `protobuf:"bytes,2,opt,name=dapp,proto3" json:"dapp,omitempty"`
	Network       *BlockchainNetwork     `protobuf:"bytes,3,opt,name=network,proto3" json:"network,omitempty"`
	Spender       string                 `protobuf:"bytes,4,opt,name=spender,proto3" json:"spender,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserApprovedDapp) Reset() {
	*x = UserApprovedDapp{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserApprovedDapp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserApprovedDapp) ProtoMessage() {}

func (x *UserApprovedDapp) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserApprovedDapp.ProtoReflect.Descriptor instead.
func (*UserApprovedDapp) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{28}
}

func (x *UserApprovedDapp) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *UserApprovedDapp) GetDapp() *Dapp {
	if x != nil {
		return x.Dapp
	}
	return nil
}

func (x *UserApprovedDapp) GetNetwork() *BlockchainNetwork {
	if x != nil {
		return x.Network
	}
	return nil
}

func (x *UserApprovedDapp) GetSpender() string {
	if x != nil {
		return x.Spender
	}
	return ""
}

type TokenApproval struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         *Token                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Approval      *Approval              `protobuf:"bytes,2,opt,name=approval,proto3" json:"approval,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenApproval) Reset() {
	*x = TokenApproval{}
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenApproval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenApproval) ProtoMessage() {}

func (x *TokenApproval) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_dapp_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenApproval.ProtoReflect.Descriptor instead.
func (*TokenApproval) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_dapp_proto_rawDescGZIP(), []int{29}
}

func (x *TokenApproval) GetToken() *Token {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *TokenApproval) GetApproval() *Approval {
	if x != nil {
		return x.Approval
	}
	return nil
}

var File_api_wallet_v1_dapp_proto protoreflect.FileDescriptor

const file_api_wallet_v1_dapp_proto_rawDesc = "" +
	"\n" +
	"\x18api/wallet/v1/dapp.proto\x12\rapi.wallet.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1aapi/wallet/v1/wallet.proto\"T\n" +
	"\x18ListApprovedAddressesReq\x128\n" +
	"\taddresses\x18\x01 \x03(\v2\x1a.api.wallet.v1.UserAddressR\taddresses\"L\n" +
	"\x1aListApprovedAddressesReply\x12.\n" +
	"\x04list\x18\x01 \x03(\v2\x1a.api.wallet.v1.UserAddressR\x04list\"\x8e\x01\n" +
	"\x1bListTokenApprovalsByDappReq\x120\n" +
	"\x0fspender_address\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x0espenderAddress\x12\x1f\n" +
	"\vchain_index\x18\x02 \x01(\x03R\n" +
	"chainIndex\x12\x1c\n" +
	"\taddresses\x18\x03 \x03(\tR\taddresses\"Q\n" +
	"\x1dListTokenApprovalsByDappReply\x120\n" +
	"\x04list\x18\x01 \x03(\v2\x1c.api.wallet.v1.TokenApprovalR\x04list\"\\\n" +
	"%ListApprovedDappsByUserAddressesReply\x123\n" +
	"\x04list\x18\x01 \x03(\v2\x1f.api.wallet.v1.UserApprovedDappR\x04list\"Z\n" +
	"\x1eListApprovalByUserAddressesReq\x128\n" +
	"\taddresses\x18\x01 \x03(\v2\x1a.api.wallet.v1.UserAddressR\taddresses\"H\n" +
	"\vUserAddress\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\tR\aaddress\x12\x1f\n" +
	"\vchain_index\x18\x02 \x01(\x03R\n" +
	"chainIndex\"b\n" +
	"\x1cListApprovalByUserAddressReq\x12!\n" +
	"\aaddress\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\aaddress\x12\x1f\n" +
	"\vchain_index\x18\x02 \x01(\x03R\n" +
	"chainIndex\"\xc8\x01\n" +
	"\x1eListApprovalByUserAddressReply\x124\n" +
	"\aaddress\x18\x01 \x01(\v2\x1a.api.wallet.v1.UserAddressR\aaddress\x12:\n" +
	"\anetwork\x18\x02 \x01(\v2 .api.wallet.v1.BlockchainNetworkR\anetwork\x124\n" +
	"\x04list\x18\x03 \x03(\v2 .api.wallet.v1.TokenDappApprovalR\x04list\"!\n" +
	"\rSearchDappReq\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\":\n" +
	"\x0fSearchDappReply\x12'\n" +
	"\x04list\x18\x01 \x03(\v2\x13.api.wallet.v1.DappR\x04list\"i\n" +
	"\x12GetDappCategoryReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12\x1f\n" +
	"\vchain_index\x18\x02 \x01(\x03R\n" +
	"chainIndex\x12\x19\n" +
	"\bchain_id\x18\x03 \x01(\tR\achainId\"?\n" +
	"\x14GetDappCategoryReply\x12'\n" +
	"\x04list\x18\x01 \x03(\v2\x13.api.wallet.v1.DappR\x04list\"*\n" +
	"\x0fGetDappTopicReq\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"C\n" +
	"\x11GetDappTopicReply\x12.\n" +
	"\x05topic\x18\x01 \x01(\v2\x18.api.wallet.v1.DappTopicR\x05topic\"\x12\n" +
	"\x10ListDappIndexReq\"\xd1\x01\n" +
	"\x04Dapp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04logo\x18\x02 \x01(\tR\x04logo\x12\x12\n" +
	"\x04link\x18\x03 \x01(\tR\x04link\x12\x12\n" +
	"\x04tags\x18\x04 \x01(\tR\x04tags\x12\x10\n" +
	"\x03hot\x18\x05 \x01(\bR\x03hot\x12-\n" +
	"\x05i18ns\x18\x06 \x03(\v2\x17.api.wallet.v1.DappI18NR\x05i18ns\x12<\n" +
	"\bnetworks\x18\a \x03(\v2 .api.wallet.v1.BlockchainNetworkR\bnetworks\"8\n" +
	"\bDappI18N\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\asummary\x18\x02 \x01(\tR\asummary\".\n" +
	"\x11BlockchainNetwork\x12\x19\n" +
	"\bchain_id\x18\x01 \x01(\tR\achainId\"\x93\x01\n" +
	"\fDappCategory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x18\n" +
	"\asummary\x18\x03 \x01(\tR\asummary\x12\x1a\n" +
	"\blanguage\x18\x04 \x01(\tR\blanguage\x12)\n" +
	"\x05dapps\x18\x05 \x03(\v2\x13.api.wallet.v1.DappR\x05dapps\"\x8d\x02\n" +
	"\tDappTopic\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12%\n" +
	"\x0ebackground_url\x18\x02 \x01(\tR\rbackgroundUrl\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x18\n" +
	"\asummary\x18\x04 \x01(\tR\asummary\x12\x1a\n" +
	"\blanguage\x18\x05 \x01(\tR\blanguage\x12\x14\n" +
	"\x05title\x18\x06 \x01(\tR\x05title\x12\x1b\n" +
	"\ttop_title\x18\a \x01(\tR\btopTitle\x12!\n" +
	"\fbottom_title\x18\b \x01(\tR\vbottomTitle\x12)\n" +
	"\x05dapps\x18\t \x03(\v2\x13.api.wallet.v1.DappR\x05dapps\"t\n" +
	"\tDappIndex\x12.\n" +
	"\x05topic\x18\x01 \x01(\v2\x18.api.wallet.v1.DappTopicR\x05topic\x127\n" +
	"\bcategory\x18\x02 \x01(\v2\x1b.api.wallet.v1.DappCategoryR\bcategory\"B\n" +
	"\x12ListDappIndexReply\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.api.wallet.v1.DappIndexR\x04list\"\x13\n" +
	"\x11ListNavigationReq\"H\n" +
	"\x13ListNavigationReply\x121\n" +
	"\x04list\x18\x01 \x03(\v2\x1d.api.wallet.v1.DappNavigationR\x04list\"N\n" +
	"\x0eDappNavigation\x12(\n" +
	"\x10dapp_category_id\x18\x01 \x01(\x04R\x0edappCategoryId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\xb4\x01\n" +
	"\bApproval\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12#\n" +
	"\rowner_address\x18\x02 \x01(\tR\fownerAddress\x12'\n" +
	"\x0fspender_address\x18\x03 \x01(\tR\x0espenderAddress\x12#\n" +
	"\rtoken_address\x18\x04 \x01(\tR\ftokenAddress\x12\x14\n" +
	"\x05value\x18\x05 \x01(\tR\x05value\"\x9d\x01\n" +
	"\x11TokenDappApproval\x12*\n" +
	"\x05token\x18\x01 \x01(\v2\x14.api.wallet.v1.TokenR\x05token\x12'\n" +
	"\x04dapp\x18\x02 \x01(\v2\x13.api.wallet.v1.DappR\x04dapp\x123\n" +
	"\bapproval\x18\x03 \x01(\v2\x17.api.wallet.v1.ApprovalR\bapproval\"\xaf\x01\n" +
	"\x10UserApprovedDapp\x12\x1c\n" +
	"\taddresses\x18\x01 \x03(\tR\taddresses\x12'\n" +
	"\x04dapp\x18\x02 \x01(\v2\x13.api.wallet.v1.DappR\x04dapp\x12:\n" +
	"\anetwork\x18\x03 \x01(\v2 .api.wallet.v1.BlockchainNetworkR\anetwork\x12\x18\n" +
	"\aspender\x18\x04 \x01(\tR\aspender\"p\n" +
	"\rTokenApproval\x12*\n" +
	"\x05token\x18\x01 \x01(\v2\x14.api.wallet.v1.TokenR\x05token\x123\n" +
	"\bapproval\x18\x02 \x01(\v2\x17.api.wallet.v1.ApprovalR\bapproval2\xbb\t\n" +
	"\aDappSrv\x12s\n" +
	"\x0eListNavigation\x12 .api.wallet.v1.ListNavigationReq\x1a\".api.wallet.v1.ListNavigationReply\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/v1/dapp/navigation\x12k\n" +
	"\rListDappIndex\x12\x1f.api.wallet.v1.ListDappIndexReq\x1a!.api.wallet.v1.ListDappIndexReply\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/v1/dapp/index\x12m\n" +
	"\fGetDappTopic\x12\x1e.api.wallet.v1.GetDappTopicReq\x1a .api.wallet.v1.GetDappTopicReply\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/v1/dapp/topic/{id}\x12y\n" +
	"\x0fGetDappCategory\x12!.api.wallet.v1.GetDappCategoryReq\x1a#.api.wallet.v1.GetDappCategoryReply\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/v1/dapp/category/{id}\x12\\\n" +
	"\n" +
	"SearchDapp\x12\x1c.api.wallet.v1.SearchDappReq\x1a\x1e.api.wallet.v1.SearchDappReply\"\x10\x82\xd3\xe4\x93\x02\n" +
	"\x12\b/v1/dapp\x12\x93\x01\n" +
	"\x15ListApprovedAddresses\x12'.api.wallet.v1.ListApprovedAddressesReq\x1a).api.wallet.v1.ListApprovedAddressesReply\"&\x82\xd3\xe4\x93\x02 :\x01*\"\x1b/v1/dapp/approved_addresses\x12\x9d\x01\n" +
	"\x19ListApprovalByUserAddress\x12+.api.wallet.v1.ListApprovalByUserAddressReq\x1a-.api.wallet.v1.ListApprovalByUserAddressReply\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/v1/dapp/address/approval\x12\xaf\x01\n" +
	" ListApprovedDappsByUserAddresses\x12-.api.wallet.v1.ListApprovalByUserAddressesReq\x1a4.api.wallet.v1.ListApprovedDappsByUserAddressesReply\"&\x82\xd3\xe4\x93\x02 :\x01*\"\x1b/v1/dapp/addresses/approval\x12\x9d\x01\n" +
	"\x18ListTokenApprovalsByDapp\x12*.api.wallet.v1.ListTokenApprovalsByDappReq\x1a,.api.wallet.v1.ListTokenApprovalsByDappReply\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/v1/dapp/blockchain/approvalB\x91\x01\n" +
	"\x11com.api.wallet.v1B\tDappProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_dapp_proto_rawDescOnce sync.Once
	file_api_wallet_v1_dapp_proto_rawDescData []byte
)

func file_api_wallet_v1_dapp_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_dapp_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_dapp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_dapp_proto_rawDesc), len(file_api_wallet_v1_dapp_proto_rawDesc)))
	})
	return file_api_wallet_v1_dapp_proto_rawDescData
}

var file_api_wallet_v1_dapp_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_api_wallet_v1_dapp_proto_goTypes = []any{
	(*ListApprovedAddressesReq)(nil),              // 0: api.wallet.v1.ListApprovedAddressesReq
	(*ListApprovedAddressesReply)(nil),            // 1: api.wallet.v1.ListApprovedAddressesReply
	(*ListTokenApprovalsByDappReq)(nil),           // 2: api.wallet.v1.ListTokenApprovalsByDappReq
	(*ListTokenApprovalsByDappReply)(nil),         // 3: api.wallet.v1.ListTokenApprovalsByDappReply
	(*ListApprovedDappsByUserAddressesReply)(nil), // 4: api.wallet.v1.ListApprovedDappsByUserAddressesReply
	(*ListApprovalByUserAddressesReq)(nil),        // 5: api.wallet.v1.ListApprovalByUserAddressesReq
	(*UserAddress)(nil),                           // 6: api.wallet.v1.UserAddress
	(*ListApprovalByUserAddressReq)(nil),          // 7: api.wallet.v1.ListApprovalByUserAddressReq
	(*ListApprovalByUserAddressReply)(nil),        // 8: api.wallet.v1.ListApprovalByUserAddressReply
	(*SearchDappReq)(nil),                         // 9: api.wallet.v1.SearchDappReq
	(*SearchDappReply)(nil),                       // 10: api.wallet.v1.SearchDappReply
	(*GetDappCategoryReq)(nil),                    // 11: api.wallet.v1.GetDappCategoryReq
	(*GetDappCategoryReply)(nil),                  // 12: api.wallet.v1.GetDappCategoryReply
	(*GetDappTopicReq)(nil),                       // 13: api.wallet.v1.GetDappTopicReq
	(*GetDappTopicReply)(nil),                     // 14: api.wallet.v1.GetDappTopicReply
	(*ListDappIndexReq)(nil),                      // 15: api.wallet.v1.ListDappIndexReq
	(*Dapp)(nil),                                  // 16: api.wallet.v1.Dapp
	(*DappI18N)(nil),                              // 17: api.wallet.v1.DappI18N
	(*BlockchainNetwork)(nil),                     // 18: api.wallet.v1.BlockchainNetwork
	(*DappCategory)(nil),                          // 19: api.wallet.v1.DappCategory
	(*DappTopic)(nil),                             // 20: api.wallet.v1.DappTopic
	(*DappIndex)(nil),                             // 21: api.wallet.v1.DappIndex
	(*ListDappIndexReply)(nil),                    // 22: api.wallet.v1.ListDappIndexReply
	(*ListNavigationReq)(nil),                     // 23: api.wallet.v1.ListNavigationReq
	(*ListNavigationReply)(nil),                   // 24: api.wallet.v1.ListNavigationReply
	(*DappNavigation)(nil),                        // 25: api.wallet.v1.DappNavigation
	(*Approval)(nil),                              // 26: api.wallet.v1.Approval
	(*TokenDappApproval)(nil),                     // 27: api.wallet.v1.TokenDappApproval
	(*UserApprovedDapp)(nil),                      // 28: api.wallet.v1.UserApprovedDapp
	(*TokenApproval)(nil),                         // 29: api.wallet.v1.TokenApproval
	(*Token)(nil),                                 // 30: api.wallet.v1.Token
}
var file_api_wallet_v1_dapp_proto_depIdxs = []int32{
	6,  // 0: api.wallet.v1.ListApprovedAddressesReq.addresses:type_name -> api.wallet.v1.UserAddress
	6,  // 1: api.wallet.v1.ListApprovedAddressesReply.list:type_name -> api.wallet.v1.UserAddress
	29, // 2: api.wallet.v1.ListTokenApprovalsByDappReply.list:type_name -> api.wallet.v1.TokenApproval
	28, // 3: api.wallet.v1.ListApprovedDappsByUserAddressesReply.list:type_name -> api.wallet.v1.UserApprovedDapp
	6,  // 4: api.wallet.v1.ListApprovalByUserAddressesReq.addresses:type_name -> api.wallet.v1.UserAddress
	6,  // 5: api.wallet.v1.ListApprovalByUserAddressReply.address:type_name -> api.wallet.v1.UserAddress
	18, // 6: api.wallet.v1.ListApprovalByUserAddressReply.network:type_name -> api.wallet.v1.BlockchainNetwork
	27, // 7: api.wallet.v1.ListApprovalByUserAddressReply.list:type_name -> api.wallet.v1.TokenDappApproval
	16, // 8: api.wallet.v1.SearchDappReply.list:type_name -> api.wallet.v1.Dapp
	16, // 9: api.wallet.v1.GetDappCategoryReply.list:type_name -> api.wallet.v1.Dapp
	20, // 10: api.wallet.v1.GetDappTopicReply.topic:type_name -> api.wallet.v1.DappTopic
	17, // 11: api.wallet.v1.Dapp.i18ns:type_name -> api.wallet.v1.DappI18N
	18, // 12: api.wallet.v1.Dapp.networks:type_name -> api.wallet.v1.BlockchainNetwork
	16, // 13: api.wallet.v1.DappCategory.dapps:type_name -> api.wallet.v1.Dapp
	16, // 14: api.wallet.v1.DappTopic.dapps:type_name -> api.wallet.v1.Dapp
	20, // 15: api.wallet.v1.DappIndex.topic:type_name -> api.wallet.v1.DappTopic
	19, // 16: api.wallet.v1.DappIndex.category:type_name -> api.wallet.v1.DappCategory
	21, // 17: api.wallet.v1.ListDappIndexReply.list:type_name -> api.wallet.v1.DappIndex
	25, // 18: api.wallet.v1.ListNavigationReply.list:type_name -> api.wallet.v1.DappNavigation
	30, // 19: api.wallet.v1.TokenDappApproval.token:type_name -> api.wallet.v1.Token
	16, // 20: api.wallet.v1.TokenDappApproval.dapp:type_name -> api.wallet.v1.Dapp
	26, // 21: api.wallet.v1.TokenDappApproval.approval:type_name -> api.wallet.v1.Approval
	16, // 22: api.wallet.v1.UserApprovedDapp.dapp:type_name -> api.wallet.v1.Dapp
	18, // 23: api.wallet.v1.UserApprovedDapp.network:type_name -> api.wallet.v1.BlockchainNetwork
	30, // 24: api.wallet.v1.TokenApproval.token:type_name -> api.wallet.v1.Token
	26, // 25: api.wallet.v1.TokenApproval.approval:type_name -> api.wallet.v1.Approval
	23, // 26: api.wallet.v1.DappSrv.ListNavigation:input_type -> api.wallet.v1.ListNavigationReq
	15, // 27: api.wallet.v1.DappSrv.ListDappIndex:input_type -> api.wallet.v1.ListDappIndexReq
	13, // 28: api.wallet.v1.DappSrv.GetDappTopic:input_type -> api.wallet.v1.GetDappTopicReq
	11, // 29: api.wallet.v1.DappSrv.GetDappCategory:input_type -> api.wallet.v1.GetDappCategoryReq
	9,  // 30: api.wallet.v1.DappSrv.SearchDapp:input_type -> api.wallet.v1.SearchDappReq
	0,  // 31: api.wallet.v1.DappSrv.ListApprovedAddresses:input_type -> api.wallet.v1.ListApprovedAddressesReq
	7,  // 32: api.wallet.v1.DappSrv.ListApprovalByUserAddress:input_type -> api.wallet.v1.ListApprovalByUserAddressReq
	5,  // 33: api.wallet.v1.DappSrv.ListApprovedDappsByUserAddresses:input_type -> api.wallet.v1.ListApprovalByUserAddressesReq
	2,  // 34: api.wallet.v1.DappSrv.ListTokenApprovalsByDapp:input_type -> api.wallet.v1.ListTokenApprovalsByDappReq
	24, // 35: api.wallet.v1.DappSrv.ListNavigation:output_type -> api.wallet.v1.ListNavigationReply
	22, // 36: api.wallet.v1.DappSrv.ListDappIndex:output_type -> api.wallet.v1.ListDappIndexReply
	14, // 37: api.wallet.v1.DappSrv.GetDappTopic:output_type -> api.wallet.v1.GetDappTopicReply
	12, // 38: api.wallet.v1.DappSrv.GetDappCategory:output_type -> api.wallet.v1.GetDappCategoryReply
	10, // 39: api.wallet.v1.DappSrv.SearchDapp:output_type -> api.wallet.v1.SearchDappReply
	1,  // 40: api.wallet.v1.DappSrv.ListApprovedAddresses:output_type -> api.wallet.v1.ListApprovedAddressesReply
	8,  // 41: api.wallet.v1.DappSrv.ListApprovalByUserAddress:output_type -> api.wallet.v1.ListApprovalByUserAddressReply
	4,  // 42: api.wallet.v1.DappSrv.ListApprovedDappsByUserAddresses:output_type -> api.wallet.v1.ListApprovedDappsByUserAddressesReply
	3,  // 43: api.wallet.v1.DappSrv.ListTokenApprovalsByDapp:output_type -> api.wallet.v1.ListTokenApprovalsByDappReply
	35, // [35:44] is the sub-list for method output_type
	26, // [26:35] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_dapp_proto_init() }
func file_api_wallet_v1_dapp_proto_init() {
	if File_api_wallet_v1_dapp_proto != nil {
		return
	}
	file_api_wallet_v1_wallet_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_dapp_proto_rawDesc), len(file_api_wallet_v1_dapp_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_dapp_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_dapp_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_dapp_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_dapp_proto = out.File
	file_api_wallet_v1_dapp_proto_goTypes = nil
	file_api_wallet_v1_dapp_proto_depIdxs = nil
}
