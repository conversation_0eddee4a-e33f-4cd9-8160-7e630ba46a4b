// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/wallet/v1/user_guide.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserGuideSrv_ListUserGuideCategories_FullMethodName = "/api.wallet.v1.UserGuideSrv/ListUserGuideCategories"
	UserGuideSrv_ListUserGuides_FullMethodName          = "/api.wallet.v1.UserGuideSrv/ListUserGuides"
	UserGuideSrv_ListUserGuideContent_FullMethodName    = "/api.wallet.v1.UserGuideSrv/ListUserGuideContent"
	UserGuideSrv_SearchUserGuides_FullMethodName        = "/api.wallet.v1.UserGuideSrv/SearchUserGuides"
)

// UserGuideSrvClient is the client API for UserGuideSrv service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserGuideSrvClient interface {
	// 获取用户指南分类列表
	ListUserGuideCategories(ctx context.Context, in *ListUserGuideCategoriesReq, opts ...grpc.CallOption) (*ListUserGuideCategoriesReply, error)
	// 根据分类查询用户指南列表
	ListUserGuides(ctx context.Context, in *ListUserGuidesReq, opts ...grpc.CallOption) (*ListUserGuidesReply, error)
	// 获取用户指南内容列表
	ListUserGuideContent(ctx context.Context, in *ListUserGuideContentReq, opts ...grpc.CallOption) (*ListUserGuideContentReply, error)
	// 模糊搜索用户指南
	SearchUserGuides(ctx context.Context, in *SearchUserGuidesReq, opts ...grpc.CallOption) (*SearchUserGuidesReply, error)
}

type userGuideSrvClient struct {
	cc grpc.ClientConnInterface
}

func NewUserGuideSrvClient(cc grpc.ClientConnInterface) UserGuideSrvClient {
	return &userGuideSrvClient{cc}
}

func (c *userGuideSrvClient) ListUserGuideCategories(ctx context.Context, in *ListUserGuideCategoriesReq, opts ...grpc.CallOption) (*ListUserGuideCategoriesReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListUserGuideCategoriesReply)
	err := c.cc.Invoke(ctx, UserGuideSrv_ListUserGuideCategories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideSrvClient) ListUserGuides(ctx context.Context, in *ListUserGuidesReq, opts ...grpc.CallOption) (*ListUserGuidesReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListUserGuidesReply)
	err := c.cc.Invoke(ctx, UserGuideSrv_ListUserGuides_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideSrvClient) ListUserGuideContent(ctx context.Context, in *ListUserGuideContentReq, opts ...grpc.CallOption) (*ListUserGuideContentReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListUserGuideContentReply)
	err := c.cc.Invoke(ctx, UserGuideSrv_ListUserGuideContent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userGuideSrvClient) SearchUserGuides(ctx context.Context, in *SearchUserGuidesReq, opts ...grpc.CallOption) (*SearchUserGuidesReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchUserGuidesReply)
	err := c.cc.Invoke(ctx, UserGuideSrv_SearchUserGuides_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserGuideSrvServer is the server API for UserGuideSrv service.
// All implementations must embed UnimplementedUserGuideSrvServer
// for forward compatibility.
type UserGuideSrvServer interface {
	// 获取用户指南分类列表
	ListUserGuideCategories(context.Context, *ListUserGuideCategoriesReq) (*ListUserGuideCategoriesReply, error)
	// 根据分类查询用户指南列表
	ListUserGuides(context.Context, *ListUserGuidesReq) (*ListUserGuidesReply, error)
	// 获取用户指南内容列表
	ListUserGuideContent(context.Context, *ListUserGuideContentReq) (*ListUserGuideContentReply, error)
	// 模糊搜索用户指南
	SearchUserGuides(context.Context, *SearchUserGuidesReq) (*SearchUserGuidesReply, error)
	mustEmbedUnimplementedUserGuideSrvServer()
}

// UnimplementedUserGuideSrvServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserGuideSrvServer struct{}

func (UnimplementedUserGuideSrvServer) ListUserGuideCategories(context.Context, *ListUserGuideCategoriesReq) (*ListUserGuideCategoriesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserGuideCategories not implemented")
}
func (UnimplementedUserGuideSrvServer) ListUserGuides(context.Context, *ListUserGuidesReq) (*ListUserGuidesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserGuides not implemented")
}
func (UnimplementedUserGuideSrvServer) ListUserGuideContent(context.Context, *ListUserGuideContentReq) (*ListUserGuideContentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserGuideContent not implemented")
}
func (UnimplementedUserGuideSrvServer) SearchUserGuides(context.Context, *SearchUserGuidesReq) (*SearchUserGuidesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchUserGuides not implemented")
}
func (UnimplementedUserGuideSrvServer) mustEmbedUnimplementedUserGuideSrvServer() {}
func (UnimplementedUserGuideSrvServer) testEmbeddedByValue()                      {}

// UnsafeUserGuideSrvServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserGuideSrvServer will
// result in compilation errors.
type UnsafeUserGuideSrvServer interface {
	mustEmbedUnimplementedUserGuideSrvServer()
}

func RegisterUserGuideSrvServer(s grpc.ServiceRegistrar, srv UserGuideSrvServer) {
	// If the following call pancis, it indicates UnimplementedUserGuideSrvServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserGuideSrv_ServiceDesc, srv)
}

func _UserGuideSrv_ListUserGuideCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserGuideCategoriesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideSrvServer).ListUserGuideCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideSrv_ListUserGuideCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideSrvServer).ListUserGuideCategories(ctx, req.(*ListUserGuideCategoriesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideSrv_ListUserGuides_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserGuidesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideSrvServer).ListUserGuides(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideSrv_ListUserGuides_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideSrvServer).ListUserGuides(ctx, req.(*ListUserGuidesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideSrv_ListUserGuideContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserGuideContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideSrvServer).ListUserGuideContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideSrv_ListUserGuideContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideSrvServer).ListUserGuideContent(ctx, req.(*ListUserGuideContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserGuideSrv_SearchUserGuides_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchUserGuidesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserGuideSrvServer).SearchUserGuides(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserGuideSrv_SearchUserGuides_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserGuideSrvServer).SearchUserGuides(ctx, req.(*SearchUserGuidesReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserGuideSrv_ServiceDesc is the grpc.ServiceDesc for UserGuideSrv service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserGuideSrv_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wallet.v1.UserGuideSrv",
	HandlerType: (*UserGuideSrvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListUserGuideCategories",
			Handler:    _UserGuideSrv_ListUserGuideCategories_Handler,
		},
		{
			MethodName: "ListUserGuides",
			Handler:    _UserGuideSrv_ListUserGuides_Handler,
		},
		{
			MethodName: "ListUserGuideContent",
			Handler:    _UserGuideSrv_ListUserGuideContent_Handler,
		},
		{
			MethodName: "SearchUserGuides",
			Handler:    _UserGuideSrv_SearchUserGuides_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wallet/v1/user_guide.proto",
}
