// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/app_version.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "byd_wallet/api/walletadmin/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取最新版本请求
type GetLatestAppVersionReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// APP类型。android,ios
	AppType       string `protobuf:"bytes,1,opt,name=app_type,json=appType,proto3" json:"app_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLatestAppVersionReq) Reset() {
	*x = GetLatestAppVersionReq{}
	mi := &file_api_wallet_v1_app_version_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLatestAppVersionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestAppVersionReq) ProtoMessage() {}

func (x *GetLatestAppVersionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_app_version_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestAppVersionReq.ProtoReflect.Descriptor instead.
func (*GetLatestAppVersionReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_app_version_proto_rawDescGZIP(), []int{0}
}

func (x *GetLatestAppVersionReq) GetAppType() string {
	if x != nil {
		return x.AppType
	}
	return ""
}

var File_api_wallet_v1_app_version_proto protoreflect.FileDescriptor

const file_api_wallet_v1_app_version_proto_rawDesc = "" +
	"\n" +
	"\x1fapi/wallet/v1/app_version.proto\x12\rapi.wallet.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a$api/walletadmin/v1/app_version.proto\"<\n" +
	"\x16GetLatestAppVersionReq\x12\"\n" +
	"\bapp_type\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\aappType2\x97\x01\n" +
	"\x11AppVersionService\x12\x81\x01\n" +
	"\x13GetLatestAppVersion\x12%.api.wallet.v1.GetLatestAppVersionReq\x1a\".api.walletadmin.v1.AppVersionInfo\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/v1/app-versions/latestB\x97\x01\n" +
	"\x11com.api.wallet.v1B\x0fAppVersionProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_app_version_proto_rawDescOnce sync.Once
	file_api_wallet_v1_app_version_proto_rawDescData []byte
)

func file_api_wallet_v1_app_version_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_app_version_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_app_version_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_app_version_proto_rawDesc), len(file_api_wallet_v1_app_version_proto_rawDesc)))
	})
	return file_api_wallet_v1_app_version_proto_rawDescData
}

var file_api_wallet_v1_app_version_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_wallet_v1_app_version_proto_goTypes = []any{
	(*GetLatestAppVersionReq)(nil), // 0: api.wallet.v1.GetLatestAppVersionReq
	(*v1.AppVersionInfo)(nil),      // 1: api.walletadmin.v1.AppVersionInfo
}
var file_api_wallet_v1_app_version_proto_depIdxs = []int32{
	0, // 0: api.wallet.v1.AppVersionService.GetLatestAppVersion:input_type -> api.wallet.v1.GetLatestAppVersionReq
	1, // 1: api.wallet.v1.AppVersionService.GetLatestAppVersion:output_type -> api.walletadmin.v1.AppVersionInfo
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_app_version_proto_init() }
func file_api_wallet_v1_app_version_proto_init() {
	if File_api_wallet_v1_app_version_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_app_version_proto_rawDesc), len(file_api_wallet_v1_app_version_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_app_version_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_app_version_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_app_version_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_app_version_proto = out.File
	file_api_wallet_v1_app_version_proto_goTypes = nil
	file_api_wallet_v1_app_version_proto_depIdxs = nil
}
