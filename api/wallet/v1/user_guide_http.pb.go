// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/wallet/v1/user_guide.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserGuideSrvListUserGuideCategories = "/api.wallet.v1.UserGuideSrv/ListUserGuideCategories"
const OperationUserGuideSrvListUserGuideContent = "/api.wallet.v1.UserGuideSrv/ListUserGuideContent"
const OperationUserGuideSrvListUserGuides = "/api.wallet.v1.UserGuideSrv/ListUserGuides"
const OperationUserGuideSrvSearchUserGuides = "/api.wallet.v1.UserGuideSrv/SearchUserGuides"

type UserGuideSrvHTTPServer interface {
	// ListUserGuideCategories 获取用户指南分类列表
	ListUserGuideCategories(context.Context, *ListUserGuideCategoriesReq) (*ListUserGuideCategoriesReply, error)
	// ListUserGuideContent 获取用户指南内容列表
	ListUserGuideContent(context.Context, *ListUserGuideContentReq) (*ListUserGuideContentReply, error)
	// ListUserGuides 根据分类查询用户指南列表
	ListUserGuides(context.Context, *ListUserGuidesReq) (*ListUserGuidesReply, error)
	// SearchUserGuides 模糊搜索用户指南
	SearchUserGuides(context.Context, *SearchUserGuidesReq) (*SearchUserGuidesReply, error)
}

func RegisterUserGuideSrvHTTPServer(s *http.Server, srv UserGuideSrvHTTPServer) {
	r := s.Route("/")
	r.GET("/v1/user-guide-categories", _UserGuideSrv_ListUserGuideCategories0_HTTP_Handler(srv))
	r.GET("/v1/user-guides", _UserGuideSrv_ListUserGuides0_HTTP_Handler(srv))
	r.GET("/v1/user-guides/{id}/contents", _UserGuideSrv_ListUserGuideContent0_HTTP_Handler(srv))
	r.GET("/v1/user-guides/search", _UserGuideSrv_SearchUserGuides0_HTTP_Handler(srv))
}

func _UserGuideSrv_ListUserGuideCategories0_HTTP_Handler(srv UserGuideSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserGuideCategoriesReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideSrvListUserGuideCategories)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserGuideCategories(ctx, req.(*ListUserGuideCategoriesReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserGuideCategoriesReply)
		return ctx.Result(200, reply)
	}
}

func _UserGuideSrv_ListUserGuides0_HTTP_Handler(srv UserGuideSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserGuidesReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideSrvListUserGuides)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserGuides(ctx, req.(*ListUserGuidesReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserGuidesReply)
		return ctx.Result(200, reply)
	}
}

func _UserGuideSrv_ListUserGuideContent0_HTTP_Handler(srv UserGuideSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListUserGuideContentReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideSrvListUserGuideContent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserGuideContent(ctx, req.(*ListUserGuideContentReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListUserGuideContentReply)
		return ctx.Result(200, reply)
	}
}

func _UserGuideSrv_SearchUserGuides0_HTTP_Handler(srv UserGuideSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SearchUserGuidesReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserGuideSrvSearchUserGuides)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SearchUserGuides(ctx, req.(*SearchUserGuidesReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SearchUserGuidesReply)
		return ctx.Result(200, reply)
	}
}

type UserGuideSrvHTTPClient interface {
	ListUserGuideCategories(ctx context.Context, req *ListUserGuideCategoriesReq, opts ...http.CallOption) (rsp *ListUserGuideCategoriesReply, err error)
	ListUserGuideContent(ctx context.Context, req *ListUserGuideContentReq, opts ...http.CallOption) (rsp *ListUserGuideContentReply, err error)
	ListUserGuides(ctx context.Context, req *ListUserGuidesReq, opts ...http.CallOption) (rsp *ListUserGuidesReply, err error)
	SearchUserGuides(ctx context.Context, req *SearchUserGuidesReq, opts ...http.CallOption) (rsp *SearchUserGuidesReply, err error)
}

type UserGuideSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewUserGuideSrvHTTPClient(client *http.Client) UserGuideSrvHTTPClient {
	return &UserGuideSrvHTTPClientImpl{client}
}

func (c *UserGuideSrvHTTPClientImpl) ListUserGuideCategories(ctx context.Context, in *ListUserGuideCategoriesReq, opts ...http.CallOption) (*ListUserGuideCategoriesReply, error) {
	var out ListUserGuideCategoriesReply
	pattern := "/v1/user-guide-categories"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGuideSrvListUserGuideCategories))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideSrvHTTPClientImpl) ListUserGuideContent(ctx context.Context, in *ListUserGuideContentReq, opts ...http.CallOption) (*ListUserGuideContentReply, error) {
	var out ListUserGuideContentReply
	pattern := "/v1/user-guides/{id}/contents"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGuideSrvListUserGuideContent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideSrvHTTPClientImpl) ListUserGuides(ctx context.Context, in *ListUserGuidesReq, opts ...http.CallOption) (*ListUserGuidesReply, error) {
	var out ListUserGuidesReply
	pattern := "/v1/user-guides"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGuideSrvListUserGuides))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserGuideSrvHTTPClientImpl) SearchUserGuides(ctx context.Context, in *SearchUserGuidesReq, opts ...http.CallOption) (*SearchUserGuidesReply, error) {
	var out SearchUserGuidesReply
	pattern := "/v1/user-guides/search"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserGuideSrvSearchUserGuides))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
