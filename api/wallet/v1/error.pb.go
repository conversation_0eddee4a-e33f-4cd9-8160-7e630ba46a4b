// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/error.proto

package v1

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorReason int32

const (
	// 内部服务错误
	ErrorReason_INTERNAL_SERVER ErrorReason = 0
	// 参数校验错误
	ErrorReason_VALIDATOR ErrorReason = 1
	// 波场租赁错误
	ErrorReason_RENT ErrorReason = 2
	// boss wallet  user 相关
	ErrorReason_BOSSID_LOCK   ErrorReason = 3
	ErrorReason_BOSSID_EXISTS ErrorReason = 4
	// 数据已存在
	ErrorReason_DATA_EXISTS ErrorReason = 5
)

// Enum value maps for ErrorReason.
var (
	ErrorReason_name = map[int32]string{
		0: "INTERNAL_SERVER",
		1: "VALIDATOR",
		2: "RENT",
		3: "BOSSID_LOCK",
		4: "BOSSID_EXISTS",
		5: "DATA_EXISTS",
	}
	ErrorReason_value = map[string]int32{
		"INTERNAL_SERVER": 0,
		"VALIDATOR":       1,
		"RENT":            2,
		"BOSSID_LOCK":     3,
		"BOSSID_EXISTS":   4,
		"DATA_EXISTS":     5,
	}
)

func (x ErrorReason) Enum() *ErrorReason {
	p := new(ErrorReason)
	*p = x
	return p
}

func (x ErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_wallet_v1_error_proto_enumTypes[0].Descriptor()
}

func (ErrorReason) Type() protoreflect.EnumType {
	return &file_api_wallet_v1_error_proto_enumTypes[0]
}

func (x ErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorReason.Descriptor instead.
func (ErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_api_wallet_v1_error_proto_rawDescGZIP(), []int{0}
}

var File_api_wallet_v1_error_proto protoreflect.FileDescriptor

const file_api_wallet_v1_error_proto_rawDesc = "" +
	"\n" +
	"\x19api/wallet/v1/error.proto\x12\rapi.wallet.v1\x1a\x13errors/errors.proto*\x94\x01\n" +
	"\vErrorReason\x12\x18\n" +
	"\x0fINTERNAL_SERVER\x10\x00\x1a\x03\xa8Ed\x12\x12\n" +
	"\tVALIDATOR\x10\x01\x1a\x03\xa8Ee\x12\r\n" +
	"\x04RENT\x10\x02\x1a\x03\xa8Ef\x12\x14\n" +
	"\vBOSSID_LOCK\x10\x03\x1a\x03\xa8Eg\x12\x16\n" +
	"\rBOSSID_EXISTS\x10\x04\x1a\x03\xa8Eh\x12\x14\n" +
	"\vDATA_EXISTS\x10\x05\x1a\x03\xa8Ei\x1a\x04\xa0E\xf4\x03B\x92\x01\n" +
	"\x11com.api.wallet.v1B\n" +
	"ErrorProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_error_proto_rawDescOnce sync.Once
	file_api_wallet_v1_error_proto_rawDescData []byte
)

func file_api_wallet_v1_error_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_error_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_error_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_error_proto_rawDesc), len(file_api_wallet_v1_error_proto_rawDesc)))
	})
	return file_api_wallet_v1_error_proto_rawDescData
}

var file_api_wallet_v1_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_wallet_v1_error_proto_goTypes = []any{
	(ErrorReason)(0), // 0: api.wallet.v1.ErrorReason
}
var file_api_wallet_v1_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_error_proto_init() }
func file_api_wallet_v1_error_proto_init() {
	if File_api_wallet_v1_error_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_error_proto_rawDesc), len(file_api_wallet_v1_error_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_wallet_v1_error_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_error_proto_depIdxs,
		EnumInfos:         file_api_wallet_v1_error_proto_enumTypes,
	}.Build()
	File_api_wallet_v1_error_proto = out.File
	file_api_wallet_v1_error_proto_goTypes = nil
	file_api_wallet_v1_error_proto_depIdxs = nil
}
