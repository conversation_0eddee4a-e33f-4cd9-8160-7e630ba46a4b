// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/wallet.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TokenWithWallet struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WalletAddress string                 `protobuf:"bytes,1,opt,name=wallet_address,json=walletAddress,proto3" json:"wallet_address,omitempty"`
	ChainIndex    int64                  `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Symbol        string                 `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Decimals      int64                  `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	LogoUrl       string                 `protobuf:"bytes,6,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	Address       string                 `protobuf:"bytes,7,opt,name=address,proto3" json:"address,omitempty"`
	ChainId       string                 `protobuf:"bytes,8,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenWithWallet) Reset() {
	*x = TokenWithWallet{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenWithWallet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenWithWallet) ProtoMessage() {}

func (x *TokenWithWallet) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenWithWallet.ProtoReflect.Descriptor instead.
func (*TokenWithWallet) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{0}
}

func (x *TokenWithWallet) GetWalletAddress() string {
	if x != nil {
		return x.WalletAddress
	}
	return ""
}

func (x *TokenWithWallet) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TokenWithWallet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TokenWithWallet) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TokenWithWallet) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *TokenWithWallet) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *TokenWithWallet) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TokenWithWallet) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

type QueryTxLatestTokenReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*TokenWithWallet     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryTxLatestTokenReply) Reset() {
	*x = QueryTxLatestTokenReply{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryTxLatestTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTxLatestTokenReply) ProtoMessage() {}

func (x *QueryTxLatestTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTxLatestTokenReply.ProtoReflect.Descriptor instead.
func (*QueryTxLatestTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{1}
}

func (x *QueryTxLatestTokenReply) GetList() []*TokenWithWallet {
	if x != nil {
		return x.List
	}
	return nil
}

type WalletAddress4Chain struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 钱包地址
	Addresses     []string `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	ChainIndex    int64    `protobuf:"varint,2,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WalletAddress4Chain) Reset() {
	*x = WalletAddress4Chain{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WalletAddress4Chain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WalletAddress4Chain) ProtoMessage() {}

func (x *WalletAddress4Chain) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WalletAddress4Chain.ProtoReflect.Descriptor instead.
func (*WalletAddress4Chain) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{2}
}

func (x *WalletAddress4Chain) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *WalletAddress4Chain) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type QueryTxLatestTokenReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Chains        []*WalletAddress4Chain `protobuf:"bytes,1,rep,name=chains,proto3" json:"chains,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryTxLatestTokenReq) Reset() {
	*x = QueryTxLatestTokenReq{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryTxLatestTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryTxLatestTokenReq) ProtoMessage() {}

func (x *QueryTxLatestTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryTxLatestTokenReq.ProtoReflect.Descriptor instead.
func (*QueryTxLatestTokenReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{3}
}

func (x *QueryTxLatestTokenReq) GetChains() []*WalletAddress4Chain {
	if x != nil {
		return x.Chains
	}
	return nil
}

type WalletAddress struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 钱包地址
	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	// 链索引
	ChainIndexes  []int64 `protobuf:"varint,2,rep,packed,name=chain_indexes,json=chainIndexes,proto3" json:"chain_indexes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WalletAddress) Reset() {
	*x = WalletAddress{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WalletAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WalletAddress) ProtoMessage() {}

func (x *WalletAddress) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WalletAddress.ProtoReflect.Descriptor instead.
func (*WalletAddress) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{4}
}

func (x *WalletAddress) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *WalletAddress) GetChainIndexes() []int64 {
	if x != nil {
		return x.ChainIndexes
	}
	return nil
}

type ListTokenBalanceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Addresses     []*WalletAddress       `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTokenBalanceReq) Reset() {
	*x = ListTokenBalanceReq{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTokenBalanceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenBalanceReq) ProtoMessage() {}

func (x *ListTokenBalanceReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenBalanceReq.ProtoReflect.Descriptor instead.
func (*ListTokenBalanceReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{5}
}

func (x *ListTokenBalanceReq) GetAddresses() []*WalletAddress {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type TokenBalance struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// token地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// token名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// token符号
	Symbol string `protobuf:"bytes,4,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// token精度
	Decimals int64 `protobuf:"varint,5,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// token原始数量
	RawBalance string `protobuf:"bytes,6,opt,name=raw_balance,json=rawBalance,proto3" json:"raw_balance,omitempty"`
	// token价格(USD)
	Price string `protobuf:"bytes,7,opt,name=price,proto3" json:"price,omitempty"`
	// token图标
	LogoUrl       string `protobuf:"bytes,8,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenBalance) Reset() {
	*x = TokenBalance{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenBalance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenBalance) ProtoMessage() {}

func (x *TokenBalance) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenBalance.ProtoReflect.Descriptor instead.
func (*TokenBalance) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{6}
}

func (x *TokenBalance) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TokenBalance) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TokenBalance) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TokenBalance) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *TokenBalance) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *TokenBalance) GetRawBalance() string {
	if x != nil {
		return x.RawBalance
	}
	return ""
}

func (x *TokenBalance) GetPrice() string {
	if x != nil {
		return x.Price
	}
	return ""
}

func (x *TokenBalance) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

type WalletBalance struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 钱包地址
	Address       string          `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Balances      []*TokenBalance `protobuf:"bytes,2,rep,name=balances,proto3" json:"balances,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WalletBalance) Reset() {
	*x = WalletBalance{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WalletBalance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WalletBalance) ProtoMessage() {}

func (x *WalletBalance) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WalletBalance.ProtoReflect.Descriptor instead.
func (*WalletBalance) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{7}
}

func (x *WalletBalance) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *WalletBalance) GetBalances() []*TokenBalance {
	if x != nil {
		return x.Balances
	}
	return nil
}

type ListTokenBalanceReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*WalletBalance       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTokenBalanceReply) Reset() {
	*x = ListTokenBalanceReply{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTokenBalanceReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenBalanceReply) ProtoMessage() {}

func (x *ListTokenBalanceReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenBalanceReply.ProtoReflect.Descriptor instead.
func (*ListTokenBalanceReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{8}
}

func (x *ListTokenBalanceReply) GetList() []*WalletBalance {
	if x != nil {
		return x.List
	}
	return nil
}

type GetTokenReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 合约地址
	Address       string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTokenReq) Reset() {
	*x = GetTokenReq{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTokenReq) ProtoMessage() {}

func (x *GetTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTokenReq.ProtoReflect.Descriptor instead.
func (*GetTokenReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{9}
}

func (x *GetTokenReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *GetTokenReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type NetworkListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NetworkListReq) Reset() {
	*x = NetworkListReq{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkListReq) ProtoMessage() {}

func (x *NetworkListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkListReq.ProtoReflect.Descriptor instead.
func (*NetworkListReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{10}
}

type NetworkListReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链列表
	List          []*Network `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NetworkListReply) Reset() {
	*x = NetworkListReply{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkListReply) ProtoMessage() {}

func (x *NetworkListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkListReply.ProtoReflect.Descriptor instead.
func (*NetworkListReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{11}
}

func (x *NetworkListReply) GetList() []*Network {
	if x != nil {
		return x.List
	}
	return nil
}

type TransactionsReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 查询地址，多个用逗号(,)隔开
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 查询合约地址，多个用逗号(,)隔开,不区分合约查询all
	ProgramId string `protobuf:"bytes,3,opt,name=program_id,json=programId,proto3" json:"program_id,omitempty"`
	Page      int64  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	Limit         int64 `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionsReq) Reset() {
	*x = TransactionsReq{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionsReq) ProtoMessage() {}

func (x *TransactionsReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionsReq.ProtoReflect.Descriptor instead.
func (*TransactionsReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{12}
}

func (x *TransactionsReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TransactionsReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *TransactionsReq) GetProgramId() string {
	if x != nil {
		return x.ProgramId
	}
	return ""
}

func (x *TransactionsReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TransactionsReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type TransactionsReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 总条数
	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	// transaction
	List          []*Transaction `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionsReply) Reset() {
	*x = TransactionsReply{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionsReply) ProtoMessage() {}

func (x *TransactionsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionsReply.ProtoReflect.Descriptor instead.
func (*TransactionsReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{13}
}

func (x *TransactionsReply) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *TransactionsReply) GetList() []*Transaction {
	if x != nil {
		return x.List
	}
	return nil
}

type TransactionsByAddressReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 查询from地址，多个用逗号(,)隔开
	FromAddress string `protobuf:"bytes,2,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 查询to地址，多个用逗号(,)隔开
	ToAddress string `protobuf:"bytes,3,opt,name=to_address,json=toAddress,proto3" json:"to_address,omitempty"`
	// 查询合约地址，多个用逗号(,)隔开
	ProgramId *string `protobuf:"bytes,4,opt,name=program_id,json=programId,proto3,oneof" json:"program_id,omitempty"`
	Page      int64   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	Limit         int64 `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionsByAddressReq) Reset() {
	*x = TransactionsByAddressReq{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionsByAddressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionsByAddressReq) ProtoMessage() {}

func (x *TransactionsByAddressReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionsByAddressReq.ProtoReflect.Descriptor instead.
func (*TransactionsByAddressReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{14}
}

func (x *TransactionsByAddressReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TransactionsByAddressReq) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *TransactionsByAddressReq) GetToAddress() string {
	if x != nil {
		return x.ToAddress
	}
	return ""
}

func (x *TransactionsByAddressReq) GetProgramId() string {
	if x != nil && x.ProgramId != nil {
		return *x.ProgramId
	}
	return ""
}

func (x *TransactionsByAddressReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TransactionsByAddressReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type TransactionReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 交易hash
	Txn           string `protobuf:"bytes,2,opt,name=txn,proto3" json:"txn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransactionReq) Reset() {
	*x = TransactionReq{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionReq) ProtoMessage() {}

func (x *TransactionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionReq.ProtoReflect.Descriptor instead.
func (*TransactionReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{15}
}

func (x *TransactionReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *TransactionReq) GetTxn() string {
	if x != nil {
		return x.Txn
	}
	return ""
}

type Transaction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 交易hash
	Txn string `protobuf:"bytes,1,opt,name=txn,proto3" json:"txn,omitempty"`
	// 来源地址
	FromAddress string `protobuf:"bytes,2,opt,name=from_address,json=fromAddress,proto3" json:"from_address,omitempty"`
	// 接收地址
	ToAddress string `protobuf:"bytes,3,opt,name=to_address,json=toAddress,proto3" json:"to_address,omitempty"`
	// 转账余额
	Value string `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	// 交易手续费
	Fee string `protobuf:"bytes,5,opt,name=fee,proto3" json:"fee,omitempty"`
	// 交易方法
	Method string `protobuf:"bytes,6,opt,name=method,proto3" json:"method,omitempty"`
	// 合约地址
	ProgramId string `protobuf:"bytes,7,opt,name=program_id,json=programId,proto3" json:"program_id,omitempty"`
	// 链上完成时间
	Timestamp int64 `protobuf:"varint,8,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// 上链状态 success、fail
	Status string `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	// 所在高度
	BlockNumber int64 `protobuf:"varint,10,opt,name=block_number,json=blockNumber,proto3" json:"block_number,omitempty"`
	// chain_index
	ChainIndex    int64 `protobuf:"varint,11,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Transaction) Reset() {
	*x = Transaction{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transaction) ProtoMessage() {}

func (x *Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transaction.ProtoReflect.Descriptor instead.
func (*Transaction) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{16}
}

func (x *Transaction) GetTxn() string {
	if x != nil {
		return x.Txn
	}
	return ""
}

func (x *Transaction) GetFromAddress() string {
	if x != nil {
		return x.FromAddress
	}
	return ""
}

func (x *Transaction) GetToAddress() string {
	if x != nil {
		return x.ToAddress
	}
	return ""
}

func (x *Transaction) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Transaction) GetFee() string {
	if x != nil {
		return x.Fee
	}
	return ""
}

func (x *Transaction) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *Transaction) GetProgramId() string {
	if x != nil {
		return x.ProgramId
	}
	return ""
}

func (x *Transaction) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *Transaction) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Transaction) GetBlockNumber() int64 {
	if x != nil {
		return x.BlockNumber
	}
	return 0
}

func (x *Transaction) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type Network struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// chain名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// symbol 标识
	Symbol string `protobuf:"bytes,2,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// chainIndex
	ChainIndex int64 `protobuf:"varint,4,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 每条链的标记
	ChainId string `protobuf:"bytes,5,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	// 代币精度
	Decimals int64 `protobuf:"varint,6,opt,name=decimals,proto3" json:"decimals,omitempty"`
	// 链图标
	BlockchainUrl string `protobuf:"bytes,7,opt,name=blockchain_url,json=blockchainUrl,proto3" json:"blockchain_url,omitempty"`
	// token图标
	TokenUrl string `protobuf:"bytes,8,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	// 区块浏览器 地址
	ExplorerUrl string `protobuf:"bytes,9,opt,name=explorer_url,json=explorerUrl,proto3" json:"explorer_url,omitempty"`
	// 支付gas的 symbol
	GasTokenSymbol string `protobuf:"bytes,10,opt,name=gas_token_symbol,json=gasTokenSymbol,proto3" json:"gas_token_symbol,omitempty"`
	// chain 类型
	ChainType string `protobuf:"bytes,11,opt,name=chain_type,json=chainType,proto3" json:"chain_type,omitempty"`
	// handle
	Handle string `protobuf:"bytes,12,opt,name=handle,proto3" json:"handle,omitempty"`
	// 排序
	SortOrder int64 `protobuf:"varint,13,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// 链名称
	ChainName     string `protobuf:"bytes,14,opt,name=chain_name,json=chainName,proto3" json:"chain_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Network) Reset() {
	*x = Network{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Network) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Network) ProtoMessage() {}

func (x *Network) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Network.ProtoReflect.Descriptor instead.
func (*Network) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{17}
}

func (x *Network) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Network) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Network) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Network) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *Network) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *Network) GetBlockchainUrl() string {
	if x != nil {
		return x.BlockchainUrl
	}
	return ""
}

func (x *Network) GetTokenUrl() string {
	if x != nil {
		return x.TokenUrl
	}
	return ""
}

func (x *Network) GetExplorerUrl() string {
	if x != nil {
		return x.ExplorerUrl
	}
	return ""
}

func (x *Network) GetGasTokenSymbol() string {
	if x != nil {
		return x.GasTokenSymbol
	}
	return ""
}

func (x *Network) GetChainType() string {
	if x != nil {
		return x.ChainType
	}
	return ""
}

func (x *Network) GetHandle() string {
	if x != nil {
		return x.Handle
	}
	return ""
}

func (x *Network) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *Network) GetChainName() string {
	if x != nil {
		return x.ChainName
	}
	return ""
}

type TokenListReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引(0,60,...支持查询多个链,查询全部就传空或不传)
	ChainIndexes string `protobuf:"bytes,1,opt,name=chain_indexes,json=chainIndexes,proto3" json:"chain_indexes,omitempty"`
	// 支持名字或者合约地址
	Query string `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	// 查询页
	Page int64 `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	// 每页条数，限制200条
	Limit         int64 `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenListReq) Reset() {
	*x = TokenListReq{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenListReq) ProtoMessage() {}

func (x *TokenListReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenListReq.ProtoReflect.Descriptor instead.
func (*TokenListReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{18}
}

func (x *TokenListReq) GetChainIndexes() string {
	if x != nil {
		return x.ChainIndexes
	}
	return ""
}

func (x *TokenListReq) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *TokenListReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *TokenListReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type Token struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChainIndex    int64                  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Symbol        string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Decimals      int64                  `protobuf:"varint,4,opt,name=decimals,proto3" json:"decimals,omitempty"`
	LogoUrl       string                 `protobuf:"bytes,5,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	Address       string                 `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	ChainId       string                 `protobuf:"bytes,7,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Token) Reset() {
	*x = Token{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Token) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Token) ProtoMessage() {}

func (x *Token) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Token.ProtoReflect.Descriptor instead.
func (*Token) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{19}
}

func (x *Token) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *Token) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Token) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Token) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *Token) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *Token) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Token) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

type TokenListReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Token               `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count         int64                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenListReply) Reset() {
	*x = TokenListReply{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenListReply) ProtoMessage() {}

func (x *TokenListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenListReply.ProtoReflect.Descriptor instead.
func (*TokenListReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{20}
}

func (x *TokenListReply) GetList() []*Token {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TokenListReply) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ReportInternalTxnReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// chainIndex
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// hash
	Txn           string `protobuf:"bytes,3,opt,name=txn,proto3" json:"txn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportInternalTxnReq) Reset() {
	*x = ReportInternalTxnReq{}
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportInternalTxnReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportInternalTxnReq) ProtoMessage() {}

func (x *ReportInternalTxnReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_wallet_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportInternalTxnReq.ProtoReflect.Descriptor instead.
func (*ReportInternalTxnReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_wallet_proto_rawDescGZIP(), []int{21}
}

func (x *ReportInternalTxnReq) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ReportInternalTxnReq) GetTxn() string {
	if x != nil {
		return x.Txn
	}
	return ""
}

var File_api_wallet_v1_wallet_proto protoreflect.FileDescriptor

const file_api_wallet_v1_wallet_proto_rawDesc = "" +
	"\n" +
	"\x1aapi/wallet/v1/wallet.proto\x12\rapi.wallet.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\"\xf1\x01\n" +
	"\x0fTokenWithWallet\x12%\n" +
	"\x0ewallet_address\x18\x01 \x01(\tR\rwalletAddress\x12\x1f\n" +
	"\vchain_index\x18\x02 \x01(\x03R\n" +
	"chainIndex\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x04 \x01(\tR\x06symbol\x12\x1a\n" +
	"\bdecimals\x18\x05 \x01(\x03R\bdecimals\x12\x19\n" +
	"\blogo_url\x18\x06 \x01(\tR\alogoUrl\x12\x18\n" +
	"\aaddress\x18\a \x01(\tR\aaddress\x12\x19\n" +
	"\bchain_id\x18\b \x01(\tR\achainId\"M\n" +
	"\x17QueryTxLatestTokenReply\x122\n" +
	"\x04list\x18\x01 \x03(\v2\x1e.api.wallet.v1.TokenWithWalletR\x04list\"^\n" +
	"\x13WalletAddress4Chain\x12&\n" +
	"\taddresses\x18\x01 \x03(\tB\b\xbaH\x05\x92\x01\x02\b\x01R\taddresses\x12\x1f\n" +
	"\vchain_index\x18\x02 \x01(\x03R\n" +
	"chainIndex\"]\n" +
	"\x15QueryTxLatestTokenReq\x12D\n" +
	"\x06chains\x18\x01 \x03(\v2\".api.wallet.v1.WalletAddress4ChainB\b\xbaH\x05\x92\x01\x02\b\x01R\x06chains\"a\n" +
	"\rWalletAddress\x12!\n" +
	"\aaddress\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\aaddress\x12-\n" +
	"\rchain_indexes\x18\x02 \x03(\x03B\b\xbaH\x05\x92\x01\x02\b\x01R\fchainIndexes\"[\n" +
	"\x13ListTokenBalanceReq\x12D\n" +
	"\taddresses\x18\x01 \x03(\v2\x1c.api.wallet.v1.WalletAddressB\b\xbaH\x05\x92\x01\x02\b\x01R\taddresses\"\xe3\x01\n" +
	"\fTokenBalance\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x04 \x01(\tR\x06symbol\x12\x1a\n" +
	"\bdecimals\x18\x05 \x01(\x03R\bdecimals\x12\x1f\n" +
	"\vraw_balance\x18\x06 \x01(\tR\n" +
	"rawBalance\x12\x14\n" +
	"\x05price\x18\a \x01(\tR\x05price\x12\x19\n" +
	"\blogo_url\x18\b \x01(\tR\alogoUrl\"b\n" +
	"\rWalletBalance\x12\x18\n" +
	"\aaddress\x18\x01 \x01(\tR\aaddress\x127\n" +
	"\bbalances\x18\x02 \x03(\v2\x1b.api.wallet.v1.TokenBalanceR\bbalances\"I\n" +
	"\x15ListTokenBalanceReply\x120\n" +
	"\x04list\x18\x01 \x03(\v2\x1c.api.wallet.v1.WalletBalanceR\x04list\"H\n" +
	"\vGetTokenReq\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress\"\x10\n" +
	"\x0eNetworkListReq\">\n" +
	"\x10NetworkListReply\x12*\n" +
	"\x04list\x18\x02 \x03(\v2\x16.api.wallet.v1.NetworkR\x04list\"\xa9\x01\n" +
	"\x0fTransactionsReq\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12!\n" +
	"\aaddress\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\aaddress\x12\x1d\n" +
	"\n" +
	"program_id\x18\x03 \x01(\tR\tprogramId\x12\x12\n" +
	"\x04page\x18\x04 \x01(\x03R\x04page\x12\x1f\n" +
	"\x05limit\x18\x05 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\x05limit\"Y\n" +
	"\x11TransactionsReply\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x03R\x05count\x12.\n" +
	"\x04list\x18\x02 \x03(\v2\x1a.api.wallet.v1.TransactionR\x04list\"\xab\x03\n" +
	"\x18TransactionsByAddressReq\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12!\n" +
	"\ffrom_address\x18\x02 \x01(\tR\vfromAddress\x12\x1d\n" +
	"\n" +
	"to_address\x18\x03 \x01(\tR\ttoAddress\x12\"\n" +
	"\n" +
	"program_id\x18\x04 \x01(\tH\x00R\tprogramId\x88\x01\x01\x12\x12\n" +
	"\x04page\x18\x05 \x01(\x03R\x04page\x12\x1f\n" +
	"\x05limit\x18\x06 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\x05limit:\xc3\x01\xbaH\xbf\x01\x1a\xbc\x01\n" +
	"\x1btransactions_by_address_req\x120from_address and to_address cannot both be empty\x1ak!((!has(this.from_address) || this.from_address == '') && (!has(this.to_address) || this.to_address == ''))B\r\n" +
	"\v_program_id\"L\n" +
	"\x0eTransactionReq\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x19\n" +
	"\x03txn\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x03txn\"\xba\x02\n" +
	"\vTransaction\x12\x10\n" +
	"\x03txn\x18\x01 \x01(\tR\x03txn\x12!\n" +
	"\ffrom_address\x18\x02 \x01(\tR\vfromAddress\x12\x1d\n" +
	"\n" +
	"to_address\x18\x03 \x01(\tR\ttoAddress\x12\x14\n" +
	"\x05value\x18\x04 \x01(\tR\x05value\x12\x10\n" +
	"\x03fee\x18\x05 \x01(\tR\x03fee\x12\x16\n" +
	"\x06method\x18\x06 \x01(\tR\x06method\x12\x1d\n" +
	"\n" +
	"program_id\x18\a \x01(\tR\tprogramId\x12\x1c\n" +
	"\ttimestamp\x18\b \x01(\x03R\ttimestamp\x12\x16\n" +
	"\x06status\x18\t \x01(\tR\x06status\x12!\n" +
	"\fblock_number\x18\n" +
	" \x01(\x03R\vblockNumber\x12\x1f\n" +
	"\vchain_index\x18\v \x01(\x03R\n" +
	"chainIndex\"\x93\x03\n" +
	"\aNetwork\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x02 \x01(\tR\x06symbol\x12\x1f\n" +
	"\vchain_index\x18\x04 \x01(\x03R\n" +
	"chainIndex\x12\x19\n" +
	"\bchain_id\x18\x05 \x01(\tR\achainId\x12\x1a\n" +
	"\bdecimals\x18\x06 \x01(\x03R\bdecimals\x12%\n" +
	"\x0eblockchain_url\x18\a \x01(\tR\rblockchainUrl\x12\x1b\n" +
	"\ttoken_url\x18\b \x01(\tR\btokenUrl\x12!\n" +
	"\fexplorer_url\x18\t \x01(\tR\vexplorerUrl\x12(\n" +
	"\x10gas_token_symbol\x18\n" +
	" \x01(\tR\x0egasTokenSymbol\x12\x1d\n" +
	"\n" +
	"chain_type\x18\v \x01(\tR\tchainType\x12\x16\n" +
	"\x06handle\x18\f \x01(\tR\x06handle\x12\x1d\n" +
	"\n" +
	"sort_order\x18\r \x01(\x03R\tsortOrder\x12\x1d\n" +
	"\n" +
	"chain_name\x18\x0e \x01(\tR\tchainName\"\x88\x01\n" +
	"\fTokenListReq\x12#\n" +
	"\rchain_indexes\x18\x01 \x01(\tR\fchainIndexes\x12\x14\n" +
	"\x05query\x18\x02 \x01(\tR\x05query\x12\x1b\n" +
	"\x04page\x18\x03 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12 \n" +
	"\x05limit\x18\x04 \x01(\x03B\n" +
	"\xbaH\a\"\x05\x18\xc8\x01 \x00R\x05limit\"\xc0\x01\n" +
	"\x05Token\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x03 \x01(\tR\x06symbol\x12\x1a\n" +
	"\bdecimals\x18\x04 \x01(\x03R\bdecimals\x12\x19\n" +
	"\blogo_url\x18\x05 \x01(\tR\alogoUrl\x12\x18\n" +
	"\aaddress\x18\x06 \x01(\tR\aaddress\x12\x19\n" +
	"\bchain_id\x18\a \x01(\tR\achainId\"P\n" +
	"\x0eTokenListReply\x12(\n" +
	"\x04list\x18\x01 \x03(\v2\x14.api.wallet.v1.TokenR\x04list\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x03R\x05count\"R\n" +
	"\x14ReportInternalTxnReq\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x19\n" +
	"\x03txn\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x03txn2\x84\b\n" +
	"\tWalletSrv\x12f\n" +
	"\vNetworkList\x12\x1d.api.wallet.v1.NetworkListReq\x1a\x1f.api.wallet.v1.NetworkListReply\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/v1/networkList\x12m\n" +
	"\fTransactions\x12\x1e.api.wallet.v1.TransactionsReq\x1a .api.wallet.v1.TransactionsReply\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/v1/transactionList\x12\x85\x01\n" +
	"\x15TransactionsByAddress\x12'.api.wallet.v1.TransactionsByAddressReq\x1a .api.wallet.v1.TransactionsReply\"!\x82\xd3\xe4\x93\x02\x1b\x12\x19/v1/transactionsByAddress\x12i\n" +
	"\x0fTransactionInfo\x12\x1d.api.wallet.v1.TransactionReq\x1a\x1a.api.wallet.v1.Transaction\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/v1/transactionInfo\x12^\n" +
	"\tTokenList\x12\x1b.api.wallet.v1.TokenListReq\x1a\x1d.api.wallet.v1.TokenListReply\"\x15\x82\xd3\xe4\x93\x02\x0f\x12\r/v1/tokenList\x12R\n" +
	"\bGetToken\x12\x1a.api.wallet.v1.GetTokenReq\x1a\x14.api.wallet.v1.Token\"\x14\x82\xd3\xe4\x93\x02\x0e\x12\f/v1/getToken\x12}\n" +
	"\x10ListTokenBalance\x12\".api.wallet.v1.ListTokenBalanceReq\x1a$.api.wallet.v1.ListTokenBalanceReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/listTokenBalance\x12\x85\x01\n" +
	"\x12QueryTxLatestToken\x12$.api.wallet.v1.QueryTxLatestTokenReq\x1a&.api.wallet.v1.QueryTxLatestTokenReply\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/queryTxLatestToken\x12r\n" +
	"\x11ReportInternalTxn\x12#.api.wallet.v1.ReportInternalTxnReq\x1a\x16.google.protobuf.Empty\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/v1/reportInternalTxnB\x93\x01\n" +
	"\x11com.api.wallet.v1B\vWalletProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_wallet_proto_rawDescOnce sync.Once
	file_api_wallet_v1_wallet_proto_rawDescData []byte
)

func file_api_wallet_v1_wallet_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_wallet_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_wallet_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_wallet_proto_rawDesc), len(file_api_wallet_v1_wallet_proto_rawDesc)))
	})
	return file_api_wallet_v1_wallet_proto_rawDescData
}

var file_api_wallet_v1_wallet_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_api_wallet_v1_wallet_proto_goTypes = []any{
	(*TokenWithWallet)(nil),          // 0: api.wallet.v1.TokenWithWallet
	(*QueryTxLatestTokenReply)(nil),  // 1: api.wallet.v1.QueryTxLatestTokenReply
	(*WalletAddress4Chain)(nil),      // 2: api.wallet.v1.WalletAddress4Chain
	(*QueryTxLatestTokenReq)(nil),    // 3: api.wallet.v1.QueryTxLatestTokenReq
	(*WalletAddress)(nil),            // 4: api.wallet.v1.WalletAddress
	(*ListTokenBalanceReq)(nil),      // 5: api.wallet.v1.ListTokenBalanceReq
	(*TokenBalance)(nil),             // 6: api.wallet.v1.TokenBalance
	(*WalletBalance)(nil),            // 7: api.wallet.v1.WalletBalance
	(*ListTokenBalanceReply)(nil),    // 8: api.wallet.v1.ListTokenBalanceReply
	(*GetTokenReq)(nil),              // 9: api.wallet.v1.GetTokenReq
	(*NetworkListReq)(nil),           // 10: api.wallet.v1.NetworkListReq
	(*NetworkListReply)(nil),         // 11: api.wallet.v1.NetworkListReply
	(*TransactionsReq)(nil),          // 12: api.wallet.v1.TransactionsReq
	(*TransactionsReply)(nil),        // 13: api.wallet.v1.TransactionsReply
	(*TransactionsByAddressReq)(nil), // 14: api.wallet.v1.TransactionsByAddressReq
	(*TransactionReq)(nil),           // 15: api.wallet.v1.TransactionReq
	(*Transaction)(nil),              // 16: api.wallet.v1.Transaction
	(*Network)(nil),                  // 17: api.wallet.v1.Network
	(*TokenListReq)(nil),             // 18: api.wallet.v1.TokenListReq
	(*Token)(nil),                    // 19: api.wallet.v1.Token
	(*TokenListReply)(nil),           // 20: api.wallet.v1.TokenListReply
	(*ReportInternalTxnReq)(nil),     // 21: api.wallet.v1.ReportInternalTxnReq
	(*emptypb.Empty)(nil),            // 22: google.protobuf.Empty
}
var file_api_wallet_v1_wallet_proto_depIdxs = []int32{
	0,  // 0: api.wallet.v1.QueryTxLatestTokenReply.list:type_name -> api.wallet.v1.TokenWithWallet
	2,  // 1: api.wallet.v1.QueryTxLatestTokenReq.chains:type_name -> api.wallet.v1.WalletAddress4Chain
	4,  // 2: api.wallet.v1.ListTokenBalanceReq.addresses:type_name -> api.wallet.v1.WalletAddress
	6,  // 3: api.wallet.v1.WalletBalance.balances:type_name -> api.wallet.v1.TokenBalance
	7,  // 4: api.wallet.v1.ListTokenBalanceReply.list:type_name -> api.wallet.v1.WalletBalance
	17, // 5: api.wallet.v1.NetworkListReply.list:type_name -> api.wallet.v1.Network
	16, // 6: api.wallet.v1.TransactionsReply.list:type_name -> api.wallet.v1.Transaction
	19, // 7: api.wallet.v1.TokenListReply.list:type_name -> api.wallet.v1.Token
	10, // 8: api.wallet.v1.WalletSrv.NetworkList:input_type -> api.wallet.v1.NetworkListReq
	12, // 9: api.wallet.v1.WalletSrv.Transactions:input_type -> api.wallet.v1.TransactionsReq
	14, // 10: api.wallet.v1.WalletSrv.TransactionsByAddress:input_type -> api.wallet.v1.TransactionsByAddressReq
	15, // 11: api.wallet.v1.WalletSrv.TransactionInfo:input_type -> api.wallet.v1.TransactionReq
	18, // 12: api.wallet.v1.WalletSrv.TokenList:input_type -> api.wallet.v1.TokenListReq
	9,  // 13: api.wallet.v1.WalletSrv.GetToken:input_type -> api.wallet.v1.GetTokenReq
	5,  // 14: api.wallet.v1.WalletSrv.ListTokenBalance:input_type -> api.wallet.v1.ListTokenBalanceReq
	3,  // 15: api.wallet.v1.WalletSrv.QueryTxLatestToken:input_type -> api.wallet.v1.QueryTxLatestTokenReq
	21, // 16: api.wallet.v1.WalletSrv.ReportInternalTxn:input_type -> api.wallet.v1.ReportInternalTxnReq
	11, // 17: api.wallet.v1.WalletSrv.NetworkList:output_type -> api.wallet.v1.NetworkListReply
	13, // 18: api.wallet.v1.WalletSrv.Transactions:output_type -> api.wallet.v1.TransactionsReply
	13, // 19: api.wallet.v1.WalletSrv.TransactionsByAddress:output_type -> api.wallet.v1.TransactionsReply
	16, // 20: api.wallet.v1.WalletSrv.TransactionInfo:output_type -> api.wallet.v1.Transaction
	20, // 21: api.wallet.v1.WalletSrv.TokenList:output_type -> api.wallet.v1.TokenListReply
	19, // 22: api.wallet.v1.WalletSrv.GetToken:output_type -> api.wallet.v1.Token
	8,  // 23: api.wallet.v1.WalletSrv.ListTokenBalance:output_type -> api.wallet.v1.ListTokenBalanceReply
	1,  // 24: api.wallet.v1.WalletSrv.QueryTxLatestToken:output_type -> api.wallet.v1.QueryTxLatestTokenReply
	22, // 25: api.wallet.v1.WalletSrv.ReportInternalTxn:output_type -> google.protobuf.Empty
	17, // [17:26] is the sub-list for method output_type
	8,  // [8:17] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_wallet_proto_init() }
func file_api_wallet_v1_wallet_proto_init() {
	if File_api_wallet_v1_wallet_proto != nil {
		return
	}
	file_api_wallet_v1_wallet_proto_msgTypes[14].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_wallet_proto_rawDesc), len(file_api_wallet_v1_wallet_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_wallet_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_wallet_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_wallet_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_wallet_proto = out.File
	file_api_wallet_v1_wallet_proto_goTypes = nil
	file_api_wallet_v1_wallet_proto_depIdxs = nil
}
