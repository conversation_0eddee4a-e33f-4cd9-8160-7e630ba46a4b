// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/response.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// HttpResponse 是所有 HTTP 响应的通用结构
type HttpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      // 业务状态码，0 表示成功，其他表示错误
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"` // 业务消息，成功或错误的描述
	Data          *anypb.Any             `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`       // 实际的业务数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HttpResponse) Reset() {
	*x = HttpResponse{}
	mi := &file_api_wallet_v1_response_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HttpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpResponse) ProtoMessage() {}

func (x *HttpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_response_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpResponse.ProtoReflect.Descriptor instead.
func (*HttpResponse) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_response_proto_rawDescGZIP(), []int{0}
}

func (x *HttpResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HttpResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *HttpResponse) GetData() *anypb.Any {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_api_wallet_v1_response_proto protoreflect.FileDescriptor

const file_api_wallet_v1_response_proto_rawDesc = "" +
	"\n" +
	"\x1capi/wallet/v1/response.proto\x12\rapi.wallet.v1\x1a\x19google/protobuf/any.proto\"f\n" +
	"\fHttpResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12(\n" +
	"\x04data\x18\x03 \x01(\v2\x14.google.protobuf.AnyR\x04dataB\x95\x01\n" +
	"\x11com.api.wallet.v1B\rResponseProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_response_proto_rawDescOnce sync.Once
	file_api_wallet_v1_response_proto_rawDescData []byte
)

func file_api_wallet_v1_response_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_response_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_response_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_response_proto_rawDesc), len(file_api_wallet_v1_response_proto_rawDesc)))
	})
	return file_api_wallet_v1_response_proto_rawDescData
}

var file_api_wallet_v1_response_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_wallet_v1_response_proto_goTypes = []any{
	(*HttpResponse)(nil), // 0: api.wallet.v1.HttpResponse
	(*anypb.Any)(nil),    // 1: google.protobuf.Any
}
var file_api_wallet_v1_response_proto_depIdxs = []int32{
	1, // 0: api.wallet.v1.HttpResponse.data:type_name -> google.protobuf.Any
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_response_proto_init() }
func file_api_wallet_v1_response_proto_init() {
	if File_api_wallet_v1_response_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_response_proto_rawDesc), len(file_api_wallet_v1_response_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_wallet_v1_response_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_response_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_response_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_response_proto = out.File
	file_api_wallet_v1_response_proto_goTypes = nil
	file_api_wallet_v1_response_proto_depIdxs = nil
}
