// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: api/wallet/v1/rent.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationTronSrvAddTronRentRecord = "/api.wallet.v1.TronSrv/AddTronRentRecord"
const OperationTronSrvQueryPreorderInfo = "/api.wallet.v1.TronSrv/QueryPreorderInfo"
const OperationTronSrvUploadHash = "/api.wallet.v1.TronSrv/UploadHash"

type TronSrvHTTPServer interface {
	// AddTronRentRecord AddTronRentRecord 创建能量买单
	AddTronRentRecord(context.Context, *AddTronRentRecordReq) (*AddTronRentRecordReply, error)
	// QueryPreorderInfo QueryPreorderInfo 查询预订单信息，用于预估租赁费用
	QueryPreorderInfo(context.Context, *QueryPreorderInfoReq) (*QueryPreorderInfoReply, error)
	// UploadHash UploadHash 上传买单哈希
	UploadHash(context.Context, *UploadHashReq) (*UploadHashReply, error)
}

func RegisterTronSrvHTTPServer(s *http.Server, srv TronSrvHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/tron/rent/add", _TronSrv_AddTronRentRecord0_HTTP_Handler(srv))
	r.POST("/v1/tron/rent/preorder", _TronSrv_QueryPreorderInfo0_HTTP_Handler(srv))
	r.POST("/v1/tron/rent/upload_hash", _TronSrv_UploadHash0_HTTP_Handler(srv))
}

func _TronSrv_AddTronRentRecord0_HTTP_Handler(srv TronSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddTronRentRecordReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTronSrvAddTronRentRecord)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddTronRentRecord(ctx, req.(*AddTronRentRecordReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddTronRentRecordReply)
		return ctx.Result(200, reply)
	}
}

func _TronSrv_QueryPreorderInfo0_HTTP_Handler(srv TronSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryPreorderInfoReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTronSrvQueryPreorderInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryPreorderInfo(ctx, req.(*QueryPreorderInfoReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryPreorderInfoReply)
		return ctx.Result(200, reply)
	}
}

func _TronSrv_UploadHash0_HTTP_Handler(srv TronSrvHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadHashReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTronSrvUploadHash)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadHash(ctx, req.(*UploadHashReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UploadHashReply)
		return ctx.Result(200, reply)
	}
}

type TronSrvHTTPClient interface {
	AddTronRentRecord(ctx context.Context, req *AddTronRentRecordReq, opts ...http.CallOption) (rsp *AddTronRentRecordReply, err error)
	QueryPreorderInfo(ctx context.Context, req *QueryPreorderInfoReq, opts ...http.CallOption) (rsp *QueryPreorderInfoReply, err error)
	UploadHash(ctx context.Context, req *UploadHashReq, opts ...http.CallOption) (rsp *UploadHashReply, err error)
}

type TronSrvHTTPClientImpl struct {
	cc *http.Client
}

func NewTronSrvHTTPClient(client *http.Client) TronSrvHTTPClient {
	return &TronSrvHTTPClientImpl{client}
}

func (c *TronSrvHTTPClientImpl) AddTronRentRecord(ctx context.Context, in *AddTronRentRecordReq, opts ...http.CallOption) (*AddTronRentRecordReply, error) {
	var out AddTronRentRecordReply
	pattern := "/v1/tron/rent/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTronSrvAddTronRentRecord))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TronSrvHTTPClientImpl) QueryPreorderInfo(ctx context.Context, in *QueryPreorderInfoReq, opts ...http.CallOption) (*QueryPreorderInfoReply, error) {
	var out QueryPreorderInfoReply
	pattern := "/v1/tron/rent/preorder"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTronSrvQueryPreorderInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TronSrvHTTPClientImpl) UploadHash(ctx context.Context, in *UploadHashReq, opts ...http.CallOption) (*UploadHashReply, error) {
	var out UploadHashReply
	pattern := "/v1/tron/rent/upload_hash"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTronSrvUploadHash))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
