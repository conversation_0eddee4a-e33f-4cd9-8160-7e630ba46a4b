// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/wallet/v1/app_version.proto

package v1

import (
	v1 "byd_wallet/api/walletadmin/v1"
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AppVersionService_GetLatestAppVersion_FullMethodName = "/api.wallet.v1.AppVersionService/GetLatestAppVersion"
)

// AppVersionServiceClient is the client API for AppVersionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppVersionServiceClient interface {
	// 获取最新版本
	GetLatestAppVersion(ctx context.Context, in *GetLatestAppVersionReq, opts ...grpc.CallOption) (*v1.AppVersionInfo, error)
}

type appVersionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppVersionServiceClient(cc grpc.ClientConnInterface) AppVersionServiceClient {
	return &appVersionServiceClient{cc}
}

func (c *appVersionServiceClient) GetLatestAppVersion(ctx context.Context, in *GetLatestAppVersionReq, opts ...grpc.CallOption) (*v1.AppVersionInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.AppVersionInfo)
	err := c.cc.Invoke(ctx, AppVersionService_GetLatestAppVersion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppVersionServiceServer is the server API for AppVersionService service.
// All implementations must embed UnimplementedAppVersionServiceServer
// for forward compatibility.
type AppVersionServiceServer interface {
	// 获取最新版本
	GetLatestAppVersion(context.Context, *GetLatestAppVersionReq) (*v1.AppVersionInfo, error)
	mustEmbedUnimplementedAppVersionServiceServer()
}

// UnimplementedAppVersionServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAppVersionServiceServer struct{}

func (UnimplementedAppVersionServiceServer) GetLatestAppVersion(context.Context, *GetLatestAppVersionReq) (*v1.AppVersionInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLatestAppVersion not implemented")
}
func (UnimplementedAppVersionServiceServer) mustEmbedUnimplementedAppVersionServiceServer() {}
func (UnimplementedAppVersionServiceServer) testEmbeddedByValue()                           {}

// UnsafeAppVersionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppVersionServiceServer will
// result in compilation errors.
type UnsafeAppVersionServiceServer interface {
	mustEmbedUnimplementedAppVersionServiceServer()
}

func RegisterAppVersionServiceServer(s grpc.ServiceRegistrar, srv AppVersionServiceServer) {
	// If the following call pancis, it indicates UnimplementedAppVersionServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AppVersionService_ServiceDesc, srv)
}

func _AppVersionService_GetLatestAppVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestAppVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppVersionServiceServer).GetLatestAppVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AppVersionService_GetLatestAppVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppVersionServiceServer).GetLatestAppVersion(ctx, req.(*GetLatestAppVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AppVersionService_ServiceDesc is the grpc.ServiceDesc for AppVersionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppVersionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.wallet.v1.AppVersionService",
	HandlerType: (*AppVersionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLatestAppVersion",
			Handler:    _AppVersionService_GetLatestAppVersion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wallet/v1/app_version.proto",
}
