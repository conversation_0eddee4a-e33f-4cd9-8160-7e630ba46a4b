// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/swap.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListSwapRecordRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 页码 从1开始
	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	// 每页展示条数
	Limit int64 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	// 用户钱包地址
	Addresses     []string `protobuf:"bytes,3,rep,name=addresses,proto3" json:"addresses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSwapRecordRequest) Reset() {
	*x = ListSwapRecordRequest{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSwapRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapRecordRequest) ProtoMessage() {}

func (x *ListSwapRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapRecordRequest.ProtoReflect.Descriptor instead.
func (*ListSwapRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{0}
}

func (x *ListSwapRecordRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListSwapRecordRequest) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListSwapRecordRequest) GetAddresses() []string {
	if x != nil {
		return x.Addresses
	}
	return nil
}

type ListSwapRecordReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	List  []*SwapRecord          `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	// 总条数
	Count         int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSwapRecordReply) Reset() {
	*x = ListSwapRecordReply{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSwapRecordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSwapRecordReply) ProtoMessage() {}

func (x *ListSwapRecordReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSwapRecordReply.ProtoReflect.Descriptor instead.
func (*ListSwapRecordReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{1}
}

func (x *ListSwapRecordReply) GetList() []*SwapRecord {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListSwapRecordReply) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetSwapRecordRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 兑换记录hash
	Hash          string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSwapRecordRequest) Reset() {
	*x = GetSwapRecordRequest{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSwapRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSwapRecordRequest) ProtoMessage() {}

func (x *GetSwapRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSwapRecordRequest.ProtoReflect.Descriptor instead.
func (*GetSwapRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{2}
}

func (x *GetSwapRecordRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

type AddSwapRecordRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	From  *SwapToken             `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To    *SwapToken             `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	Path  string                 `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	Dex   string                 `protobuf:"bytes,4,opt,name=dex,proto3" json:"dex,omitempty"`
	// 滑点百分比，比如传1代表滑点1%
	Slippage string `protobuf:"bytes,5,opt,name=slippage,proto3" json:"slippage,omitempty"`
	// 客户端上传的交易hash
	Hash string `protobuf:"bytes,6,opt,name=hash,proto3" json:"hash,omitempty"`
	// 授权hash
	ApprovalHash string `protobuf:"bytes,7,opt,name=approval_hash,json=approvalHash,proto3" json:"approval_hash,omitempty"`
	// 汇率
	SwapPrice string `protobuf:"bytes,8,opt,name=swap_price,json=swapPrice,proto3" json:"swap_price,omitempty"`
	// 费率
	FeeRate string `protobuf:"bytes,9,opt,name=fee_rate,json=feeRate,proto3" json:"fee_rate,omitempty"`
	// 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
	EstimatedTime string `protobuf:"bytes,10,opt,name=estimated_time,json=estimatedTime,proto3" json:"estimated_time,omitempty"`
	// 矿工费
	GasFee        string `protobuf:"bytes,11,opt,name=gas_fee,json=gasFee,proto3" json:"gas_fee,omitempty"`
	OrderId       string `protobuf:"bytes,12,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddSwapRecordRequest) Reset() {
	*x = AddSwapRecordRequest{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddSwapRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddSwapRecordRequest) ProtoMessage() {}

func (x *AddSwapRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddSwapRecordRequest.ProtoReflect.Descriptor instead.
func (*AddSwapRecordRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{3}
}

func (x *AddSwapRecordRequest) GetFrom() *SwapToken {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *AddSwapRecordRequest) GetTo() *SwapToken {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *AddSwapRecordRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *AddSwapRecordRequest) GetDex() string {
	if x != nil {
		return x.Dex
	}
	return ""
}

func (x *AddSwapRecordRequest) GetSlippage() string {
	if x != nil {
		return x.Slippage
	}
	return ""
}

func (x *AddSwapRecordRequest) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *AddSwapRecordRequest) GetApprovalHash() string {
	if x != nil {
		return x.ApprovalHash
	}
	return ""
}

func (x *AddSwapRecordRequest) GetSwapPrice() string {
	if x != nil {
		return x.SwapPrice
	}
	return ""
}

func (x *AddSwapRecordRequest) GetFeeRate() string {
	if x != nil {
		return x.FeeRate
	}
	return ""
}

func (x *AddSwapRecordRequest) GetEstimatedTime() string {
	if x != nil {
		return x.EstimatedTime
	}
	return ""
}

func (x *AddSwapRecordRequest) GetGasFee() string {
	if x != nil {
		return x.GasFee
	}
	return ""
}

func (x *AddSwapRecordRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type SwapRecord struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 兑换记录id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 兑换时间(unix时间戳)
	SwappedAt int64 `protobuf:"varint,2,opt,name=swapped_at,json=swappedAt,proto3" json:"swapped_at,omitempty"`
	// 兑换状态
	// pending(处理中), success(兑换成功), fail(兑换失败)
	Status string     `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	From   *SwapToken `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty"`
	To     *SwapToken `protobuf:"bytes,5,opt,name=to,proto3" json:"to,omitempty"`
	// 用户兑换消耗的GasFee
	GasFee string `protobuf:"bytes,6,opt,name=gas_fee,json=gasFee,proto3" json:"gas_fee,omitempty"`
	// 兑换手续费率
	FeeRate string `protobuf:"bytes,7,opt,name=fee_rate,json=feeRate,proto3" json:"fee_rate,omitempty"`
	// 用户发起的交易hash
	Hash string `protobuf:"bytes,8,opt,name=hash,proto3" json:"hash,omitempty"`
	// 授权hash
	ApprovalHash string `protobuf:"bytes,9,opt,name=approval_hash,json=approvalHash,proto3" json:"approval_hash,omitempty"`
	// 区块高度
	Height int64 `protobuf:"varint,10,opt,name=height,proto3" json:"height,omitempty"`
	// 兑换平台名称
	Dex string `protobuf:"bytes,11,opt,name=dex,proto3" json:"dex,omitempty"`
	// 兑换平台logo url
	DexLogo string `protobuf:"bytes,12,opt,name=dex_logo,json=dexLogo,proto3" json:"dex_logo,omitempty"`
	// 兑换价格
	SwapPrice string `protobuf:"bytes,13,opt,name=swap_price,json=swapPrice,proto3" json:"swap_price,omitempty"`
	// 兑换详情列表
	Details []*SwapDetail `protobuf:"bytes,14,rep,name=details,proto3" json:"details,omitempty"`
	// 结束时间(unix时间戳)
	FinishedAt int64 `protobuf:"varint,15,opt,name=finished_at,json=finishedAt,proto3" json:"finished_at,omitempty"`
	// 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
	EstimatedTime string `protobuf:"bytes,16,opt,name=estimated_time,json=estimatedTime,proto3" json:"estimated_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapRecord) Reset() {
	*x = SwapRecord{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapRecord) ProtoMessage() {}

func (x *SwapRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapRecord.ProtoReflect.Descriptor instead.
func (*SwapRecord) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{4}
}

func (x *SwapRecord) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SwapRecord) GetSwappedAt() int64 {
	if x != nil {
		return x.SwappedAt
	}
	return 0
}

func (x *SwapRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SwapRecord) GetFrom() *SwapToken {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *SwapRecord) GetTo() *SwapToken {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *SwapRecord) GetGasFee() string {
	if x != nil {
		return x.GasFee
	}
	return ""
}

func (x *SwapRecord) GetFeeRate() string {
	if x != nil {
		return x.FeeRate
	}
	return ""
}

func (x *SwapRecord) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *SwapRecord) GetApprovalHash() string {
	if x != nil {
		return x.ApprovalHash
	}
	return ""
}

func (x *SwapRecord) GetHeight() int64 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *SwapRecord) GetDex() string {
	if x != nil {
		return x.Dex
	}
	return ""
}

func (x *SwapRecord) GetDexLogo() string {
	if x != nil {
		return x.DexLogo
	}
	return ""
}

func (x *SwapRecord) GetSwapPrice() string {
	if x != nil {
		return x.SwapPrice
	}
	return ""
}

func (x *SwapRecord) GetDetails() []*SwapDetail {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *SwapRecord) GetFinishedAt() int64 {
	if x != nil {
		return x.FinishedAt
	}
	return 0
}

func (x *SwapRecord) GetEstimatedTime() string {
	if x != nil {
		return x.EstimatedTime
	}
	return ""
}

type SwapDetail struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 链索引
	ChainIndex int64 `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 区块浏览器url
	ExplorerUrl string `protobuf:"bytes,2,opt,name=explorer_url,json=explorerUrl,proto3" json:"explorer_url,omitempty"`
	// 交易hash
	Hash string `protobuf:"bytes,3,opt,name=hash,proto3" json:"hash,omitempty"`
	// 交易状态
	Status        string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapDetail) Reset() {
	*x = SwapDetail{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapDetail) ProtoMessage() {}

func (x *SwapDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapDetail.ProtoReflect.Descriptor instead.
func (*SwapDetail) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{5}
}

func (x *SwapDetail) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwapDetail) GetExplorerUrl() string {
	if x != nil {
		return x.ExplorerUrl
	}
	return ""
}

func (x *SwapDetail) GetHash() string {
	if x != nil {
		return x.Hash
	}
	return ""
}

func (x *SwapDetail) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ListBlockchainNetworkRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListBlockchainNetworkRequest) Reset() {
	*x = ListBlockchainNetworkRequest{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListBlockchainNetworkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBlockchainNetworkRequest) ProtoMessage() {}

func (x *ListBlockchainNetworkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBlockchainNetworkRequest.ProtoReflect.Descriptor instead.
func (*ListBlockchainNetworkRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{6}
}

type ListBlockchainNetworkReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Network             `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListBlockchainNetworkReply) Reset() {
	*x = ListBlockchainNetworkReply{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListBlockchainNetworkReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBlockchainNetworkReply) ProtoMessage() {}

func (x *ListBlockchainNetworkReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBlockchainNetworkReply.ProtoReflect.Descriptor instead.
func (*ListBlockchainNetworkReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{7}
}

func (x *ListBlockchainNetworkReply) GetList() []*Network {
	if x != nil {
		return x.List
	}
	return nil
}

type SwapRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	From  *SwapToken             `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To    *SwapToken             `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	Path  string                 `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	Dex   string                 `protobuf:"bytes,4,opt,name=dex,proto3" json:"dex,omitempty"`
	// 滑点百分比，比如传1代表滑点1%
	Slippage      string `protobuf:"bytes,5,opt,name=slippage,proto3" json:"slippage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapRequest) Reset() {
	*x = SwapRequest{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapRequest) ProtoMessage() {}

func (x *SwapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapRequest.ProtoReflect.Descriptor instead.
func (*SwapRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{8}
}

func (x *SwapRequest) GetFrom() *SwapToken {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *SwapRequest) GetTo() *SwapToken {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *SwapRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *SwapRequest) GetDex() string {
	if x != nil {
		return x.Dex
	}
	return ""
}

func (x *SwapRequest) GetSlippage() string {
	if x != nil {
		return x.Slippage
	}
	return ""
}

type SwapReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 调用合约请求地址
	From string `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	// 被调用合约地址
	To string `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	// 调用传入金额
	Value    string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	GasLimit string `protobuf:"bytes,4,opt,name=gas_limit,json=gasLimit,proto3" json:"gas_limit,omitempty"`
	GasPrice string `protobuf:"bytes,5,opt,name=gas_price,json=gasPrice,proto3" json:"gas_price,omitempty"`
	// 合约调用数据
	Data string `protobuf:"bytes,6,opt,name=data,proto3" json:"data,omitempty"`
	// 授权地址
	ApproveAddress string `protobuf:"bytes,7,opt,name=approve_address,json=approveAddress,proto3" json:"approve_address,omitempty"`
	// Deposit coin type,0 (common address, no call data);1 (common address,have call data);2(contract address,have call data )
	ToType string `protobuf:"bytes,8,opt,name=to_type,json=toType,proto3" json:"to_type,omitempty"`
	// Only the SWFT channel,deposit address
	PlatformAddr string `protobuf:"bytes,9,opt,name=platform_addr,json=platformAddr,proto3" json:"platform_addr,omitempty"`
	// 兑换汇率
	SwapPrice string `protobuf:"bytes,10,opt,name=swap_price,json=swapPrice,proto3" json:"swap_price,omitempty"`
	// 第三方原始数据
	RawData       string `protobuf:"bytes,11,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"`
	OrderId       string `protobuf:"bytes,12,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapReply) Reset() {
	*x = SwapReply{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapReply) ProtoMessage() {}

func (x *SwapReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapReply.ProtoReflect.Descriptor instead.
func (*SwapReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{9}
}

func (x *SwapReply) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *SwapReply) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *SwapReply) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *SwapReply) GetGasLimit() string {
	if x != nil {
		return x.GasLimit
	}
	return ""
}

func (x *SwapReply) GetGasPrice() string {
	if x != nil {
		return x.GasPrice
	}
	return ""
}

func (x *SwapReply) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *SwapReply) GetApproveAddress() string {
	if x != nil {
		return x.ApproveAddress
	}
	return ""
}

func (x *SwapReply) GetToType() string {
	if x != nil {
		return x.ToType
	}
	return ""
}

func (x *SwapReply) GetPlatformAddr() string {
	if x != nil {
		return x.PlatformAddr
	}
	return ""
}

func (x *SwapReply) GetSwapPrice() string {
	if x != nil {
		return x.SwapPrice
	}
	return ""
}

func (x *SwapReply) GetRawData() string {
	if x != nil {
		return x.RawData
	}
	return ""
}

func (x *SwapReply) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type SwapToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 代币合约地址
	TokenAddress string `protobuf:"bytes,1,opt,name=token_address,json=tokenAddress,proto3" json:"token_address,omitempty"`
	// 用户地址
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	// 链
	ChainIndex int64 `protobuf:"varint,3,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	// 数量
	Amount string `protobuf:"bytes,4,opt,name=amount,proto3" json:"amount,omitempty"`
	// token logo url
	TokenLogo string `protobuf:"bytes,5,opt,name=token_logo,json=tokenLogo,proto3" json:"token_logo,omitempty"`
	// 标的
	Symbol string `protobuf:"bytes,6,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// 精度
	Decimals      string `protobuf:"bytes,7,opt,name=decimals,proto3" json:"decimals,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwapToken) Reset() {
	*x = SwapToken{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwapToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwapToken) ProtoMessage() {}

func (x *SwapToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwapToken.ProtoReflect.Descriptor instead.
func (*SwapToken) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{10}
}

func (x *SwapToken) GetTokenAddress() string {
	if x != nil {
		return x.TokenAddress
	}
	return ""
}

func (x *SwapToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SwapToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwapToken) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *SwapToken) GetTokenLogo() string {
	if x != nil {
		return x.TokenLogo
	}
	return ""
}

func (x *SwapToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *SwapToken) GetDecimals() string {
	if x != nil {
		return x.Decimals
	}
	return ""
}

type MultiQuoteRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	From  *SwapToken             `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To    *SwapToken             `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	// 滑点百分比，比如传1代表滑点1%
	Slippage      string `protobuf:"bytes,3,opt,name=slippage,proto3" json:"slippage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MultiQuoteRequest) Reset() {
	*x = MultiQuoteRequest{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MultiQuoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiQuoteRequest) ProtoMessage() {}

func (x *MultiQuoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiQuoteRequest.ProtoReflect.Descriptor instead.
func (*MultiQuoteRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{11}
}

func (x *MultiQuoteRequest) GetFrom() *SwapToken {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *MultiQuoteRequest) GetTo() *SwapToken {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *MultiQuoteRequest) GetSlippage() string {
	if x != nil {
		return x.Slippage
	}
	return ""
}

type MultiQuoteReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*QuoteInfo           `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MultiQuoteReply) Reset() {
	*x = MultiQuoteReply{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MultiQuoteReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiQuoteReply) ProtoMessage() {}

func (x *MultiQuoteReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiQuoteReply.ProtoReflect.Descriptor instead.
func (*MultiQuoteReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{12}
}

func (x *MultiQuoteReply) GetList() []*QuoteInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type QuoteInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 预估目标代币接收数量
	ReceiveTokenAmount string `protobuf:"bytes,1,opt,name=receive_token_amount,json=receiveTokenAmount,proto3" json:"receive_token_amount,omitempty"`
	// 最小目标代币接收数量
	MinReceiveTokenAmount string `protobuf:"bytes,2,opt,name=min_receive_token_amount,json=minReceiveTokenAmount,proto3" json:"min_receive_token_amount,omitempty"`
	// 滑点百分比，比如1代表滑点1%
	Slippage string `protobuf:"bytes,3,opt,name=slippage,proto3" json:"slippage,omitempty"`
	// 兑换平台
	Dex string `protobuf:"bytes,4,opt,name=dex,proto3" json:"dex,omitempty"`
	// 兑换平台logo
	DexLogo string `protobuf:"bytes,5,opt,name=dex_logo,json=dexLogo,proto3" json:"dex_logo,omitempty"`
	// 交易费率
	FeeRate string `protobuf:"bytes,6,opt,name=fee_rate,json=feeRate,proto3" json:"fee_rate,omitempty"`
	// 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
	EstimatedTime string `protobuf:"bytes,7,opt,name=estimated_time,json=estimatedTime,proto3" json:"estimated_time,omitempty"`
	// 用于传给Swap接口
	Path string `protobuf:"bytes,8,opt,name=path,proto3" json:"path,omitempty"`
	// 最大兑换数量
	MaxFromTokenAmount string `protobuf:"bytes,9,opt,name=max_from_token_amount,json=maxFromTokenAmount,proto3" json:"max_from_token_amount,omitempty"`
	// 最小兑换数量
	MinFromTokenAmount string `protobuf:"bytes,10,opt,name=min_from_token_amount,json=minFromTokenAmount,proto3" json:"min_from_token_amount,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *QuoteInfo) Reset() {
	*x = QuoteInfo{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuoteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuoteInfo) ProtoMessage() {}

func (x *QuoteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuoteInfo.ProtoReflect.Descriptor instead.
func (*QuoteInfo) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{13}
}

func (x *QuoteInfo) GetReceiveTokenAmount() string {
	if x != nil {
		return x.ReceiveTokenAmount
	}
	return ""
}

func (x *QuoteInfo) GetMinReceiveTokenAmount() string {
	if x != nil {
		return x.MinReceiveTokenAmount
	}
	return ""
}

func (x *QuoteInfo) GetSlippage() string {
	if x != nil {
		return x.Slippage
	}
	return ""
}

func (x *QuoteInfo) GetDex() string {
	if x != nil {
		return x.Dex
	}
	return ""
}

func (x *QuoteInfo) GetDexLogo() string {
	if x != nil {
		return x.DexLogo
	}
	return ""
}

func (x *QuoteInfo) GetFeeRate() string {
	if x != nil {
		return x.FeeRate
	}
	return ""
}

func (x *QuoteInfo) GetEstimatedTime() string {
	if x != nil {
		return x.EstimatedTime
	}
	return ""
}

func (x *QuoteInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *QuoteInfo) GetMaxFromTokenAmount() string {
	if x != nil {
		return x.MaxFromTokenAmount
	}
	return ""
}

func (x *QuoteInfo) GetMinFromTokenAmount() string {
	if x != nil {
		return x.MinFromTokenAmount
	}
	return ""
}

type ListTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 传-1 获取所有
	ChainIndex    int64  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	SearchKey     string `protobuf:"bytes,2,opt,name=search_key,json=searchKey,proto3" json:"search_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTokenRequest) Reset() {
	*x = ListTokenRequest{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenRequest) ProtoMessage() {}

func (x *ListTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenRequest.ProtoReflect.Descriptor instead.
func (*ListTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{14}
}

func (x *ListTokenRequest) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *ListTokenRequest) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

type ListTokenReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*Token               `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTokenReply) Reset() {
	*x = ListTokenReply{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTokenReply) ProtoMessage() {}

func (x *ListTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTokenReply.ProtoReflect.Descriptor instead.
func (*ListTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{15}
}

func (x *ListTokenReply) GetList() []*Token {
	if x != nil {
		return x.List
	}
	return nil
}

type ListHotTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHotTokenRequest) Reset() {
	*x = ListHotTokenRequest{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHotTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHotTokenRequest) ProtoMessage() {}

func (x *ListHotTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHotTokenRequest.ProtoReflect.Descriptor instead.
func (*ListHotTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{16}
}

type ListHotTokenReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*SwappableHotToken   `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHotTokenReply) Reset() {
	*x = ListHotTokenReply{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHotTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHotTokenReply) ProtoMessage() {}

func (x *ListHotTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHotTokenReply.ProtoReflect.Descriptor instead.
func (*ListHotTokenReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{17}
}

func (x *ListHotTokenReply) GetList() []*SwappableHotToken {
	if x != nil {
		return x.List
	}
	return nil
}

type SwappableHotToken struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	ChainIndex int64                  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	Name       string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Symbol     string                 `protobuf:"bytes,3,opt,name=symbol,proto3" json:"symbol,omitempty"`
	Decimals   int64                  `protobuf:"varint,4,opt,name=decimals,proto3" json:"decimals,omitempty"`
	LogoUrl    string                 `protobuf:"bytes,5,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	Address    string                 `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	ChainId    string                 `protobuf:"bytes,7,opt,name=chain_id,json=chainId,proto3" json:"chain_id,omitempty"`
	SortOrder  int64                  `protobuf:"varint,8,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty"`
	// 是否是全部的分组
	IsAll         bool `protobuf:"varint,9,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SwappableHotToken) Reset() {
	*x = SwappableHotToken{}
	mi := &file_api_wallet_v1_swap_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SwappableHotToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwappableHotToken) ProtoMessage() {}

func (x *SwappableHotToken) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_swap_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwappableHotToken.ProtoReflect.Descriptor instead.
func (*SwappableHotToken) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_swap_proto_rawDescGZIP(), []int{18}
}

func (x *SwappableHotToken) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

func (x *SwappableHotToken) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SwappableHotToken) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *SwappableHotToken) GetDecimals() int64 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *SwappableHotToken) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *SwappableHotToken) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SwappableHotToken) GetChainId() string {
	if x != nil {
		return x.ChainId
	}
	return ""
}

func (x *SwappableHotToken) GetSortOrder() int64 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *SwappableHotToken) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

var File_api_wallet_v1_swap_proto protoreflect.FileDescriptor

const file_api_wallet_v1_swap_proto_rawDesc = "" +
	"\n" +
	"\x18api/wallet/v1/swap.proto\x12\rapi.wallet.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1aapi/wallet/v1/wallet.proto\"s\n" +
	"\x15ListSwapRecordRequest\x12\x1b\n" +
	"\x04page\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x04page\x12\x1f\n" +
	"\x05limit\x18\x02 \x01(\x03B\t\xbaH\x06\"\x04\x18d \x00R\x05limit\x12\x1c\n" +
	"\taddresses\x18\x03 \x03(\tR\taddresses\"Z\n" +
	"\x13ListSwapRecordReply\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.api.wallet.v1.SwapRecordR\x04list\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x03R\x05count\"3\n" +
	"\x14GetSwapRecordRequest\x12\x1b\n" +
	"\x04hash\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x04hash\"\xa9\x03\n" +
	"\x14AddSwapRecordRequest\x124\n" +
	"\x04from\x18\x01 \x01(\v2\x18.api.wallet.v1.SwapTokenB\x06\xbaH\x03\xc8\x01\x01R\x04from\x120\n" +
	"\x02to\x18\x02 \x01(\v2\x18.api.wallet.v1.SwapTokenB\x06\xbaH\x03\xc8\x01\x01R\x02to\x12\x12\n" +
	"\x04path\x18\x03 \x01(\tR\x04path\x12\x19\n" +
	"\x03dex\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x03dex\x12\x1a\n" +
	"\bslippage\x18\x05 \x01(\tR\bslippage\x12\x1b\n" +
	"\x04hash\x18\x06 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x04hash\x12#\n" +
	"\rapproval_hash\x18\a \x01(\tR\fapprovalHash\x12\x1d\n" +
	"\n" +
	"swap_price\x18\b \x01(\tR\tswapPrice\x12\"\n" +
	"\bfee_rate\x18\t \x01(\tB\a\xbaH\x04r\x02\x10\x01R\afeeRate\x12%\n" +
	"\x0eestimated_time\x18\n" +
	" \x01(\tR\restimatedTime\x12\x17\n" +
	"\agas_fee\x18\v \x01(\tR\x06gasFee\x12\x19\n" +
	"\border_id\x18\f \x01(\tR\aorderId\"\xf9\x03\n" +
	"\n" +
	"SwapRecord\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x1d\n" +
	"\n" +
	"swapped_at\x18\x02 \x01(\x03R\tswappedAt\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12,\n" +
	"\x04from\x18\x04 \x01(\v2\x18.api.wallet.v1.SwapTokenR\x04from\x12(\n" +
	"\x02to\x18\x05 \x01(\v2\x18.api.wallet.v1.SwapTokenR\x02to\x12\x17\n" +
	"\agas_fee\x18\x06 \x01(\tR\x06gasFee\x12\x19\n" +
	"\bfee_rate\x18\a \x01(\tR\afeeRate\x12\x12\n" +
	"\x04hash\x18\b \x01(\tR\x04hash\x12#\n" +
	"\rapproval_hash\x18\t \x01(\tR\fapprovalHash\x12\x16\n" +
	"\x06height\x18\n" +
	" \x01(\x03R\x06height\x12\x10\n" +
	"\x03dex\x18\v \x01(\tR\x03dex\x12\x19\n" +
	"\bdex_logo\x18\f \x01(\tR\adexLogo\x12\x1d\n" +
	"\n" +
	"swap_price\x18\r \x01(\tR\tswapPrice\x123\n" +
	"\adetails\x18\x0e \x03(\v2\x19.api.wallet.v1.SwapDetailR\adetails\x12\x1f\n" +
	"\vfinished_at\x18\x0f \x01(\x03R\n" +
	"finishedAt\x12%\n" +
	"\x0eestimated_time\x18\x10 \x01(\tR\restimatedTime\"|\n" +
	"\n" +
	"SwapDetail\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12!\n" +
	"\fexplorer_url\x18\x02 \x01(\tR\vexplorerUrl\x12\x12\n" +
	"\x04hash\x18\x03 \x01(\tR\x04hash\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\"\x1e\n" +
	"\x1cListBlockchainNetworkRequest\"H\n" +
	"\x1aListBlockchainNetworkReply\x12*\n" +
	"\x04list\x18\x01 \x03(\v2\x16.api.wallet.v1.NetworkR\x04list\"\xa7\x01\n" +
	"\vSwapRequest\x12,\n" +
	"\x04from\x18\x01 \x01(\v2\x18.api.wallet.v1.SwapTokenR\x04from\x12(\n" +
	"\x02to\x18\x02 \x01(\v2\x18.api.wallet.v1.SwapTokenR\x02to\x12\x12\n" +
	"\x04path\x18\x03 \x01(\tR\x04path\x12\x10\n" +
	"\x03dex\x18\x04 \x01(\tR\x03dex\x12\x1a\n" +
	"\bslippage\x18\x05 \x01(\tR\bslippage\"\xcf\x02\n" +
	"\tSwapReply\x12\x12\n" +
	"\x04from\x18\x01 \x01(\tR\x04from\x12\x0e\n" +
	"\x02to\x18\x02 \x01(\tR\x02to\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\x12\x1b\n" +
	"\tgas_limit\x18\x04 \x01(\tR\bgasLimit\x12\x1b\n" +
	"\tgas_price\x18\x05 \x01(\tR\bgasPrice\x12\x12\n" +
	"\x04data\x18\x06 \x01(\tR\x04data\x12'\n" +
	"\x0fapprove_address\x18\a \x01(\tR\x0eapproveAddress\x12\x17\n" +
	"\ato_type\x18\b \x01(\tR\x06toType\x12#\n" +
	"\rplatform_addr\x18\t \x01(\tR\fplatformAddr\x12\x1d\n" +
	"\n" +
	"swap_price\x18\n" +
	" \x01(\tR\tswapPrice\x12\x19\n" +
	"\braw_data\x18\v \x01(\tR\arawData\x12\x19\n" +
	"\border_id\x18\f \x01(\tR\aorderId\"\xdf\x01\n" +
	"\tSwapToken\x12#\n" +
	"\rtoken_address\x18\x01 \x01(\tR\ftokenAddress\x12!\n" +
	"\aaddress\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\aaddress\x12\x1f\n" +
	"\vchain_index\x18\x03 \x01(\x03R\n" +
	"chainIndex\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\tR\x06amount\x12\x1d\n" +
	"\n" +
	"token_logo\x18\x05 \x01(\tR\ttokenLogo\x12\x16\n" +
	"\x06symbol\x18\x06 \x01(\tR\x06symbol\x12\x1a\n" +
	"\bdecimals\x18\a \x01(\tR\bdecimals\"\x97\x01\n" +
	"\x11MultiQuoteRequest\x124\n" +
	"\x04from\x18\x01 \x01(\v2\x18.api.wallet.v1.SwapTokenB\x06\xbaH\x03\xc8\x01\x01R\x04from\x120\n" +
	"\x02to\x18\x02 \x01(\v2\x18.api.wallet.v1.SwapTokenB\x06\xbaH\x03\xc8\x01\x01R\x02to\x12\x1a\n" +
	"\bslippage\x18\x03 \x01(\tR\bslippage\"?\n" +
	"\x0fMultiQuoteReply\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.api.wallet.v1.QuoteInfoR\x04list\"\xfb\x02\n" +
	"\tQuoteInfo\x120\n" +
	"\x14receive_token_amount\x18\x01 \x01(\tR\x12receiveTokenAmount\x127\n" +
	"\x18min_receive_token_amount\x18\x02 \x01(\tR\x15minReceiveTokenAmount\x12\x1a\n" +
	"\bslippage\x18\x03 \x01(\tR\bslippage\x12\x10\n" +
	"\x03dex\x18\x04 \x01(\tR\x03dex\x12\x19\n" +
	"\bdex_logo\x18\x05 \x01(\tR\adexLogo\x12\x19\n" +
	"\bfee_rate\x18\x06 \x01(\tR\afeeRate\x12%\n" +
	"\x0eestimated_time\x18\a \x01(\tR\restimatedTime\x12\x12\n" +
	"\x04path\x18\b \x01(\tR\x04path\x121\n" +
	"\x15max_from_token_amount\x18\t \x01(\tR\x12maxFromTokenAmount\x121\n" +
	"\x15min_from_token_amount\x18\n" +
	" \x01(\tR\x12minFromTokenAmount\"R\n" +
	"\x10ListTokenRequest\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x1d\n" +
	"\n" +
	"search_key\x18\x02 \x01(\tR\tsearchKey\":\n" +
	"\x0eListTokenReply\x12(\n" +
	"\x04list\x18\x01 \x03(\v2\x14.api.wallet.v1.TokenR\x04list\"\x15\n" +
	"\x13ListHotTokenRequest\"I\n" +
	"\x11ListHotTokenReply\x124\n" +
	"\x04list\x18\x01 \x03(\v2 .api.wallet.v1.SwappableHotTokenR\x04list\"\x82\x02\n" +
	"\x11SwappableHotToken\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06symbol\x18\x03 \x01(\tR\x06symbol\x12\x1a\n" +
	"\bdecimals\x18\x04 \x01(\x03R\bdecimals\x12\x19\n" +
	"\blogo_url\x18\x05 \x01(\tR\alogoUrl\x12\x18\n" +
	"\aaddress\x18\x06 \x01(\tR\aaddress\x12\x19\n" +
	"\bchain_id\x18\a \x01(\tR\achainId\x12\x1d\n" +
	"\n" +
	"sort_order\x18\b \x01(\x03R\tsortOrder\x12\x15\n" +
	"\x06is_all\x18\t \x01(\bR\x05isAll2\x9a\a\n" +
	"\vSwapService\x12\x94\x01\n" +
	"\x15ListBlockchainNetwork\x12+.api.wallet.v1.ListBlockchainNetworkRequest\x1a).api.wallet.v1.ListBlockchainNetworkReply\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/v1/swap/blockchain_network\x12c\n" +
	"\tListToken\x12\x1f.api.wallet.v1.ListTokenRequest\x1a\x1d.api.wallet.v1.ListTokenReply\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/v1/swap/token\x12p\n" +
	"\fListHotToken\x12\".api.wallet.v1.ListHotTokenRequest\x1a .api.wallet.v1.ListHotTokenReply\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/v1/swap/token/hot\x12o\n" +
	"\n" +
	"MultiQuote\x12 .api.wallet.v1.MultiQuoteRequest\x1a\x1e.api.wallet.v1.MultiQuoteReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/swap/multi_quote\x12Q\n" +
	"\x04Swap\x12\x1a.api.wallet.v1.SwapRequest\x1a\x18.api.wallet.v1.SwapReply\"\x13\x82\xd3\xe4\x93\x02\r:\x01*\"\b/v1/swap\x12k\n" +
	"\rAddSwapRecord\x12#.api.wallet.v1.AddSwapRecordRequest\x1a\x19.api.wallet.v1.SwapRecord\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/v1/swap/record\x12o\n" +
	"\rGetSwapRecord\x12#.api.wallet.v1.GetSwapRecordRequest\x1a\x19.api.wallet.v1.SwapRecord\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/v1/swap/record/{hash}\x12{\n" +
	"\x0eListSwapRecord\x12$.api.wallet.v1.ListSwapRecordRequest\x1a\".api.wallet.v1.ListSwapRecordReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/swap/record/listB\x91\x01\n" +
	"\x11com.api.wallet.v1B\tSwapProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_swap_proto_rawDescOnce sync.Once
	file_api_wallet_v1_swap_proto_rawDescData []byte
)

func file_api_wallet_v1_swap_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_swap_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_swap_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_swap_proto_rawDesc), len(file_api_wallet_v1_swap_proto_rawDesc)))
	})
	return file_api_wallet_v1_swap_proto_rawDescData
}

var file_api_wallet_v1_swap_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_api_wallet_v1_swap_proto_goTypes = []any{
	(*ListSwapRecordRequest)(nil),        // 0: api.wallet.v1.ListSwapRecordRequest
	(*ListSwapRecordReply)(nil),          // 1: api.wallet.v1.ListSwapRecordReply
	(*GetSwapRecordRequest)(nil),         // 2: api.wallet.v1.GetSwapRecordRequest
	(*AddSwapRecordRequest)(nil),         // 3: api.wallet.v1.AddSwapRecordRequest
	(*SwapRecord)(nil),                   // 4: api.wallet.v1.SwapRecord
	(*SwapDetail)(nil),                   // 5: api.wallet.v1.SwapDetail
	(*ListBlockchainNetworkRequest)(nil), // 6: api.wallet.v1.ListBlockchainNetworkRequest
	(*ListBlockchainNetworkReply)(nil),   // 7: api.wallet.v1.ListBlockchainNetworkReply
	(*SwapRequest)(nil),                  // 8: api.wallet.v1.SwapRequest
	(*SwapReply)(nil),                    // 9: api.wallet.v1.SwapReply
	(*SwapToken)(nil),                    // 10: api.wallet.v1.SwapToken
	(*MultiQuoteRequest)(nil),            // 11: api.wallet.v1.MultiQuoteRequest
	(*MultiQuoteReply)(nil),              // 12: api.wallet.v1.MultiQuoteReply
	(*QuoteInfo)(nil),                    // 13: api.wallet.v1.QuoteInfo
	(*ListTokenRequest)(nil),             // 14: api.wallet.v1.ListTokenRequest
	(*ListTokenReply)(nil),               // 15: api.wallet.v1.ListTokenReply
	(*ListHotTokenRequest)(nil),          // 16: api.wallet.v1.ListHotTokenRequest
	(*ListHotTokenReply)(nil),            // 17: api.wallet.v1.ListHotTokenReply
	(*SwappableHotToken)(nil),            // 18: api.wallet.v1.SwappableHotToken
	(*Network)(nil),                      // 19: api.wallet.v1.Network
	(*Token)(nil),                        // 20: api.wallet.v1.Token
}
var file_api_wallet_v1_swap_proto_depIdxs = []int32{
	4,  // 0: api.wallet.v1.ListSwapRecordReply.list:type_name -> api.wallet.v1.SwapRecord
	10, // 1: api.wallet.v1.AddSwapRecordRequest.from:type_name -> api.wallet.v1.SwapToken
	10, // 2: api.wallet.v1.AddSwapRecordRequest.to:type_name -> api.wallet.v1.SwapToken
	10, // 3: api.wallet.v1.SwapRecord.from:type_name -> api.wallet.v1.SwapToken
	10, // 4: api.wallet.v1.SwapRecord.to:type_name -> api.wallet.v1.SwapToken
	5,  // 5: api.wallet.v1.SwapRecord.details:type_name -> api.wallet.v1.SwapDetail
	19, // 6: api.wallet.v1.ListBlockchainNetworkReply.list:type_name -> api.wallet.v1.Network
	10, // 7: api.wallet.v1.SwapRequest.from:type_name -> api.wallet.v1.SwapToken
	10, // 8: api.wallet.v1.SwapRequest.to:type_name -> api.wallet.v1.SwapToken
	10, // 9: api.wallet.v1.MultiQuoteRequest.from:type_name -> api.wallet.v1.SwapToken
	10, // 10: api.wallet.v1.MultiQuoteRequest.to:type_name -> api.wallet.v1.SwapToken
	13, // 11: api.wallet.v1.MultiQuoteReply.list:type_name -> api.wallet.v1.QuoteInfo
	20, // 12: api.wallet.v1.ListTokenReply.list:type_name -> api.wallet.v1.Token
	18, // 13: api.wallet.v1.ListHotTokenReply.list:type_name -> api.wallet.v1.SwappableHotToken
	6,  // 14: api.wallet.v1.SwapService.ListBlockchainNetwork:input_type -> api.wallet.v1.ListBlockchainNetworkRequest
	14, // 15: api.wallet.v1.SwapService.ListToken:input_type -> api.wallet.v1.ListTokenRequest
	16, // 16: api.wallet.v1.SwapService.ListHotToken:input_type -> api.wallet.v1.ListHotTokenRequest
	11, // 17: api.wallet.v1.SwapService.MultiQuote:input_type -> api.wallet.v1.MultiQuoteRequest
	8,  // 18: api.wallet.v1.SwapService.Swap:input_type -> api.wallet.v1.SwapRequest
	3,  // 19: api.wallet.v1.SwapService.AddSwapRecord:input_type -> api.wallet.v1.AddSwapRecordRequest
	2,  // 20: api.wallet.v1.SwapService.GetSwapRecord:input_type -> api.wallet.v1.GetSwapRecordRequest
	0,  // 21: api.wallet.v1.SwapService.ListSwapRecord:input_type -> api.wallet.v1.ListSwapRecordRequest
	7,  // 22: api.wallet.v1.SwapService.ListBlockchainNetwork:output_type -> api.wallet.v1.ListBlockchainNetworkReply
	15, // 23: api.wallet.v1.SwapService.ListToken:output_type -> api.wallet.v1.ListTokenReply
	17, // 24: api.wallet.v1.SwapService.ListHotToken:output_type -> api.wallet.v1.ListHotTokenReply
	12, // 25: api.wallet.v1.SwapService.MultiQuote:output_type -> api.wallet.v1.MultiQuoteReply
	9,  // 26: api.wallet.v1.SwapService.Swap:output_type -> api.wallet.v1.SwapReply
	4,  // 27: api.wallet.v1.SwapService.AddSwapRecord:output_type -> api.wallet.v1.SwapRecord
	4,  // 28: api.wallet.v1.SwapService.GetSwapRecord:output_type -> api.wallet.v1.SwapRecord
	1,  // 29: api.wallet.v1.SwapService.ListSwapRecord:output_type -> api.wallet.v1.ListSwapRecordReply
	22, // [22:30] is the sub-list for method output_type
	14, // [14:22] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_swap_proto_init() }
func file_api_wallet_v1_swap_proto_init() {
	if File_api_wallet_v1_swap_proto != nil {
		return
	}
	file_api_wallet_v1_wallet_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_swap_proto_rawDesc), len(file_api_wallet_v1_swap_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_swap_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_swap_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_swap_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_swap_proto = out.File
	file_api_wallet_v1_swap_proto_goTypes = nil
	file_api_wallet_v1_swap_proto_depIdxs = nil
}
