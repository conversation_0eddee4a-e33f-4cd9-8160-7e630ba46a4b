// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/wallet/v1/user.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BaseSignPayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 签名数据
	Sign string `protobuf:"bytes,1,opt,name=sign,proto3" json:"sign,omitempty"`
	// 签名消息 如注册walletId:"[{\"chainIndex\":60,\"address\":\"******************************************\"}]" UpDataWalletId:"{\"oldWalletId\":\"888888\",\"newWalletId\":\"99999999\"}"
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// 签名地址
	Address string `protobuf:"bytes,3,opt,name=address,proto3" json:"address,omitempty"`
	// chain_index
	ChainIndex    int64 `protobuf:"varint,4,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BaseSignPayload) Reset() {
	*x = BaseSignPayload{}
	mi := &file_api_wallet_v1_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaseSignPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseSignPayload) ProtoMessage() {}

func (x *BaseSignPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseSignPayload.ProtoReflect.Descriptor instead.
func (*BaseSignPayload) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{0}
}

func (x *BaseSignPayload) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *BaseSignPayload) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BaseSignPayload) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *BaseSignPayload) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type UserRegisterReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *BaseSignPayload       `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserRegisterReq) Reset() {
	*x = UserRegisterReq{}
	mi := &file_api_wallet_v1_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserRegisterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRegisterReq) ProtoMessage() {}

func (x *UserRegisterReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRegisterReq.ProtoReflect.Descriptor instead.
func (*UserRegisterReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{1}
}

func (x *UserRegisterReq) GetBase() *BaseSignPayload {
	if x != nil {
		return x.Base
	}
	return nil
}

type UserRegisterReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WalletId      string                 `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserRegisterReply) Reset() {
	*x = UserRegisterReply{}
	mi := &file_api_wallet_v1_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserRegisterReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRegisterReply) ProtoMessage() {}

func (x *UserRegisterReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRegisterReply.ProtoReflect.Descriptor instead.
func (*UserRegisterReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{2}
}

func (x *UserRegisterReply) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

type UpdateWalletIDReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Base          *BaseSignPayload       `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateWalletIDReq) Reset() {
	*x = UpdateWalletIDReq{}
	mi := &file_api_wallet_v1_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateWalletIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWalletIDReq) ProtoMessage() {}

func (x *UpdateWalletIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWalletIDReq.ProtoReflect.Descriptor instead.
func (*UpdateWalletIDReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateWalletIDReq) GetBase() *BaseSignPayload {
	if x != nil {
		return x.Base
	}
	return nil
}

type UpdateWalletIDReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WalletId      string                 `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateWalletIDReply) Reset() {
	*x = UpdateWalletIDReply{}
	mi := &file_api_wallet_v1_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateWalletIDReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWalletIDReply) ProtoMessage() {}

func (x *UpdateWalletIDReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWalletIDReply.ProtoReflect.Descriptor instead.
func (*UpdateWalletIDReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateWalletIDReply) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

type GetWalletIdReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户地址，多个地址以逗号隔开，如：0x123...23,0x23...434
	Addresses     string `protobuf:"bytes,1,opt,name=addresses,proto3" json:"addresses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWalletIdReq) Reset() {
	*x = GetWalletIdReq{}
	mi := &file_api_wallet_v1_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWalletIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWalletIdReq) ProtoMessage() {}

func (x *GetWalletIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWalletIdReq.ProtoReflect.Descriptor instead.
func (*GetWalletIdReq) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{5}
}

func (x *GetWalletIdReq) GetAddresses() string {
	if x != nil {
		return x.Addresses
	}
	return ""
}

type GeWalletIdsReply struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// walletId 列表
	List          []*GetWalletId `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GeWalletIdsReply) Reset() {
	*x = GeWalletIdsReply{}
	mi := &file_api_wallet_v1_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GeWalletIdsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeWalletIdsReply) ProtoMessage() {}

func (x *GeWalletIdsReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeWalletIdsReply.ProtoReflect.Descriptor instead.
func (*GeWalletIdsReply) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{6}
}

func (x *GeWalletIdsReply) GetList() []*GetWalletId {
	if x != nil {
		return x.List
	}
	return nil
}

type GetWalletId struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WalletId      string                 `protobuf:"bytes,1,opt,name=wallet_id,json=walletId,proto3" json:"wallet_id,omitempty"`
	Address       string                 `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWalletId) Reset() {
	*x = GetWalletId{}
	mi := &file_api_wallet_v1_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWalletId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWalletId) ProtoMessage() {}

func (x *GetWalletId) ProtoReflect() protoreflect.Message {
	mi := &file_api_wallet_v1_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWalletId.ProtoReflect.Descriptor instead.
func (*GetWalletId) Descriptor() ([]byte, []int) {
	return file_api_wallet_v1_user_proto_rawDescGZIP(), []int{7}
}

func (x *GetWalletId) GetWalletId() string {
	if x != nil {
		return x.WalletId
	}
	return ""
}

func (x *GetWalletId) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

var File_api_wallet_v1_user_proto protoreflect.FileDescriptor

const file_api_wallet_v1_user_proto_rawDesc = "" +
	"\n" +
	"\x18api/wallet/v1/user.proto\x12\rapi.wallet.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bbuf/validate/validate.proto\"\x95\x01\n" +
	"\x0fBaseSignPayload\x12\x1b\n" +
	"\x04sign\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x04sign\x12!\n" +
	"\amessage\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\amessage\x12!\n" +
	"\aaddress\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\aaddress\x12\x1f\n" +
	"\vchain_index\x18\x04 \x01(\x03R\n" +
	"chainIndex\"M\n" +
	"\x0fUserRegisterReq\x12:\n" +
	"\x04base\x18\x01 \x01(\v2\x1e.api.wallet.v1.BaseSignPayloadB\x06\xbaH\x03\xc8\x01\x01R\x04base\"8\n" +
	"\x11UserRegisterReply\x12#\n" +
	"\twallet_id\x18\x01 \x01(\tB\x06\xbaH\x03\xc8\x01\x01R\bwalletId\"G\n" +
	"\x11UpdateWalletIDReq\x122\n" +
	"\x04base\x18\x01 \x01(\v2\x1e.api.wallet.v1.BaseSignPayloadR\x04base\"2\n" +
	"\x13UpdateWalletIDReply\x12\x1b\n" +
	"\twallet_id\x18\x01 \x01(\tR\bwalletId\".\n" +
	"\x0eGetWalletIdReq\x12\x1c\n" +
	"\taddresses\x18\x01 \x01(\tR\taddresses\"B\n" +
	"\x10GeWalletIdsReply\x12.\n" +
	"\x04list\x18\x01 \x03(\v2\x1a.api.wallet.v1.GetWalletIdR\x04list\"M\n" +
	"\vGetWalletId\x12$\n" +
	"\twallet_id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bwalletId\x12\x18\n" +
	"\aaddress\x18\x02 \x01(\tR\aaddress2\xe8\x02\n" +
	"\aUserSrv\x12u\n" +
	"\fUserRegister\x12\x1e.api.wallet.v1.UserRegisterReq\x1a .api.wallet.v1.UserRegisterReply\"#\x82\xd3\xe4\x93\x02\x1d:\x01*\"\x18/v1/wallet/user_register\x12~\n" +
	"\x0eUpdateWalletID\x12 .api.wallet.v1.UpdateWalletIDReq\x1a\".api.wallet.v1.UpdateWalletIDReply\"&\x82\xd3\xe4\x93\x02 :\x01*\"\x1b/v1/wallet/update_wallet_id\x12f\n" +
	"\fGetWalletIds\x12\x1d.api.wallet.v1.GetWalletIdReq\x1a\x1f.api.wallet.v1.GeWalletIdsReply\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/v1/wallet/idsB\x91\x01\n" +
	"\x11com.api.wallet.v1B\tUserProtoP\x01Z\x1bbyd_wallet/api/wallet/v1;v1\xa2\x02\x03AWX\xaa\x02\rApi.Wallet.V1\xca\x02\rApi\\Wallet\\V1\xe2\x02\x19Api\\Wallet\\V1\\GPBMetadata\xea\x02\x0fApi::Wallet::V1b\x06proto3"

var (
	file_api_wallet_v1_user_proto_rawDescOnce sync.Once
	file_api_wallet_v1_user_proto_rawDescData []byte
)

func file_api_wallet_v1_user_proto_rawDescGZIP() []byte {
	file_api_wallet_v1_user_proto_rawDescOnce.Do(func() {
		file_api_wallet_v1_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_wallet_v1_user_proto_rawDesc), len(file_api_wallet_v1_user_proto_rawDesc)))
	})
	return file_api_wallet_v1_user_proto_rawDescData
}

var file_api_wallet_v1_user_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_wallet_v1_user_proto_goTypes = []any{
	(*BaseSignPayload)(nil),     // 0: api.wallet.v1.BaseSignPayload
	(*UserRegisterReq)(nil),     // 1: api.wallet.v1.UserRegisterReq
	(*UserRegisterReply)(nil),   // 2: api.wallet.v1.UserRegisterReply
	(*UpdateWalletIDReq)(nil),   // 3: api.wallet.v1.UpdateWalletIDReq
	(*UpdateWalletIDReply)(nil), // 4: api.wallet.v1.UpdateWalletIDReply
	(*GetWalletIdReq)(nil),      // 5: api.wallet.v1.GetWalletIdReq
	(*GeWalletIdsReply)(nil),    // 6: api.wallet.v1.GeWalletIdsReply
	(*GetWalletId)(nil),         // 7: api.wallet.v1.GetWalletId
}
var file_api_wallet_v1_user_proto_depIdxs = []int32{
	0, // 0: api.wallet.v1.UserRegisterReq.base:type_name -> api.wallet.v1.BaseSignPayload
	0, // 1: api.wallet.v1.UpdateWalletIDReq.base:type_name -> api.wallet.v1.BaseSignPayload
	7, // 2: api.wallet.v1.GeWalletIdsReply.list:type_name -> api.wallet.v1.GetWalletId
	1, // 3: api.wallet.v1.UserSrv.UserRegister:input_type -> api.wallet.v1.UserRegisterReq
	3, // 4: api.wallet.v1.UserSrv.UpdateWalletID:input_type -> api.wallet.v1.UpdateWalletIDReq
	5, // 5: api.wallet.v1.UserSrv.GetWalletIds:input_type -> api.wallet.v1.GetWalletIdReq
	2, // 6: api.wallet.v1.UserSrv.UserRegister:output_type -> api.wallet.v1.UserRegisterReply
	4, // 7: api.wallet.v1.UserSrv.UpdateWalletID:output_type -> api.wallet.v1.UpdateWalletIDReply
	6, // 8: api.wallet.v1.UserSrv.GetWalletIds:output_type -> api.wallet.v1.GeWalletIdsReply
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_wallet_v1_user_proto_init() }
func file_api_wallet_v1_user_proto_init() {
	if File_api_wallet_v1_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_wallet_v1_user_proto_rawDesc), len(file_api_wallet_v1_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wallet_v1_user_proto_goTypes,
		DependencyIndexes: file_api_wallet_v1_user_proto_depIdxs,
		MessageInfos:      file_api_wallet_v1_user_proto_msgTypes,
	}.Build()
	File_api_wallet_v1_user_proto = out.File
	file_api_wallet_v1_user_proto_goTypes = nil
	file_api_wallet_v1_user_proto_depIdxs = nil
}
