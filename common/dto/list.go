package dto

func ToList[a, b any](data []a, f func(a) b) []b {
	r := make([]b, 0, len(data))
	for _, v := range data {
		r = append(r, f(v))
	}
	return r
}

func ToListWithMapErr[a, b any](data []a, f func(a) b, mf func(b) (b, error)) ([]b, error) {
	r := make([]b, 0, len(data))
	for _, v := range data {
		vv, err := mf(f(v))
		if err != nil {
			return nil, err
		}
		r = append(r, vv)
	}
	return r, nil
}
