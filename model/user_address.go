package model

import (
	"gorm.io/gorm"
)

type UserAddress struct {
	gorm.Model
	UserID     uint   `gorm:"index;comment:用户ID，关联 wallet 表"`
	ChainIndex int64  `gorm:"index:,unique,composite:chain_index_address;comment:chainIndex"`
	Address    string `gorm:"type:citext;index:,unique,composite:chain_index_address;type:varchar(255);index;comment:用户地址"`
	User       User   `gorm:"foreignKey:UserID"`
}

func (UserAddress) TableName() string {
	return "user_address"
}
