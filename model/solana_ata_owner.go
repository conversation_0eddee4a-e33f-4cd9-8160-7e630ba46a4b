package model

import (
	"time"

	"gorm.io/gorm"
)

// SolanaATAOwner Solana ATA (Associated Token Account) Owner 映射表
// 用于存储 ATA 地址和对应的 owner 地址的映射关系
// 提供数据库层的持久化存储，作为 Redis 缓存的备份数据源
type SolanaATAOwner struct {
	ID        uint      `gorm:"primarykey" json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// ATA 地址（Associated Token Account 地址）
	// 使用复合唯一索引确保每个 ATA 地址只有一条记录
	ATAAddress string `gorm:"type:varchar(44);uniqueIndex:idx_ata_address;not null;comment:ATA地址(Associated Token Account)" json:"ata_address"`

	// Owner 地址（ATA 的所有者地址）
	// 添加普通索引以支持按 owner 查询的场景
	OwnerAddress string `gorm:"type:varchar(44);index:idx_owner_address;not null;comment:ATA所有者地址" json:"owner_address"`

	// 链索引，用于区分不同的 Solana 网络（主网、测试网等）
	// 虽然当前主要用于 Solana 主网，但为了扩展性添加此字段
	ChainIndex int64 `gorm:"index:idx_chain_index;not null;default:501;comment:链索引,Solana主网为501" json:"chain_index"`

	// 数据来源标识，用于追踪数据的来源和质量
	// 可能的值：rpc（从RPC获取）、cache（从缓存同步）、manual（手动添加）
	Source string `gorm:"type:varchar(20);index:idx_source;default:'rpc';comment:数据来源:rpc/cache/manual" json:"source"`

	// 最后验证时间，用于数据一致性检查和过期策略
	// 可以用于定期验证数据的准确性
	LastVerifiedAt *time.Time `gorm:"index:idx_last_verified;comment:最后验证时间" json:"last_verified_at"`

	// 验证状态，用于标识数据的可靠性
	// 可能的值：verified（已验证）、unverified（未验证）、invalid（无效）
	VerificationStatus string `gorm:"type:varchar(20);index:idx_verification_status;default:'verified';comment:验证状态:verified/unverified/invalid" json:"verification_status"`
}

// TableName 指定数据库表名
func (SolanaATAOwner) TableName() string {
	return "solana_ata_owners"
}

// BeforeCreate GORM 钩子：创建前的处理
func (s *SolanaATAOwner) BeforeCreate(tx *gorm.DB) error {
	// 设置默认值
	if s.ChainIndex == 0 {
		s.ChainIndex = 501 // Solana 主网链索引
	}
	if s.Source == "" {
		s.Source = "rpc"
	}
	if s.VerificationStatus == "" {
		s.VerificationStatus = "verified"
	}
	return nil
}

// BeforeUpdate GORM 钩子：更新前的处理
func (s *SolanaATAOwner) BeforeUpdate(tx *gorm.DB) error {
	// 更新时自动设置最后验证时间
	now := time.Now()
	s.LastVerifiedAt = &now
	return nil
}

// IsValid 检查记录是否有效
func (s *SolanaATAOwner) IsValid() bool {
	return s.ATAAddress != "" && s.OwnerAddress != "" && s.VerificationStatus == "verified"
}

// IsExpired 检查记录是否过期（基于最后验证时间）
// 参数 expireDuration 指定过期时间间隔
func (s *SolanaATAOwner) IsExpired(expireDuration time.Duration) bool {
	if s.LastVerifiedAt == nil {
		return false // 如果没有验证时间，认为不过期
	}
	return time.Since(*s.LastVerifiedAt) > expireDuration
}

// MarkAsVerified 标记记录为已验证状态
func (s *SolanaATAOwner) MarkAsVerified() {
	s.VerificationStatus = "verified"
	now := time.Now()
	s.LastVerifiedAt = &now
}

// MarkAsInvalid 标记记录为无效状态
func (s *SolanaATAOwner) MarkAsInvalid() {
	s.VerificationStatus = "invalid"
	now := time.Now()
	s.LastVerifiedAt = &now
}

// SolanaATAOwnerFilter 查询过滤器
type SolanaATAOwnerFilter struct {
	// 基础分页参数
	Page     int `json:"page"`
	PageSize int `json:"page_size"`

	// 查询条件
	ATAAddress         string    `json:"ata_address"`          // ATA 地址精确匹配
	OwnerAddress       string    `json:"owner_address"`        // Owner 地址精确匹配
	ChainIndex         int64     `json:"chain_index"`          // 链索引
	Source             string    `json:"source"`               // 数据来源
	VerificationStatus string    `json:"verification_status"`  // 验证状态
	CreatedAfter       time.Time `json:"created_after"`        // 创建时间范围（开始）
	CreatedBefore      time.Time `json:"created_before"`       // 创建时间范围（结束）
	LastVerifiedAfter  time.Time `json:"last_verified_after"`  // 最后验证时间范围（开始）
	LastVerifiedBefore time.Time `json:"last_verified_before"` // 最后验证时间范围（结束）

	// 排序参数
	OrderBy string `json:"order_by"` // 排序字段，如 "created_at DESC"
}

// SolanaATAOwnerBatchInsert 批量插入的数据结构
type SolanaATAOwnerBatchInsert struct {
	ATAAddress     string     `json:"ata_address"`
	OwnerAddress   string     `json:"owner_address"`
	ChainIndex     int64      `json:"chain_index"`
	Source         string     `json:"source"`
	LastVerifiedAt *time.Time `json:"last_verified_at"`
}

// ToModel 转换为数据库模型
func (b *SolanaATAOwnerBatchInsert) ToModel() *SolanaATAOwner {
	return &SolanaATAOwner{
		ATAAddress:         b.ATAAddress,
		OwnerAddress:       b.OwnerAddress,
		ChainIndex:         b.ChainIndex,
		Source:             b.Source,
		LastVerifiedAt:     b.LastVerifiedAt,
		VerificationStatus: "verified",
	}
}

// SolanaATAOwnerStats 统计信息
type SolanaATAOwnerStats struct {
	TotalRecords       int64      `json:"total_records"`        // 总记录数
	VerifiedRecords    int64      `json:"verified_records"`     // 已验证记录数
	UnverifiedRecords  int64      `json:"unverified_records"`   // 未验证记录数
	InvalidRecords     int64      `json:"invalid_records"`      // 无效记录数
	RecordsFromRPC     int64      `json:"records_from_rpc"`     // 来自RPC的记录数
	RecordsFromCache   int64      `json:"records_from_cache"`   // 来自缓存的记录数
	RecordsFromManual  int64      `json:"records_from_manual"`  // 手动添加的记录数
	OldestRecord       *time.Time `json:"oldest_record"`        // 最早记录时间
	NewestRecord       *time.Time `json:"newest_record"`        // 最新记录时间
	LastVerifiedRecord *time.Time `json:"last_verified_record"` // 最后验证记录时间
}
