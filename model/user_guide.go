package model

import "gorm.io/gorm"

// UserGuide 用户指南
type UserGuide struct {
	BaseModelNoDeleted
	CategoryID uint   `gorm:"index;comment:分类id"`
	Language   string `gorm:"index;comment:语言"`
	Title      string `gorm:"not null;comment:标题"`
	Summary    string `gorm:"comment:简介"`
	Display    bool   `gorm:"comment:是否展示"`

	// 关联关系定义
	Category *UserGuideCategory  `gorm:"foreignKey:CategoryID;references:ID"`
	Contents []*UserGuideContent `gorm:"foreignKey:UserGuideID"`
}

// BeforeSave 执行Save前，删除关联数据
func (d UserGuide) BeforeSave(tx *gorm.DB) error {
	return d.BeforeDelete(tx)
}

func (d UserGuide) BeforeDelete(tx *gorm.DB) error {
	return tx.Delete(&UserGuideContent{}, "user_guide_id=?", d.ID).Error
}

// UserGuideContent 用户指南正文
type UserGuideContent struct {
	BaseModelNoDeleted
	UserGuideID uint   `gorm:"index;comment:用户指南id"`
	Content     string `gorm:"comment:正文文本"`
	PhotoURL    string `gorm:"comment:图片"`
	Display     bool   `gorm:"comment:是否展示"`
}

type UserGuideCategory struct {
	BaseModelNoDeleted
	Language string `gorm:"index;comment:语言"`
	Name     string `gorm:"comment:名称"`
	LogoURL  string `gorm:"comment:图标"`
	Display  bool   `gorm:"comment:是否展示"`
}

// UserGuideCategoryView Admin展示用，不需要migrate
type UserGuideCategoryView struct {
	UserGuideCategory
	UserGuideCount int64 `gorm:"comment:用户指南文章数"`
}
