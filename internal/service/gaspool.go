package service

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/gaspool/base"
	"byd_wallet/model"
	"context"
	"fmt"
	"time"
)

// PaymasterAddressCache paymaster地址缓存项
type PaymasterAddressCache struct {
	Address   string    // 缓存的地址
	Timestamp time.Time // 缓存时间戳
}

type GasPoolService struct {
	v1.UnimplementedGasPoolSrvServer

	uc               *gaspool.Usecase
	userUc           *biz.UserUsecase
	hotAccountReader base.HotAccountReader
	spotMgr          *biz.SpotPriceManager
}

func NewGasPoolService(uc *gaspool.Usecase,
	userUc *biz.UserUsecase,
	hotAccountReader base.HotAccountReader,
	spotMgr *biz.SpotPriceManager) *GasPoolService {
	return &GasPoolService{
		uc:               uc,
		userUc:           userUc,
		hotAccountReader: hotAccountReader,
		spotMgr:          spotMgr,
	}
}

// 获取GasPool币种价格
func (s *GasPoolService) GetGasPoolTokenPrice(ctx context.Context, req *v1.GetGasPoolTokenPriceReq) (*v1.GetGasPoolTokenPriceReply, error) {
	sp, err := s.spotMgr.FindSpotPriceByAddress(ctx, req.ChainIndex, req.Address)
	if err != nil {
		return nil, err
	}
	return &v1.GetGasPoolTokenPriceReply{
		Price:     sp.Price.String(),
		Timestamp: sp.Timestamp,
	}, nil
}

// 发送交易
func (s *GasPoolService) SendTx(ctx context.Context, req *v1.SendTxReq) (*v1.SendTxReply, error) {
	reply, err := s.uc.SendTx(ctx, &gaspool.SendTxReq{
		ChainIndex: req.ChainIndex,
		RawTxHex:   req.RawTx,
		TxType:     model.GasPoolTxType(req.TxType),
	})
	if err != nil {
		return nil, err
	}
	return &v1.SendTxReply{
		TxHash: reply.TxHash,
	}, nil
}

// 可充值币种列表
func (s *GasPoolService) ListGasPoolDepositToken(ctx context.Context, req *v1.ListGasPoolDepositTokenReq) (*v1.ListGasPoolDepositTokenReply, error) {
	enable := true
	list, err := s.uc.ListGasPoolDepositTokenView(ctx, &gaspool.GasPoolDepositTokenViewFilter{
		Enable: &enable,
	})
	if err != nil {
		return nil, err
	}
	mapFunc := func(v *v1.GasPoolDepositToken) (*v1.GasPoolDepositToken, error) {
		sp, err := s.spotMgr.FindSpotPriceByAddress(ctx, v.ChainIndex, v.Address)
		if err != nil {
			return nil, fmt.Errorf("find spot price error: %w: chainIndex=%d, addr=%s",
				err, v.ChainIndex, v.Address)
		}
		v.Price = sp.Price.String()
		v.PriceTime = sp.Timestamp
		return v, nil
	}
	resList, err := dto.ToListWithMapErr(list, ToPbGasPoolDepositToken, mapFunc)
	if err != nil {
		return nil, err
	}
	return &v1.ListGasPoolDepositTokenReply{
		List: resList,
	}, nil
}

// 查询GasPool余额
func (s *GasPoolService) GetGasPoolBalance(ctx context.Context, req *v1.GetGasPoolBalanceReq) (*v1.GetGasPoolBalanceReply, error) {
	user, err := s.userUc.FindUserByUsername(ctx, req.WalletId)
	if err != nil {
		return nil, err
	}
	gp, err := s.uc.FindGasPoolByUserID(ctx, user.ID)
	if err != nil {
		return nil, err
	}
	return &v1.GetGasPoolBalanceReply{
		Balance: gp.Balance.String(),
		Stats: &v1.GasPoolUserStats{ // TODO: implement me
			TotalDepositAmount: "0",
			TotalReduceAmount:  "0",
			TotalCreditAmount:  "0",
			TotalReduceCount:   0,
			ChainCount:         2,
		},
	}, nil
}

// GasPool消费记录列表
func (s *GasPoolService) ListGasPoolConsumeRecord(ctx context.Context, req *v1.ListGasPoolConsumeRecordReq) (*v1.ListGasPoolConsumeRecordReply, error) {
	user, err := s.userUc.FindUserByUsername(ctx, req.WalletId)
	if err != nil {
		return nil, err
	}
	list, totalCount, err := s.uc.ListGasPoolSponsorTxConsumeView(ctx, &gaspool.GasPoolSponsorTxConsumeViewFilter{
		Page:     req.Page,
		PageSize: req.Limit,
		OrderBy:  "id DESC",
		UserID:   user.ID,
	})
	if err != nil {
		return nil, err
	}
	return &v1.ListGasPoolConsumeRecordReply{
		List:       dto.ToList(list, ToPbGasPoolConsumeRecord),
		TotalCount: totalCount,
	}, nil
}

// GasPool财务记录列表
func (s *GasPoolService) ListGasPoolCashFlowRecord(ctx context.Context, req *v1.ListGasPoolCashFlowRecordReq) (*v1.ListGasPoolCashFlowRecordReply, error) {
	user, err := s.userUc.FindUserByUsername(ctx, req.WalletId)
	if err != nil {
		return nil, err
	}
	list, totalCount, err := s.uc.ListGasPoolSponsorTxCashFlowView(ctx, &gaspool.GasPoolSponsorTxCashFlowViewFilter{
		Page:     req.Page,
		PageSize: req.Limit,
		OrderBy:  "id DESC",
		UserID:   user.ID,
	})
	if err != nil {
		return nil, err
	}
	tokenList, err := s.uc.ListGasPoolDepositTokenView(ctx, &gaspool.GasPoolDepositTokenViewFilter{Enable: nil}) // all
	if err != nil {
		return nil, err
	}
	return &v1.ListGasPoolCashFlowRecordReply{
		List:       dto.ToList(list, ToPbGasPoolCashFlowRecord),
		TotalCount: totalCount,
		TokenList:  dto.ToList(tokenList, ToPbGasPoolDepositToken),
	}, nil
}

// GetPaymaster 获取paymaster地址
func (s *GasPoolService) GetPaymaster(ctx context.Context, req *v1.GetPaymasterReq) (*v1.GetPaymasterReply, error) {
	address, err := s.hotAccountReader.GetHotAccountAddress(ctx, req.ChainIndex)
	if err != nil {
		return nil, fmt.Errorf("get: %w", err)
	}
	return &v1.GetPaymasterReply{
		Address: address,
	}, nil
}
