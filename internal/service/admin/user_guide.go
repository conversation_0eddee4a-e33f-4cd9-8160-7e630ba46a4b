package admin

import (
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"

	pb "byd_wallet/api/walletadmin/v1"

	"google.golang.org/protobuf/types/known/emptypb"
)

var _ pb.UserGuideServiceServer = (*UserGuideService)(nil)

// UserGuideService 用户指南管理服务
type UserGuideService struct {
	pb.UnimplementedUserGuideServiceServer
	uc *biz.UserGuideAdminUsecase
}

// NewUserGuideService 创建用户指南管理服务
func NewUserGuideService(uc *biz.UserGuideAdminUsecase) *UserGuideService {
	return &UserGuideService{
		uc: uc,
	}
}

// CreateUserGuide 创建用户指南
func (s *UserGuideService) CreateUserGuide(ctx context.Context, req *pb.CreateUserGuideReq) (*pb.CreateUserGuideReply, error) {
	guide := &model.UserGuide{
		CategoryID: uint(req.CategoryId),
		Language:   req.Language,
		Title:      req.Title,
		Summary:    req.Summary,
		Display:    req.Display,
		Contents:   dto.ToList(req.Contents, ToUserGuideContent),
	}

	err := s.uc.CreateUserGuide(ctx, guide)
	if err != nil {
		return nil, err
	}

	return &pb.CreateUserGuideReply{}, nil
}

// UpdateUserGuide 更新用户指南
func (s *UserGuideService) UpdateUserGuide(ctx context.Context, req *pb.UpdateUserGuideReq) (*emptypb.Empty, error) {
	guide := &model.UserGuide{
		CategoryID: uint(req.CategoryId),
		Language:   req.Language,
		Title:      req.Title,
		Summary:    req.Summary,
		Display:    req.Display,
		Contents:   dto.ToList(req.Contents, ToUserGuideContent),
	}
	guide.ID = uint(req.Id)

	err := s.uc.UpdateUserGuide(ctx, guide)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// DeleteUserGuide 删除用户指南
func (s *UserGuideService) DeleteUserGuide(ctx context.Context, req *pb.DeleteUserGuideReq) (*emptypb.Empty, error) {
	err := s.uc.DeleteUserGuide(ctx, uint(req.Id))
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// GetUserGuide 获取用户指南详情
func (s *UserGuideService) GetUserGuide(ctx context.Context, req *pb.GetUserGuideReq) (*pb.UserGuideInfo, error) {
	info, err := s.uc.GetUserGuide(ctx, uint(req.Id))
	if err != nil {
		return nil, err
	}
	return FromUserGuide(info), nil
}

// ListUserGuides 用户指南列表
func (s *UserGuideService) ListUserGuides(ctx context.Context, req *pb.ListUserGuidesReq) (*pb.ListUserGuidesReply, error) {
	list, count, err := s.uc.ListUserGuide(ctx, biz.AdminUserGuideFilter{
		Pagination: FromPagination(req.Page, req.PageSize),
		CategoryID: uint(req.CategoryId),
		Language:   req.Language,
	})
	if err != nil {
		return nil, err
	}
	return &pb.ListUserGuidesReply{
		List:       dto.ToList(list, FromUserGuide),
		TotalCount: count,
	}, nil
}

// CreateUserGuideCategory 创建用户指南分类
func (s *UserGuideService) CreateUserGuideCategory(ctx context.Context, req *pb.CreateUserGuideCategoryReq) (*pb.CreateUserGuideCategoryReply, error) {
	category := &model.UserGuideCategory{
		Language: req.Language,
		Name:     req.Name,
		LogoURL:  req.LogoUrl,
		Display:  req.Display,
	}

	err := s.uc.CreateUserGuideCategory(ctx, category)
	if err != nil {
		return nil, err
	}

	return &pb.CreateUserGuideCategoryReply{}, nil
}

// UpdateUserGuideCategory 更新用户指南分类
func (s *UserGuideService) UpdateUserGuideCategory(ctx context.Context, req *pb.UpdateUserGuideCategoryReq) (*emptypb.Empty, error) {
	category := &model.UserGuideCategory{
		Language: req.Language,
		Name:     req.Name,
		LogoURL:  req.LogoUrl,
		Display:  req.Display,
	}
	category.ID = uint(req.Id)

	err := s.uc.UpdateUserGuideCategory(ctx, category)
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// DeleteUserGuideCategory 删除用户指南分类
func (s *UserGuideService) DeleteUserGuideCategory(ctx context.Context, req *pb.DeleteUserGuideCategoryReq) (*emptypb.Empty, error) {
	err := s.uc.DeleteUserGuideCategory(ctx, uint(req.Id))
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

// GetUserGuideCategory 获取用户指南分类详情
func (s *UserGuideService) GetUserGuideCategory(ctx context.Context, req *pb.GetUserGuideCategoryReq) (*pb.UserGuideCategoryInfo, error) {
	info, err := s.uc.GetUserGuideCategory(ctx, uint(req.Id))
	if err != nil {
		return nil, err
	}
	return FromUserGuideCategory(info), nil
}

// ListUserGuideCategories 用户指南分类列表
func (s *UserGuideService) ListUserGuideCategories(ctx context.Context, req *pb.ListUserGuideCategoriesReq) (*pb.ListUserGuideCategoriesReply, error) {
	list, count, err := s.uc.ListUserGuideCategory(ctx, biz.AdminUserGuideCategoryFilter{
		Pagination: FromPagination(req.Page, req.PageSize),
		Language:   req.Language,
	})
	if err != nil {
		return nil, err
	}
	return &pb.ListUserGuideCategoriesReply{
		List:       dto.ToList(list, FromUserGuideCategory),
		TotalCount: count,
	}, nil
}
