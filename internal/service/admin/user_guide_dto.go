package admin

import (
	v1 "byd_wallet/api/walletadmin/v1"
	"byd_wallet/model"
)

func ToUserGuideContent(from *v1.UserGuideContentInfo) *model.UserGuideContent {
	return &model.UserGuideContent{
		Content:  from.Content,
		PhotoURL: from.PhotoUrl,
		Display:  from.Display,
	}
}

func FromUserGuideContent(from *model.UserGuideContent) *v1.UserGuideContentInfo {
	return &v1.UserGuideContentInfo{
		Content:  from.Content,
		PhotoUrl: from.PhotoURL,
		Display:  from.Display,
	}
}

func FromUserGuide(from *model.UserGuide) *v1.UserGuideInfo {
	info := &v1.UserGuideInfo{
		Id:         uint64(from.ID),
		CategoryId: uint64(from.CategoryID),
		Language:   from.Language,
		Title:      from.Title,
		Summary:    from.Summary,
		Display:    from.Display,
	}

	// 转换内容列表
	if len(from.Contents) > 0 {
		contents := make([]*v1.UserGuideContentInfo, len(from.Contents))
		for i, content := range from.Contents {
			contents[i] = FromUserGuideContent(content)
		}
		info.Contents = contents
	}

	// 转换分类信息
	if from.Category != nil {
		info.Category = from.Category.Name
	}

	return info
}

func FromUserGuideCategory(from *model.UserGuideCategoryView) *v1.UserGuideCategoryInfo {
	return &v1.UserGuideCategoryInfo{
		Id:         uint64(from.ID),
		Language:   from.Language,
		Name:       from.Name,
		LogoUrl:    from.LogoURL,
		Display:    from.Display,
		GuideCount: from.UserGuideCount,
	}
}
