package task

import (
	taskCommon "byd_wallet/common"
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz"
	solana2 "byd_wallet/internal/biz/syncer/chain/solana"
	"byd_wallet/model"
	"context"
	"encoding/json"
	"fmt"
	"github.com/gagliardetto/solana-go"
	"log"
	"math"
	"strconv"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/mr-tron/base58"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type SolanaWebSocketService interface {
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
}

type solanaWebSocketService struct {
	wsURLs      []string
	currentURL  string
	rd          redis.UniversalClient
	db          *gorm.DB
	conn        *websocket.Conn
	mu          sync.Mutex
	txTableName string
}

func NewSolanaWebSocketService(db *gorm.DB, rd redis.UniversalClient, rpc biz.RPCEndpointRepo) SolanaWebSocketService {
	rpcList, err := rpc.GetRpcListByChainIndexForCache(constant.SolChainIndex, "ws")
	if err != nil {
		log.Fatal(err)
	}
	return &solanaWebSocketService{
		wsURLs: rpcList,
		db:     db,
		rd:     rd,

		txTableName: (&model.Transaction{}).TableName(constant.SolChainIndex),
	}
}

type SubscribeRequest struct {
	Jsonrpc string      `json:"jsonrpc"`
	ID      int         `json:"id"`
	Method  string      `json:"method"`
	Params  interface{} `json:"params,omitempty"`
}

type BlockNotification struct {
	Jsonrpc string                  `json:"jsonrpc"`
	Method  string                  `json:"method"`
	Params  BlockNotificationParams `json:"params"`
}

type BlockNotificationParams struct {
	Result       BlockResult `json:"result"`
	Subscription int         `json:"subscription"`
}

type BlockResult struct {
	Context BlockContext `json:"context"`
	Value   BlockValue   `json:"value"`
}

type BlockContext struct {
	Slot int `json:"slot"`
}

type BlockValue struct {
	Slot  int         `json:"slot"`
	Block BlockData   `json:"block"`
	Err   interface{} `json:"err"`
}

type BlockData struct {
	PreviousBlockhash string        `json:"previousBlockhash"`
	Blockhash         string        `json:"blockhash"`
	ParentSlot        int           `json:"parentSlot"`
	Transactions      []Transaction `json:"transactions"`
	BlockTime         int64         `json:"blockTime"`
	BlockHeight       int64         `json:"blockHeight"`
}

type Transaction struct {
	Transaction TransactionData `json:"transaction"`
	Meta        TransactionMeta `json:"meta"`
	Version     FlexibleString  `json:"version"`
}

type FlexibleString string

func (fs *FlexibleString) UnmarshalJSON(data []byte) error {
	// Check if value is a string
	if data[0] == '"' {
		var s string
		if err := json.Unmarshal(data, &s); err != nil {
			return err
		}
		*fs = FlexibleString(s)
		return nil
	}

	// Otherwise, assume it's a number
	var n int
	if err := json.Unmarshal(data, &n); err != nil {
		return err
	}
	*fs = FlexibleString(strconv.Itoa(n))
	return nil
}

type Message struct {
	Header          MessageHeader `json:"header"`
	AccountKeys     []string      `json:"accountKeys"`
	RecentBlockhash string        `json:"recentBlockhash"`
	Instructions    []Instruction `json:"instructions"`
}

type MessageHeader struct {
	NumRequiredSignatures       uint8 `json:"numRequiredSignatures"`
	NumReadonlySignedAccounts   uint8 `json:"numReadonlySignedAccounts"`
	NumReadonlyUnsignedAccounts uint8 `json:"numReadonlyUnsignedAccounts"`
}

type TransactionData struct {
	Signatures []string `json:"signatures"`
	Message    Message  `json:"message"`
}

type TransactionMeta struct {
	Err               interface{}        `json:"err"`
	Status            TransactionStatus  `json:"status"`
	Fee               int64              `json:"fee"`
	PreBalances       []int64            `json:"preBalances"`
	PostBalances      []int64            `json:"postBalances"`
	InnerInstructions []InnerInstruction `json:"innerInstructions"`
	LogMessages       []string           `json:"logMessages"`
	PreTokenBalances  []TokenBalance     `json:"preTokenBalances"`
	PostTokenBalances []TokenBalance     `json:"postTokenBalances"`
	Rewards           []interface{}      `json:"rewards"`
}

type TransactionStatus struct {
	Ok interface{} `json:"Ok"`
}

type InnerInstruction struct {
	Index        int           `json:"index"`
	Instructions []Instruction `json:"instructions"`
}

type Instruction struct {
	ProgramIdIndex int    `json:"programIdIndex"`
	Accounts       []int  `json:"accounts"`
	Data           string `json:"data"`
	//StackHeight    interface{} `json:"stackHeight"`
}

type TokenBalance struct {
	AccountIndex  int           `json:"accountIndex"`
	Mint          string        `json:"mint"`
	UITokenAmount UITokenAmount `json:"uiTokenAmount"`
	Owner         string        `json:"owner"`
	ProgramId     string        `json:"programId"`
}

type UITokenAmount struct {
	UIAmount       interface{} `json:"uiAmount"`
	Decimals       int         `json:"decimals"`
	Amount         string      `json:"amount"`
	UIAmountString string      `json:"uiAmountString"`
}

func (s *solanaWebSocketService) Start(ctx context.Context) error {
	if len(s.wsURLs) == 0 {
		return fmt.Errorf("no WebSocket URLs provided")
	}

	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 10 * time.Second

	// 指数退避配置
	const (
		maxRetries   = 5
		initialDelay = 1 * time.Second
		maxDelay     = 30 * time.Second
	)

	var lastErr error
	retryCount := 0

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			// 尝试所有可用URL
			for _, url := range s.wsURLs {
				conn, resp, err := dialer.DialContext(ctx, url, nil)
				if err != nil {
					lastErr = fmt.Errorf("failed to connect to %s: %v (response: %+v)", url, err, resp)
					log.Printf("Connection attempt failed to %s: %v", url, err)
					continue
				}

				s.conn = conn
				s.currentURL = url
				log.Printf("Successfully connected to %s", url)

				// 设置连接关闭时的重试逻辑
				conn.SetCloseHandler(func(code int, text string) error {
					log.Printf("WebSocket closed (code: %d, reason: %s), attempting reconnect...", code, text)
					go s.retryConnect(ctx, dialer)
					return nil
				})

				if err := s.sendSubscription(); err != nil {
					_ = conn.Close()
					lastErr = fmt.Errorf("failed to send subscription: %v", err)
					continue
				}

				go s.handleMessages(ctx)
				return nil
			}

			// 所有URL尝试失败后的重试逻辑
			retryCount++
			if retryCount > maxRetries {
				return fmt.Errorf("max retries (%d) exceeded. Last error: %v", maxRetries, lastErr)
			}

			delay := initialDelay * time.Duration(math.Pow(2, float64(retryCount-1)))
			if delay > maxDelay {
				delay = maxDelay
			}

			log.Printf("All connection attempts failed, retrying in %v (attempt %d/%d)...", delay, retryCount, maxRetries)
			time.Sleep(delay)
		}
	}
}

// 专用重连方法
func (s *solanaWebSocketService) retryConnect(ctx context.Context, dialer *websocket.Dialer) {
	const maxRetries = 3
	for i := 0; i < maxRetries; i++ {
		select {
		case <-ctx.Done():
			return
		default:
			conn, _, err := dialer.DialContext(ctx, s.currentURL, nil)
			if err != nil {
				log.Printf("Reconnect attempt %d failed: %v", i+1, err)
				time.Sleep(time.Second * time.Duration(i+1))
				continue
			}

			s.mu.Lock()
			s.conn = conn
			s.mu.Unlock()

			conn.SetCloseHandler(func(code int, text string) error {
				log.Printf("WebSocket closed (code: %d, reason: %s), attempting reconnect...", code, text)
				go s.retryConnect(ctx, dialer)
				return nil
			})

			if err := s.sendSubscription(); err != nil {
				_ = conn.Close()
				log.Printf("Resubscription failed: %v", err)
				continue
			}

			log.Printf("Successfully reconnected to %s", s.currentURL)
			go s.handleMessages(ctx)
			return
		}
	}
	log.Printf("Max reconnect attempts (%d) reached", maxRetries)
}
func (s *solanaWebSocketService) sendSubscription() error {
	subscribe := SubscribeRequest{
		Jsonrpc: "2.0",
		ID:      1,
		Method:  "blockSubscribe",
		Params: []interface{}{
			"all",
			map[string]interface{}{
				"commitment":                     "confirmed",
				"encoding":                       "json",
				"transactionDetails":             "full",
				"maxSupportedTransactionVersion": 0,
				"showRewards":                    true,
			},
		},
	}

	if err := s.conn.WriteJSON(subscribe); err != nil {
		return fmt.Errorf("subscription failed: %v", err)
	}
	return nil
}

func (s *solanaWebSocketService) Stop(ctx context.Context) error {
	if s.conn != nil {
		return s.conn.Close()
	}
	return nil
}

func (s *solanaWebSocketService) handleMessages(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			_, message, err := s.conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.Printf("WebSocket closed unexpectedly: %v", err)
				}
				return
			}

			var blockMsg BlockNotification
			if err := json.Unmarshal(message, &blockMsg); err != nil {
				log.Printf("Failed to unmarshal message: %v", err)
				continue
			}

			switch blockMsg.Method {
			case "blockNotification":
				//fmt.Println("message = ", string(message))
				//fmt.Printf("blockMsg = %+v\n", blockMsg)
				s.handleBlockNotification(&blockMsg)
			default:
				log.Printf("Received unhandled message : %s", string(message))
			}
		}
	}
}

func (s *solanaWebSocketService) handleBlockNotification(blockMsg *BlockNotification) {
	block := blockMsg.Params.Result.Value.Block
	slot := uint64(blockMsg.Params.Result.Value.Slot)
	//var solTransactions []*solana.SolTransaction
	var transactions []*model.Transaction
	for _, tx := range block.Transactions {
		if failTx := s.handleFailedTransaction(tx, slot, block.BlockTime); failTx != nil {
			transactions = append(transactions, failTx)
			continue
		}
		// Calculate SOL balance changes
		solChanges := s.calculateSOLChanges(&tx)
		tokenChanges := s.calculateTokenChanges(&tx)
		// Process main instructions
		transactions = append(transactions, s.processInstructions(
			tx.Transaction.Message.Instructions,
			tx.Transaction.Message.AccountKeys,
			solChanges,
			tokenChanges,
			tx.Transaction.Signatures,
			slot,
			tx.Meta.Fee,
			block.BlockTime,
		)...)
		// Process inner instructions
		for _, innerInstruction := range tx.Meta.InnerInstructions {
			transactions = append(transactions, s.processInstructions(
				innerInstruction.Instructions,
				tx.Transaction.Message.AccountKeys,
				solChanges,
				tokenChanges,
				tx.Transaction.Signatures,
				slot,
				tx.Meta.Fee,
				block.BlockTime,
			)...)
		}

	}
	if len(transactions) == 0 {
		return
	}

	s.batchProcessTransactions(context.Background(), transactions)

}

// 批量处理交易
func (s *solanaWebSocketService) batchProcessTransactions(ctx context.Context, transactions []*model.Transaction) {
	batchSize := 200
	for i := 0; i < len(transactions); i += batchSize {
		end := i + batchSize
		if end > len(transactions) {
			end = len(transactions)
		}

		batch := transactions[i:end]
		if err := s.saveBatchToDB(ctx, batch); err != nil {
			log.Printf("Failed to save transactions: %v batchSize %v offset %v\n", err, len(batch), i)
		}
	}
}
func (s *solanaWebSocketService) saveBatchToDB(ctx context.Context, batch []*model.Transaction) error {
	return s.db.WithContext(ctx).Table(s.txTableName).Clauses(clause.OnConflict{DoNothing: true}).CreateInBatches(batch, len(batch)).Error
}

func (s *solanaWebSocketService) handleFailedTransaction(tx Transaction, slot uint64, blockTime int64) *model.Transaction {
	if s.getTransactionStatus(tx.Meta.Err) != constant.TransactionStatusFail {
		return nil
	}
	fromAddress := tx.Transaction.Message.AccountKeys[0]
	if !s.isAnyAddressTracked(context.Background(), fromAddress) {
		return nil
	}

	return &model.Transaction{
		TxHash:      s.getFirstSignature(tx.Transaction.Signatures),
		BlockNumber: int64(slot),
		ChainIndex:  constant.SolChainIndex,
		FromAddress: fromAddress,
		Fee:         decimal.NewFromInt(tx.Meta.Fee).String(),
		Method:      "",
		Status:      constant.TransactionStatusFail,
		Timestamp:   blockTime,
	}
}

// 计算SOL余额变化
func (s *solanaWebSocketService) calculateSOLChanges(tx *Transaction) map[string]int64 {
	solChanges := make(map[string]int64, len(tx.Meta.PostBalances))
	for index, bal := range tx.Meta.PostBalances {
		if index < len(tx.Transaction.Message.AccountKeys) {
			account := tx.Transaction.Message.AccountKeys[index]
			solChanges[account] = bal - tx.Meta.PreBalances[index]
		}
	}
	return solChanges
}

func (s *solanaWebSocketService) calculateTokenChanges(tx *Transaction) map[string]solana2.TokenChange {
	tokenChanges := make(map[string]solana2.TokenChange, len(tx.Meta.PostTokenBalances))
	for index, bal := range tx.Meta.PostTokenBalances {
		if index < len(tx.Transaction.Message.AccountKeys) {
			account := tx.Transaction.Message.AccountKeys[index]
			tokenChanges[account] = solana2.TokenChange{
				Amount:   bal.UITokenAmount.Amount,
				Decimals: uint8(bal.UITokenAmount.Decimals),
				Owner:    bal.Owner,
				Mint:     bal.Mint,
			}
		}
	}
	return tokenChanges
}

// Helper function to process instructions
func (s *solanaWebSocketService) processInstructions(
	instructions []Instruction,
	accountKeys []string,
	solChanges map[string]int64,
	tokenChanges map[string]solana2.TokenChange,
	signatures []string,
	slot uint64,
	fee int64,
	blockTime int64,
) []*model.Transaction {
	var transactions []*model.Transaction
	for _, instruction := range instructions {
		decodedData, err := base58.Decode(instruction.Data)
		if err != nil {
			//log.Printf("Failed to decode signatures= %+v instruction: %+v err: %v \n", signatures, instruction, err)
			continue
		}
		var accounts []uint16
		for _, va := range instruction.Accounts {
			accounts = append(accounts, uint16(va))
		}

		cIns := solana.CompiledInstruction{
			Data:           decodedData,
			Accounts:       accounts,
			ProgramIDIndex: uint16(instruction.ProgramIdIndex),
		}

		solTx := s.handleInstruction(cIns, accountKeys, solChanges, tokenChanges)
		if solTx != nil {
			if solTx.Mint != "" {
				if v, ok := tokenChanges[solTx.FromAddress]; ok {
					solTx.FromAddress = v.Owner
				}
				if v, ok := tokenChanges[solTx.ToAddress]; ok {
					solTx.ToAddress = v.Owner
				}
			}
			if !s.isAnyAddressTracked(context.Background(), solTx.FromAddress, solTx.ToAddress) {
				return nil
			}
			transactions = append(transactions, &model.Transaction{
				TxHash:       s.getFirstSignature(signatures),
				BlockNumber:  int64(slot),
				ChainIndex:   constant.SolChainIndex,
				FromAddress:  solTx.FromAddress,
				ToAddress:    solTx.ToAddress,
				Value:        solTx.Amount.String(),
				Fee:          decimal.NewFromInt(fee).String(),
				Method:       solTx.Method,
				ProgramID:    solTx.Mint,
				Status:       constant.TransactionStatusSuccess,
				Timestamp:    blockTime,
				TokenDecimal: int64(solTx.Decimals),
			})
		}
	}

	return transactions
}

func (s *solanaWebSocketService) isAnyAddressTracked(ctx context.Context, addresses ...interface{}) bool {
	if len(addresses) == 0 {
		return false
	}
	trackedKey := taskCommon.GetTrackedAddress(constant.SolChainIndex)
	exist, err := s.rd.SMIsMember(ctx, trackedKey, addresses...).Result()
	if err != nil {
		return false
	}
	for _, e := range exist {
		if e {
			return true
		}
	}
	return false
}
func (s *solanaWebSocketService) getTransactionStatus(err interface{}) string {
	if err == nil {
		return constant.TransactionStatusSuccess
	}
	return constant.TransactionStatusFail
}

func (s *solanaWebSocketService) getFirstSignature(signatures []string) string {
	for _, signature := range signatures {
		if len(signature) > 32 { // 简单的签名长度检查
			return signature
		}
	}
	return "N/A"
}

func (s *solanaWebSocketService) handleInstruction(
	inst solana.CompiledInstruction,
	accounts []string,
	solChange map[string]int64,
	tokenChanges map[string]solana2.TokenChange,
) *solana2.SolTransaction {
	if int(inst.ProgramIDIndex) >= len(accounts) {
		return nil
	}
	programID := accounts[inst.ProgramIDIndex]
	var accountList []solana.PublicKey
	for _, account := range accounts {
		accountList = append(accountList, solana.MustPublicKeyFromBase58(account))
	}

	var solTransaction *solana2.SolTransaction
	switch programID {
	case solana.TokenProgramID.String():
		solTransaction, _ = (&solana2.TransactionsFetcher{}).ParseTokenInstruction(inst, accountList, solChange, tokenChanges)
		if solTransaction != nil && solTransaction.Method == constant.CloseAccountMethod {
			if value, ok := solChange[solTransaction.FromAddress]; ok {
				solTransaction.Amount = decimal.NewFromInt(value).Abs()
			}
		}

	case solana.SystemProgramID.String():
		solTransaction, _ = (&solana2.TransactionsFetcher{}).ParseSystemInstruction(inst, accountList)
	default:
		return nil
	}
	return solTransaction
}
