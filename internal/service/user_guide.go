package service

import (
	"byd_wallet/common/dto"
	"byd_wallet/internal/biz"
	"context"

	pb "byd_wallet/api/wallet/v1"
)

var _ pb.UserGuideSrvServer = (*UserGuideService)(nil)

// UserGuideService 用户指南服务
type UserGuideService struct {
	pb.UnimplementedUserGuideSrvServer
	uc *biz.UserGuideUsecase
}

// NewUserGuideService 创建用户指南服务
func NewUserGuideService(uc *biz.UserGuideUsecase) *UserGuideService {
	return &UserGuideService{
		uc: uc,
	}
}

// ListUserGuideCategories 获取用户指南分类列表
func (s *UserGuideService) ListUserGuideCategories(ctx context.Context, req *pb.ListUserGuideCategoriesReq) (*pb.ListUserGuideCategoriesReply, error) {
	categories, err := s.uc.ListUserGuideCategory(ctx)
	if err != nil {
		return nil, err
	}

	return &pb.ListUserGuideCategoriesReply{
		List: dto.ToList(categories, FromUserGuideCategory),
	}, nil
}

// ListUserGuides 根据分类查询用户指南列表
func (s *UserGuideService) ListUserGuides(ctx context.Context, req *pb.ListUserGuidesReq) (*pb.ListUserGuidesReply, error) {
	guides, err := s.uc.ListUserGuide(ctx, uint(req.CategoryId))
	if err != nil {
		return nil, err
	}

	return &pb.ListUserGuidesReply{
		List: dto.ToList(guides, FromUserGuide),
	}, nil
}

// ListUserGuideContent 获取用户指南内容列表
func (s *UserGuideService) ListUserGuideContent(ctx context.Context, req *pb.ListUserGuideContentReq) (*pb.ListUserGuideContentReply, error) {
	list, err := s.uc.ListUserGuideContents(ctx, uint(req.Id))
	if err != nil {
		return nil, err
	}

	return &pb.ListUserGuideContentReply{
		List: dto.ToList(list, FromUserGuideContent),
	}, nil
}

// SearchUserGuides 模糊搜索用户指南
func (s *UserGuideService) SearchUserGuides(ctx context.Context, req *pb.SearchUserGuidesReq) (*pb.SearchUserGuidesReply, error) {
	guides, err := s.uc.SearchUserGuide(ctx, req.Keyword)
	if err != nil {
		return nil, err
	}

	return &pb.SearchUserGuidesReply{
		List: dto.ToList(guides, FromUserGuide),
	}, nil
}
