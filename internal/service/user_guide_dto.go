package service

import (
	pb "byd_wallet/api/wallet/v1"
	"byd_wallet/model"
)

// FromUserGuideCategory 转换用户指南分类
func FromUserGuideCategory(from *model.UserGuideCategory) *pb.UserGuideCategoryInfo {
	return &pb.UserGuideCategoryInfo{
		Id:       uint64(from.ID),
		Language: from.Language,
		Name:     from.Name,
		LogoUrl:  from.LogoURL,
	}
}

// FromUserGuide 转换用户指南
func FromUserGuide(from *model.UserGuide) *pb.UserGuideInfo {
	return &pb.UserGuideInfo{
		Id:         uint64(from.ID),
		CategoryId: uint64(from.CategoryID),
		Language:   from.Language,
		Title:      from.Title,
		Summary:    from.Summary,
	}
}

// FromUserGuideContent 转换用户指南内容
func FromUserGuideContent(from *model.UserGuideContent) *pb.UserGuideContentInfo {
	return &pb.UserGuideContentInfo{
		Id:       uint64(from.ID),
		Content:  from.Content,
		PhotoUrl: from.PhotoURL,
	}
}
