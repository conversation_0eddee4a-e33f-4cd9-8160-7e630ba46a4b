package service

import (
	"byd_wallet/internal/service/admin"
	"byd_wallet/internal/service/mq"
	"byd_wallet/internal/service/task"

	"github.com/google/wire"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(
	task.<PERSON>EthHand<PERSON>,
	task.NewSolHandler,
	task.NewTokenProcessor,

	NewUserService,
	NewTokenService,
	NewWalletSrvService,
	NewMarketService,
	NewTronService,
	NewDappService,
	NewSwapService,
	NewGasPoolService,
	NewAppVersionService,
	NewUserGuideService,

	admin.NewAdminService,
	admin.NewAddressService,
	admin.NewUserService,
	admin.NewChainService,
	admin.NewCoinService,
	admin.NewTxService,
	admin.NewDappService,
	admin.NewFileService,
	admin.NewSwapService,
	admin.NewAppVersionService,
	admin.NewUserGuideService,

	task.NewSolanaWebSocketService,
	task.NewSolanaCronSyncService,

	mq.<PERSON><PERSON>oken<PERSON>setService,
	mq.NewUserHoldTokenService,
	mq.NewUserRegisterService,
)
