package server

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/conf"
	"byd_wallet/internal/service/mq"
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/segmentio/kafka-go"
)

type MQServer struct {
	log *log.Helper

	handlers []func(ctx context.Context)
	stopCh   chan struct{}
}

func NewMQServer(c *conf.Server,
	logger log.Logger,
	taSrvi *mq.TokenAssetService,
	uhtSrvi *mq.UserHoldTokenService,
	registerSvc *mq.UserRegisterService,
) (*MQServer, func(), error) {
	if strings.ToLower(c.Mq.MqType) != "kafka" {
		return nil, nil, fmt.Errorf("not supported mq type: %s", c.Mq.MqType)
	}

	// NOTE: If the number of consumed topics is not large,
	//  each topic has its own dedicated Reader instance.
	tKafka := kafka.NewReader(kafka.ReaderConfig{
		Brokers: c.Mq.Addrs,
		Topic:   constant.TokenTopic,
		GroupID: constant.TokenTopic + "-consumer-group",
	})

	hntKafka := kafka.NewReader(kafka.ReaderConfig{
		Brokers: c.Mq.Addrs,
		Topic:   constant.HoldNewTokenTopic,
		GroupID: constant.HoldNewTokenTopic + "-consumer-group",
	})

	registerKafka := kafka.NewReader(kafka.ReaderConfig{
		Brokers: c.Mq.Addrs,
		Topic:   constant.UserRegisterTopic,
		GroupID: constant.UserRegisterTopic + "-consumer-group",
	})

	s := &MQServer{
		log: log.NewHelper(logger),
	}
	s.handlers = []func(ctx context.Context){
		newAsyncHandler(s, tKafka, taSrvi.CreateTokenAssetByEvent),
		newAsyncHandler(s, hntKafka, uhtSrvi.CreateHoldNewTokenAssetByEvent),
		newAsyncHandler(s, registerKafka, registerSvc.OnUserRegisterEvent),
	}

	return s, func() {
		tKafka.Close()
		hntKafka.Close()
	}, nil
}

func (s *MQServer) Start(ctx context.Context) error {
	if s.stopCh == nil {
		s.stopCh = make(chan struct{})
	}
	for _, h := range s.handlers {
		h(ctx)
	}
	return nil
}

func (s *MQServer) Stop(ctx context.Context) error {
	if s.stopCh != nil {
		close(s.stopCh)
	}
	return nil
}

func newAsyncHandler[M any](s *MQServer, kr *kafka.Reader, h func(ctx context.Context, msgObj *M) error) func(ctx context.Context) {
	hName := reflect.TypeOf(*new(M)).Name()
	return func(ctx context.Context) {
		go func() {
			s.log.Infof("start mq async handler: %s", hName)
			for {
				select {
				case <-s.stopCh:
					s.log.Infof("stop mq async handler: %s", hName)
					return
				default:
					msg, err := kr.ReadMessage(ctx)
					if err != nil {
						continue
					}

					s.log.Infof("[%s] receive message: %s", hName, string(msg.Value))

					var msgObj M
					if err := json.Unmarshal(msg.Value, &msgObj); err != nil {
						s.log.Errorf("[%s] parse event json: %v: %s", hName, err, string(msg.Value))
						continue
					}

					err = h(ctx, &msgObj)
					if err != nil {
						s.log.Errorf("[%s] execute handler error: %v: %+v", hName, err, msgObj)
					}

					if err := kr.CommitMessages(ctx, msg); err != nil {
						s.log.Errorf("[%s] kafka commit error: %v", hName, err)
					}
				}
			}
		}()
	}
}
