package data

import (
	"byd_wallet/model"
	"context"
	"time"

	"github.com/gagliardetto/solana-go"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// SolanaATAOwnerRepo Solana ATA Owner 数据库操作接口
// 定义了 ATA owner 映射关系的数据库操作方法
type SolanaATAOwnerRepo interface {
	// 基础 CRUD 操作
	Create(ctx context.Context, record *model.SolanaATAOwner) error
	GetByATAAddress(ctx context.Context, ataAddress string) (*model.SolanaATAOwner, error)
	GetByOwnerAddress(ctx context.Context, ownerAddress string) ([]*model.SolanaATAOwner, error)
	Update(ctx context.Context, record *model.SolanaATAOwner) error
	Delete(ctx context.Context, id uint) error

	// 批量操作
	BatchCreate(ctx context.Context, records []*model.SolanaATAOwner) error
	BatchUpsert(ctx context.Context, records []*model.SolanaATAOwner) error
	BatchGetByATAAddresses(ctx context.Context, ataAddresses []string) ([]*model.SolanaATAOwner, error)

	// 查询操作
	ListByFilter(ctx context.Context, filter *model.SolanaATAOwnerFilter) ([]*model.SolanaATAOwner, int64, error)
	ExistsByATAAddress(ctx context.Context, ataAddress string) (bool, error)
	CountByChainIndex(ctx context.Context, chainIndex int64) (int64, error)

	// 数据维护操作
	GetExpiredRecords(ctx context.Context, expireDuration time.Duration, limit int) ([]*model.SolanaATAOwner, error)
	MarkAsVerified(ctx context.Context, ataAddress string) error
	MarkAsInvalid(ctx context.Context, ataAddress string) error
	DeleteInvalidRecords(ctx context.Context, olderThan time.Time) (int64, error)

	// 统计操作
	GetStats(ctx context.Context, chainIndex int64) (*model.SolanaATAOwnerStats, error)
}

// solanaATAOwnerRepo Solana ATA Owner 数据库操作实现
type solanaATAOwnerRepo struct {
	data *Data
	log  *log.Helper
}

// NewSolanaATAOwnerRepo 创建新的 Solana ATA Owner 数据库操作实例
func NewSolanaATAOwnerRepo(data *Data, logger log.Logger) SolanaATAOwnerRepo {
	return &solanaATAOwnerRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// Create 创建新的 ATA owner 记录
func (r *solanaATAOwnerRepo) Create(ctx context.Context, record *model.SolanaATAOwner) error {
	if err := r.data.DB(ctx).Create(record).Error; err != nil {
		if r.log != nil {
			r.log.Errorf("创建 ATA owner 记录失败: %v", err)
		}
		return err
	}
	if r.log != nil {
		r.log.Debugf("创建 ATA owner 记录成功: %s -> %s", record.ATAAddress, record.OwnerAddress)
	}
	return nil
}

// GetByATAAddress 根据 ATA 地址获取 owner 记录
func (r *solanaATAOwnerRepo) GetByATAAddress(ctx context.Context, ataAddress string) (*model.SolanaATAOwner, error) {
	var record model.SolanaATAOwner
	err := r.data.DB(ctx).Where("ata_address = ?", ataAddress).First(&record).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			if r.log != nil {
				r.log.Debugf("ATA owner 记录未找到: %s", ataAddress)
			}
			return nil, nil
		}
		if r.log != nil {
			r.log.Errorf("查询 ATA owner 记录失败: %s, 错误: %v", ataAddress, err)
		}
		return nil, err
	}
	if r.log != nil {
		r.log.Debugf("查询 ATA owner 记录成功: %s -> %s", record.ATAAddress, record.OwnerAddress)
	}
	return &record, nil
}

// GetByOwnerAddress 根据 owner 地址获取所有相关的 ATA 记录
func (r *solanaATAOwnerRepo) GetByOwnerAddress(ctx context.Context, ownerAddress string) ([]*model.SolanaATAOwner, error) {
	var records []*model.SolanaATAOwner
	err := r.data.DB(ctx).Where("owner_address = ?", ownerAddress).Find(&records).Error
	if err != nil {
		r.log.Errorf("根据 owner 地址查询 ATA 记录失败: %s, 错误: %v", ownerAddress, err)
		return nil, err
	}
	r.log.Debugf("根据 owner 地址查询到 %d 条 ATA 记录: %s", len(records), ownerAddress)
	return records, nil
}

// Update 更新 ATA owner 记录
func (r *solanaATAOwnerRepo) Update(ctx context.Context, record *model.SolanaATAOwner) error {
	if err := r.data.DB(ctx).Save(record).Error; err != nil {
		r.log.Errorf("更新 ATA owner 记录失败: %v", err)
		return err
	}
	r.log.Debugf("更新 ATA owner 记录成功: %s -> %s", record.ATAAddress, record.OwnerAddress)
	return nil
}

// Delete 删除 ATA owner 记录
func (r *solanaATAOwnerRepo) Delete(ctx context.Context, id uint) error {
	if err := r.data.DB(ctx).Delete(&model.SolanaATAOwner{}, id).Error; err != nil {
		r.log.Errorf("删除 ATA owner 记录失败: ID %d, 错误: %v", id, err)
		return err
	}
	r.log.Debugf("删除 ATA owner 记录成功: ID %d", id)
	return nil
}

// BatchCreate 批量创建 ATA owner 记录
func (r *solanaATAOwnerRepo) BatchCreate(ctx context.Context, records []*model.SolanaATAOwner) error {
	if len(records) == 0 {
		return nil
	}

	if err := r.data.DB(ctx).CreateInBatches(records, 100).Error; err != nil {
		r.log.Errorf("批量创建 ATA owner 记录失败: %d 条记录, 错误: %v", len(records), err)
		return err
	}
	r.log.Debugf("批量创建 ATA owner 记录成功: %d 条记录", len(records))
	return nil
}

// BatchUpsert 批量插入或更新 ATA owner 记录（使用 ON CONFLICT 处理重复）
func (r *solanaATAOwnerRepo) BatchUpsert(ctx context.Context, records []*model.SolanaATAOwner) error {
	if len(records) == 0 {
		return nil
	}

	// 使用 GORM 的 Clauses 处理冲突
	err := r.data.DB(ctx).Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "ata_address"}}, // 基于 ata_address 的唯一约束
		DoUpdates: clause.AssignmentColumns([]string{
			"owner_address", "last_verified_at", "verification_status", "updated_at",
		}), // 更新这些字段
	}).CreateInBatches(records, 100).Error

	if err != nil {
		r.log.Errorf("批量插入或更新 ATA owner 记录失败: %d 条记录, 错误: %v", len(records), err)
		return err
	}
	r.log.Debugf("批量插入或更新 ATA owner 记录成功: %d 条记录", len(records))
	return nil
}

// BatchGetByATAAddresses 批量根据 ATA 地址获取 owner 记录
func (r *solanaATAOwnerRepo) BatchGetByATAAddresses(ctx context.Context, ataAddresses []string) ([]*model.SolanaATAOwner, error) {
	if len(ataAddresses) == 0 {
		return []*model.SolanaATAOwner{}, nil
	}

	var records []*model.SolanaATAOwner
	err := r.data.DB(ctx).Where("ata_address IN ?", ataAddresses).Find(&records).Error
	if err != nil {
		r.log.Errorf("批量查询 ATA owner 记录失败: %d 个地址, 错误: %v", len(ataAddresses), err)
		return nil, err
	}
	r.log.Debugf("批量查询 ATA owner 记录成功: 查询 %d 个地址，找到 %d 条记录", len(ataAddresses), len(records))
	return records, nil
}

// ListByFilter 根据过滤条件查询 ATA owner 记录
func (r *solanaATAOwnerRepo) ListByFilter(ctx context.Context, filter *model.SolanaATAOwnerFilter) ([]*model.SolanaATAOwner, int64, error) {
	var records []*model.SolanaATAOwner
	var totalCount int64

	// 构建查询条件
	query := r.data.DB(ctx).Model(&model.SolanaATAOwner{})

	// 应用过滤条件
	if filter.ATAAddress != "" {
		query = query.Where("ata_address = ?", filter.ATAAddress)
	}
	if filter.OwnerAddress != "" {
		query = query.Where("owner_address = ?", filter.OwnerAddress)
	}
	if filter.ChainIndex != 0 {
		query = query.Where("chain_index = ?", filter.ChainIndex)
	}
	if filter.Source != "" {
		query = query.Where("source = ?", filter.Source)
	}
	if filter.VerificationStatus != "" {
		query = query.Where("verification_status = ?", filter.VerificationStatus)
	}
	if !filter.CreatedAfter.IsZero() {
		query = query.Where("created_at >= ?", filter.CreatedAfter)
	}
	if !filter.CreatedBefore.IsZero() {
		query = query.Where("created_at <= ?", filter.CreatedBefore)
	}
	if !filter.LastVerifiedAfter.IsZero() {
		query = query.Where("last_verified_at >= ?", filter.LastVerifiedAfter)
	}
	if !filter.LastVerifiedBefore.IsZero() {
		query = query.Where("last_verified_at <= ?", filter.LastVerifiedBefore)
	}

	// 获取总数
	if err := query.Count(&totalCount).Error; err != nil {
		r.log.Errorf("查询 ATA owner 记录总数失败: %v", err)
		return nil, 0, err
	}

	// 应用排序
	if filter.OrderBy != "" {
		query = query.Order(filter.OrderBy)
	} else {
		query = query.Order("created_at DESC") // 默认按创建时间倒序
	}

	// 应用分页
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// 执行查询
	if err := query.Find(&records).Error; err != nil {
		r.log.Errorf("查询 ATA owner 记录失败: %v", err)
		return nil, 0, err
	}

	r.log.Debugf("查询 ATA owner 记录成功: 找到 %d 条记录，总数 %d", len(records), totalCount)
	return records, totalCount, nil
}

// ExistsByATAAddress 检查指定 ATA 地址的记录是否存在
func (r *solanaATAOwnerRepo) ExistsByATAAddress(ctx context.Context, ataAddress string) (bool, error) {
	var count int64
	err := r.data.DB(ctx).Model(&model.SolanaATAOwner{}).Where("ata_address = ?", ataAddress).Count(&count).Error
	if err != nil {
		r.log.Errorf("检查 ATA 地址是否存在失败: %s, 错误: %v", ataAddress, err)
		return false, err
	}
	exists := count > 0
	r.log.Debugf("检查 ATA 地址是否存在: %s, 结果: %t", ataAddress, exists)
	return exists, nil
}

// CountByChainIndex 统计指定链索引的记录数量
func (r *solanaATAOwnerRepo) CountByChainIndex(ctx context.Context, chainIndex int64) (int64, error) {
	var count int64
	err := r.data.DB(ctx).Model(&model.SolanaATAOwner{}).Where("chain_index = ?", chainIndex).Count(&count).Error
	if err != nil {
		r.log.Errorf("统计链索引记录数量失败: %d, 错误: %v", chainIndex, err)
		return 0, err
	}
	r.log.Debugf("链索引 %d 的记录数量: %d", chainIndex, count)
	return count, nil
}

// GetExpiredRecords 获取过期的记录（基于最后验证时间）
func (r *solanaATAOwnerRepo) GetExpiredRecords(ctx context.Context, expireDuration time.Duration, limit int) ([]*model.SolanaATAOwner, error) {
	var records []*model.SolanaATAOwner
	expireTime := time.Now().Add(-expireDuration)

	query := r.data.DB(ctx).Where("last_verified_at < ? OR last_verified_at IS NULL", expireTime)
	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&records).Error
	if err != nil {
		r.log.Errorf("获取过期记录失败: %v", err)
		return nil, err
	}
	r.log.Debugf("获取到 %d 条过期记录", len(records))
	return records, nil
}

// MarkAsVerified 标记指定 ATA 地址的记录为已验证状态
func (r *solanaATAOwnerRepo) MarkAsVerified(ctx context.Context, ataAddress string) error {
	now := time.Now()
	result := r.data.DB(ctx).Model(&model.SolanaATAOwner{}).
		Where("ata_address = ?", ataAddress).
		Updates(map[string]interface{}{
			"verification_status": "verified",
			"last_verified_at":    now,
			"updated_at":          now,
		})

	if result.Error != nil {
		r.log.Errorf("标记 ATA 记录为已验证失败: %s, 错误: %v", ataAddress, result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.Warnf("标记 ATA 记录为已验证时未找到记录: %s", ataAddress)
	} else {
		r.log.Debugf("标记 ATA 记录为已验证成功: %s", ataAddress)
	}
	return nil
}

// MarkAsInvalid 标记指定 ATA 地址的记录为无效状态
func (r *solanaATAOwnerRepo) MarkAsInvalid(ctx context.Context, ataAddress string) error {
	now := time.Now()
	result := r.data.DB(ctx).Model(&model.SolanaATAOwner{}).
		Where("ata_address = ?", ataAddress).
		Updates(map[string]interface{}{
			"verification_status": "invalid",
			"last_verified_at":    now,
			"updated_at":          now,
		})

	if result.Error != nil {
		r.log.Errorf("标记 ATA 记录为无效失败: %s, 错误: %v", ataAddress, result.Error)
		return result.Error
	}

	if result.RowsAffected == 0 {
		r.log.Warnf("标记 ATA 记录为无效时未找到记录: %s", ataAddress)
	} else {
		r.log.Debugf("标记 ATA 记录为无效成功: %s", ataAddress)
	}
	return nil
}

// DeleteInvalidRecords 删除指定时间之前的无效记录
func (r *solanaATAOwnerRepo) DeleteInvalidRecords(ctx context.Context, olderThan time.Time) (int64, error) {
	result := r.data.DB(ctx).Where("verification_status = ? AND updated_at < ?", "invalid", olderThan).
		Delete(&model.SolanaATAOwner{})

	if result.Error != nil {
		r.log.Errorf("删除无效记录失败: %v", result.Error)
		return 0, result.Error
	}

	r.log.Debugf("删除了 %d 条无效记录", result.RowsAffected)
	return result.RowsAffected, nil
}

// GetStats 获取 ATA owner 记录的统计信息
func (r *solanaATAOwnerRepo) GetStats(ctx context.Context, chainIndex int64) (*model.SolanaATAOwnerStats, error) {
	stats := &model.SolanaATAOwnerStats{}

	// 基础查询条件
	baseQuery := r.data.DB(ctx).Model(&model.SolanaATAOwner{})
	if chainIndex != 0 {
		baseQuery = baseQuery.Where("chain_index = ?", chainIndex)
	}

	// 总记录数
	if err := baseQuery.Count(&stats.TotalRecords).Error; err != nil {
		r.log.Errorf("获取总记录数失败: %v", err)
		return nil, err
	}

	// 按验证状态统计
	var statusCounts []struct {
		VerificationStatus string
		Count              int64
	}
	if err := baseQuery.Select("verification_status, COUNT(*) as count").
		Group("verification_status").Scan(&statusCounts).Error; err != nil {
		r.log.Errorf("获取验证状态统计失败: %v", err)
		return nil, err
	}

	for _, sc := range statusCounts {
		switch sc.VerificationStatus {
		case "verified":
			stats.VerifiedRecords = sc.Count
		case "unverified":
			stats.UnverifiedRecords = sc.Count
		case "invalid":
			stats.InvalidRecords = sc.Count
		}
	}

	// 按数据来源统计
	var sourceCounts []struct {
		Source string
		Count  int64
	}
	if err := baseQuery.Select("source, COUNT(*) as count").
		Group("source").Scan(&sourceCounts).Error; err != nil {
		r.log.Errorf("获取数据来源统计失败: %v", err)
		return nil, err
	}

	for _, sc := range sourceCounts {
		switch sc.Source {
		case "rpc":
			stats.RecordsFromRPC = sc.Count
		case "cache":
			stats.RecordsFromCache = sc.Count
		case "manual":
			stats.RecordsFromManual = sc.Count
		}
	}

	// 时间统计
	var timeStats struct {
		OldestCreated      *time.Time
		NewestCreated      *time.Time
		LastVerifiedRecord *time.Time
	}
	if err := baseQuery.Select(
		"MIN(created_at) as oldest_created, MAX(created_at) as newest_created, MAX(last_verified_at) as last_verified_record",
	).Scan(&timeStats).Error; err != nil {
		r.log.Errorf("获取时间统计失败: %v", err)
		return nil, err
	}

	stats.OldestRecord = timeStats.OldestCreated
	stats.NewestRecord = timeStats.NewestCreated
	stats.LastVerifiedRecord = timeStats.LastVerifiedRecord

	r.log.Debugf("获取统计信息成功: 总记录数 %d, 已验证 %d, 未验证 %d, 无效 %d",
		stats.TotalRecords, stats.VerifiedRecords, stats.UnverifiedRecords, stats.InvalidRecords)

	return stats, nil
}

// SolanaATAOwnerHelper 辅助函数集合
type SolanaATAOwnerHelper struct {
	repo SolanaATAOwnerRepo
	log  *log.Helper
}

// NewSolanaATAOwnerHelper 创建辅助函数实例
func NewSolanaATAOwnerHelper(repo SolanaATAOwnerRepo, logger log.Logger) *SolanaATAOwnerHelper {
	return &SolanaATAOwnerHelper{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// UpsertFromSolanaKeys 从 Solana 公钥创建或更新记录
func (h *SolanaATAOwnerHelper) UpsertFromSolanaKeys(ctx context.Context, ataKey, ownerKey solana.PublicKey, source string) error {
	record := &model.SolanaATAOwner{
		ATAAddress:         ataKey.String(),
		OwnerAddress:       ownerKey.String(),
		ChainIndex:         501, // Solana 主网
		Source:             source,
		VerificationStatus: "verified",
	}
	now := time.Now()
	record.LastVerifiedAt = &now

	return h.repo.BatchUpsert(ctx, []*model.SolanaATAOwner{record})
}

// BatchUpsertFromSolanaKeys 批量从 Solana 公钥创建或更新记录
func (h *SolanaATAOwnerHelper) BatchUpsertFromSolanaKeys(ctx context.Context, ataOwnerPairs map[solana.PublicKey]solana.PublicKey, source string) error {
	if len(ataOwnerPairs) == 0 {
		return nil
	}

	records := make([]*model.SolanaATAOwner, 0, len(ataOwnerPairs))
	now := time.Now()

	for ataKey, ownerKey := range ataOwnerPairs {
		record := &model.SolanaATAOwner{
			ATAAddress:         ataKey.String(),
			OwnerAddress:       ownerKey.String(),
			ChainIndex:         501, // Solana 主网
			Source:             source,
			VerificationStatus: "verified",
			LastVerifiedAt:     &now,
		}
		records = append(records, record)
	}

	return h.repo.BatchUpsert(ctx, records)
}
