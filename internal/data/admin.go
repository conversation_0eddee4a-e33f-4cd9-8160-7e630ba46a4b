package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"

	"gorm.io/gorm"
)

type adminRepo struct {
	data *Data
}

func NewAdminRepo(data *Data) biz.AdminRepo {
	return &adminRepo{data}
}

func (repo *adminRepo) Create(ctx context.Context, admin *model.Admin) error {
	return repo.data.db.WithContext(ctx).Model(&model.Admin{}).Create(admin).Error
}

func (repo *adminRepo) GetByUsernameAndPassword(ctx context.Context, username string, password string) (*model.Admin, error) {
	admin := &model.Admin{}
	err := repo.data.db.WithContext(ctx).Model(&model.Admin{}).
		Where("username=?", username).
		Where("password=?", password).Take(admin).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return admin, nil
}
