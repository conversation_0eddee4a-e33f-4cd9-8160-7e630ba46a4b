package weidubot

import (
	"byd_wallet/internal/biz/rent"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/fbsobreira/gotron-sdk/pkg/address"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
)

type TronRentRequester struct {
	log *log.Helper

	rentCli *Client
	tronCli *tron.RoundRobinClient
	db      *gorm.DB
	rd      redis.UniversalClient

	// taskPool pond.Pool
	// stopCh   chan struct{}
}

func NewTronRentRequester(logger log.Logger,
	rentCli *Client,
	tronCli *tron.RoundRobinClient,
	db *gorm.DB,
	rd redis.UniversalClient) (*TronRentRequester, error) {
	r := &TronRentRequester{
		log: log.NewHelper(logger),

		rentCli: rentCli,
		tronCli: tronCli,
		db:      db,
		rd:      rd,
	}
	return r, nil
}

func (r *TronRentRequester) buildReceiveTx(fromAddr, toAddr string, amountSun int64) (txJson []byte, err error) {
	if fromAddr == "" {
		err = fmt.Errorf("from address is empty")
		return
	}
	if amountSun <= 0 {
		err = fmt.Errorf("amount is invalid")
		return
	}

	tronCli := r.tronCli.Next()
	txe, err := tronCli.Transfer(fromAddr, toAddr, amountSun)
	if err != nil {
		err = fmt.Errorf("build tron receive tx error: %w", err)
		return
	}
	txRaw := txe.Transaction.RawData
	if len(txRaw.Contract) == 0 {
		err = fmt.Errorf("tx raw contract is empty")
		return
	}
	contract := txRaw.Contract[0]
	data := &core.TransferContract{}
	if err = contract.GetParameter().UnmarshalTo(data); err != nil {
		err = fmt.Errorf("unmarshal transfer contract: %w", err)
		return
	}
	txRaw.Expiration = time.Now().Add(6 * time.Minute).UnixMilli()
	txRawBts, err := proto.Marshal(txRaw)
	if err != nil {
		err = fmt.Errorf("marshal tx raw error: %w", err)
		return
	}
	hash := sha256.Sum256(txRawBts)
	txHash := hex.EncodeToString(hash[:])
	resTx := map[string]interface{}{
		"txID":         txHash,
		"visible":      false,
		"raw_data_hex": hex.EncodeToString(txRawBts),
		"raw_data": map[string]interface{}{
			"contract": []map[string]interface{}{
				{
					"parameter": map[string]interface{}{
						"value": map[string]interface{}{
							"amount":        data.Amount,
							"owner_address": hex.EncodeToString(data.OwnerAddress),
							"to_address":    hex.EncodeToString(data.ToAddress),
						},
						"type_url": "type.googleapis.com/protocol.TransferContract",
					},
					"type": "TransferContract",
				},
			},
			"ref_block_bytes": hex.EncodeToString(txRaw.RefBlockBytes),
			"ref_block_hash":  hex.EncodeToString(txRaw.RefBlockHash),
			"timestamp":       txRaw.Timestamp,
			"expiration":      txRaw.Expiration,
		},
	}
	txJson, err = json.Marshal(resTx)
	if err != nil {
		err = fmt.Errorf("marshal tx error: %w", err)
		return
	}
	return
}

func (r *TronRentRequester) AddTronRentRecord(ctx context.Context, record *model.TronRentRecord) error {
	if record.PlatformAddr == "" {
		return errors.New("platform address is empty")
	}
	cp, op, ptn, err := r.queryPreorderInfo(ctx, int64(record.PledgeNum), record.CustomPrice)
	if err != nil {
		return err
	}
	record.PledgeTrxNum = ptn.String()
	record.ChannelPrice = cp
	record.OrderPrice = op
	record.PledgeMinute = 0
	record.PledgeHour = 1 // NOTE: const defaultTronRentPeriod
	txByte, err := r.buildReceiveTx(record.FromAddress, record.PlatformAddr, ptn.Mul(decimalsTrx).IntPart())
	if err != nil {
		return err
	}
	record.Transaction = txByte
	return nil
}

var decimalsTrx = decimal.NewFromInt(1000000)

func (r *TronRentRequester) QueryPreorderInfo(ctx context.Context, record *model.TronRentRecord) error {
	cp, op, ptn, err := r.queryPreorderInfo(ctx, int64(record.PledgeNum), record.CustomPrice)
	if err != nil {
		return err
	}
	record.ChannelPrice = cp
	record.OrderPrice = op
	record.PledgeTrxNum = ptn.String()
	return nil
}

func (r *TronRentRequester) queryPreorderInfo(ctx context.Context, pledgeNum int64, customPrice decimal.Decimal) (
	channelPrice, orderPrice, pledgeTrxNum decimal.Decimal, err error) {
	reply, err := r.rentCli.QueryEnergyPrice(ctx, pledgeNum, defaultTronRentPeriod)
	if err != nil {
		return
	}
	channelPrice = decimal.NewFromFloat(reply.Price)
	if customPrice.GreaterThan(channelPrice) {
		orderPrice = customPrice
	} else {
		orderPrice = channelPrice
	}
	// totalCost = pledge cost trx + delegation cost trx
	pledgeTrxNum = orderPrice.
		Mul(decimal.NewFromInt(pledgeNum)).
		Div(decimalsTrx).
		Add(decimal.NewFromFloat(reply.Fee))
	return
}

func (r *TronRentRequester) UploadHash(ctx context.Context, record *rent.UploadHashReq) ([]string, error) {
	signTx := map[string]interface{}{}
	err := json.Unmarshal([]byte(record.SignedData), &signTx)
	if err != nil {
		return nil, fmt.Errorf("parse signed data fail: %w", err)
	}
	ss := signTx["signature"].([]interface{})
	if len(ss) == 0 {
		return nil, errors.New("signature is empty")
	}
	sigHex, ok := ss[0].(string)
	if !ok {
		return nil, errors.New("signature is not string")
	}
	sig, err := hex.DecodeString(sigHex)
	if err != nil {
		return nil, fmt.Errorf("decode signed data error: %w", err)
	}

	trTx := struct {
		TxID       string `json:"txID"`
		RawDataHex string `json:"raw_data_hex"`
	}{}
	err = json.Unmarshal(record.Transaction, &trTx)
	if err != nil {
		return nil, fmt.Errorf("parse transaction: %w", err)
	}

	// check tx hash
	if trTx.TxID != record.FromHash {
		return nil, fmt.Errorf("txID mismatch: %s != %s", trTx.TxID, record.FromHash)
	}

	// get tx raw object
	txRawBytes, err := hex.DecodeString(trTx.RawDataHex)
	if err != nil {
		return nil, fmt.Errorf("decode raw data hex: %w", err)
	}
	txRaw := &core.TransactionRaw{}
	err = proto.Unmarshal(txRawBytes, txRaw)
	if err != nil {
		return nil, fmt.Errorf("unmarshal tx raw: %w", err)
	}
	if len(txRaw.Contract) == 0 {
		return nil, fmt.Errorf("no contract in transaction")
	}
	contract := txRaw.Contract[0]
	data := &core.TransferContract{}
	if err = contract.GetParameter().UnmarshalTo(data); err != nil {
		return nil, fmt.Errorf("unmarshal transfer contract: %w", err)
	}

	// check signature
	ok, err = verifyTxSignature(trTx.TxID, sigHex, address.Address(data.OwnerAddress).String())
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("invalid signature")
	}

	// broadcast tx
	tx := &core.Transaction{
		RawData:   txRaw,
		Signature: [][]byte{sig},
	}
	tronCli := r.tronCli.Next()
	res, err := tronCli.Broadcast(tx)
	if err != nil {
		return nil, fmt.Errorf("invoke broadcast: %w", err)
	}
	if !res.Result {
		return nil, fmt.Errorf("broadcast tx fail: %+v", res)
	}

	// buy energy
	reply, err := r.rentCli.BuyEnergy(context.Background(), &BuyEnergyReq{
		Address: record.PledgeAddress,
		Count:   int64(record.PledgeNum),
		Period:  defaultTronRentPeriod,
	})
	if err != nil {
		return nil, fmt.Errorf("invoke buy energy: %w", err)
	}
	record.Status = model.TronRentStatusSuccess
	record.ChannelOrderId = reply.OrderSn
	record.ActualChannelPrice = decimal.NewFromFloat(reply.Price)

	return nil, nil
}

func verifyTxSignature(txHashHex, signatureHex, ownerAddress string) (bool, error) {
	txHash, err := hex.DecodeString(txHashHex)
	if err != nil {
		return false, fmt.Errorf("decode tx hash: %v", err)
	}
	if len(txHash) != 32 {
		return false, fmt.Errorf("tx hash length must be 32 bytes")
	}
	signature, err := hex.DecodeString(signatureHex)
	if err != nil {
		return false, fmt.Errorf("decode signature: %v", err)
	}
	if len(signature) != 65 {
		return false, fmt.Errorf("signature length must be 65 bytes")
	}
	// Fix V
	if signature[64] >= 27 {
		signature[64] -= 27
	}

	pubKey, err := crypto.SigToPub(txHash, signature)
	if err != nil {
		return false, fmt.Errorf("recover pubkey: %v", err)
	}
	addr1 := utils.TronHexAddressFromPubKey(pubKey)
	addr := utils.Base58CheckEncodeTRON(addr1)
	return addr == ownerAddress, nil
}
