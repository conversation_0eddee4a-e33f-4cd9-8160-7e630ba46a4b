package weidubot

import (
	"byd_wallet/internal/biz/rent"
	"byd_wallet/internal/biz/syncer/chain/tron"
	"byd_wallet/model"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/btcsuite/btcd/btcec/v2"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func TestQueryPreorderInfo(t *testing.T) {
	if apiSecret == "" {
		t.Skip("no api secret")
	}
	rentCli, err := NewClient(log.DefaultLogger, &ClientConf{ApiKey: apiKey, ApiSecret: apiSecret, ApiUrl: apiUrl})
	assert.NoError(t, err)
	cli, err := NewTronRentRequester(log.DefaultLogger,
		rentCli,
		nil, nil, nil)
	assert.NoError(t, err)

	record := &model.TronRentRecord{
		PledgeNum:   1000,
		CustomPrice: decimal.NewFromFloat(60),
	}
	err = cli.QueryPreorderInfo(t.Context(), record)
	assert.Error(t, err)

	record.PledgeNum = minEnergyCount
	err = cli.QueryPreorderInfo(t.Context(), record)
	assert.NoError(t, err)
	assert.Equal(t, "60", record.OrderPrice.String())
	assert.Equal(t, minEnergyCount, record.PledgeNum)

	t.Logf("record: %+v", record)
}

func TestTronRent(t *testing.T) {
	if apiSecret == "" {
		t.Skip("no api secret")
	}

	dsn := "host=127.0.0.1 user=root password=root dbname=wallet_test port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	assert.NoError(t, err)
	assert.NoError(t, db.AutoMigrate(
		&model.TronRentRecord{},
	))
	assert.NoError(t, db.Exec("delete from public."+(&model.TronRentRecord{}).TableName()).Error)

	rd := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})
	assert.NoError(t, rd.FlushAll(t.Context()).Err())

	tronCli, err := tron.NewRoundRobinClient([]string{tronRPC})
	assert.NoError(t, err)
	rentCli, err := NewClient(log.DefaultLogger, &ClientConf{ApiKey: apiKey, ApiSecret: apiSecret, ApiUrl: apiUrl})
	assert.NoError(t, err)
	cli, err := NewTronRentRequester(log.DefaultLogger,
		rentCli,
		tronCli, db, rd)
	assert.NoError(t, err)
	record := &model.TronRentRecord{
		PledgeNum:     minEnergyCount,
		CustomPrice:   decimal.NewFromFloat(60),
		FromAddress:   fromAddress,
		PledgeAddress: fromAddress,
		PlatformAddr:  toAddress,
	}
	err = cli.AddTronRentRecord(t.Context(), record)
	assert.NoError(t, err)

	t.Logf("record: %+v", record)

	tx := make(map[string]interface{})
	txBts := []byte(record.Transaction)
	err = json.Unmarshal(txBts, &tx)
	assert.NoError(t, err)
	txHash := tx["txID"].(string)

	sign, err := signTx(txHash, fromPrivateKey)
	assert.NoError(t, err)

	// assert.NoError(t, cli.Start(t.Context()))

	record.Status = model.TronRentStatusPending
	record.TxHash = txHash
	record.SignedData = sign
	assert.NoError(t, db.Create(record).Error)

	_, err = cli.UploadHash(t.Context(), &rent.UploadHashReq{
		OrderId:     record.OrderId,
		FromHash:    record.TxHash,
		SignedData:  record.SignedData,
		Transaction: txBts,
	})
	assert.NoError(t, err)

	// time.Sleep(5 * time.Second)
	// _ = cli.Stop(t.Context())
	// time.Sleep(2 * time.Second)
}

func signTx(txHash, privateKey string) (signHex string, err error) {
	hash, _ := hex.DecodeString(txHash)
	privateKeyBytes, _ := hex.DecodeString(privateKey)
	sk, _ := btcec.PrivKeyFromBytes(privateKeyBytes)
	signature, err := crypto.Sign(hash, sk.ToECDSA())
	if err != nil {
		err = fmt.Errorf("sign failed: %v", err)
		return
	}
	signHex = hex.EncodeToString(signature)
	return
}
