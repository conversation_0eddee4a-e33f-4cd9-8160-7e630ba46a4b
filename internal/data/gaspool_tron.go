package data

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool/paymaster/tron"
	"byd_wallet/internal/data/weidubot"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"crypto/ecdsa"
	"encoding/hex"
	"fmt"
	"strconv"
	"strings"
	"time"

	tronchain "byd_wallet/internal/biz/syncer/chain/tron"

	"github.com/dtm-labs/rockscache"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/fbsobreira/gotron-sdk/pkg/address"
	"github.com/shopspring/decimal"
)

type tronAsyncTaskMgr struct {
	*Data
}

func NewTronAsyncTaskMgr(data *Data) tron.AsyncTaskMgr {
	return &tronAsyncTaskMgr{
		Data: data,
	}
}

func (t *tronAsyncTaskMgr) keyWaitDepositTxTask() string {
	return "trxpm:deposittx"
}

func (t *tronAsyncTaskMgr) AddWaitDepositTxTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return t.rd.SAdd(ctx, t.keyWaitDepositTxTask(), idstr).Err()
}

func (t *tronAsyncTaskMgr) AllWaitDepositTxTask(ctx context.Context) ([]uint, error) {
	values, err := t.rd.SMembers(ctx, t.keyWaitDepositTxTask()).Result()
	if err != nil {
		return nil, err
	}
	ids := []uint{}
	for _, v := range values {
		id, err := strconv.Atoi(v)
		if err != nil {
			continue
		}
		ids = append(ids, uint(id))
	}
	return ids, nil
}

func (t *tronAsyncTaskMgr) RemoveWaitDepositTxTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return t.rd.SRem(ctx, t.keyWaitDepositTxTask(), idstr).Err()
}

func (t *tronAsyncTaskMgr) keyWaitGasTask() string {
	return "trxpm:waitgas"
}

func (t *tronAsyncTaskMgr) AddWaitGasTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return t.rd.SAdd(ctx, t.keyWaitGasTask(), idstr).Err()
}

func (t *tronAsyncTaskMgr) AllWaitGasTask(ctx context.Context) ([]uint, error) {
	values, err := t.rd.SMembers(ctx, t.keyWaitGasTask()).Result()
	if err != nil {
		return nil, err
	}
	ids := []uint{}
	for _, v := range values {
		id, err := strconv.Atoi(v)
		if err != nil {
			continue
		}
		ids = append(ids, uint(id))
	}
	return ids, nil
}

func (t *tronAsyncTaskMgr) RemoveWaitGasTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return t.rd.SRem(ctx, t.keyWaitGasTask(), idstr).Err()
}

type tronAddressChecker struct {
	*Data
	tronCli *tronchain.RoundRobinClient
}

func NewTronAddressChecker(data *Data,
	tronCli *tronchain.RoundRobinClient) tron.TronAddressChecker {
	return &tronAddressChecker{
		Data:    data,
		tronCli: tronCli,
	}
}

func (t *tronAddressChecker) IsActivatedAddress(ctx context.Context, address string) (bool, error) {
	opts := rockscache.NewDefaultOptions()
	opts.Context = ctx
	opts.StrongConsistency = false // not need
	dbCache := rockscache.NewClient(t.rd, opts)

	const exists = "1"
	result, err := dbCache.Fetch2(ctx,
		"existstronaaddr:"+address,
		24*time.Hour,
		func() (string, error) {
			cli := t.tronCli.Next()
			acc, err := cli.GetAccount(address)
			if err != nil {
				if strings.Contains(err.Error(), "account not found") {
					return "", nil
				}
				return "", err
			}
			if acc == nil {
				return "", nil
			}
			return exists, nil
		})
	if err != nil {
		return false, fmt.Errorf("db cache error: %v", err)
	}
	return result == exists, nil
}

type tronBandwidthPayWallet struct {
	*Data
	tronCli *tronchain.RoundRobinClient

	pk       *ecdsa.PrivateKey
	fromAddr string
}

func NewTronBandwidthPayWallet(data *Data,
	tronCli *tronchain.RoundRobinClient) (tron.TronBandwidthPayWallet, error) {
	w := &tronBandwidthPayWallet{
		Data:    data,
		tronCli: tronCli,
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	pk, addr, err := w.getPrivateKey(ctx)
	if err != nil {
		return nil, err
	}
	w.pk = pk
	w.fromAddr = addr
	return w, nil
}

func (w *tronBandwidthPayWallet) getPrivateKey(ctx context.Context) (pk *ecdsa.PrivateKey, pubAddr string, err error) {
	ha := &model.HotAddress{}
	err = w.DB(ctx).
		Model(&model.HotAddress{}).
		Where("chain_index = ?", constant.TronChainIndex).
		Take(ha).Error
	if err != nil {
		err = fmt.Errorf("get hot address error: %w", err)
		return
	}
	bts, err := utils.DecryptWithAESGCM(ha.PrivateKey, ha.Key)
	if err != nil {
		err = fmt.Errorf("decrypt error: %w", err)
		return
	}
	pk, err = crypto.HexToECDSA(string(bts))
	if err != nil {
		err = fmt.Errorf("privateKeyHex error: %w", err)
		return
	}
	pubAddr = address.PubkeyToAddress(pk.PublicKey).String()
	if pubAddr != ha.Address {
		err = fmt.Errorf("address error: %s != %s", pubAddr, ha.Address)
		return
	}
	return
}

func (w *tronBandwidthPayWallet) TransferTrx(ctx context.Context, req *tron.TransferTrxReq) (reply *tron.TransferTrxReply, err error) {
	cli := w.tronCli.Next()
	extx, err := cli.Transfer(w.fromAddr, req.ReceiveAddress, req.Amount.IntPart())
	if err != nil {
		err = fmt.Errorf("transfer trx error: %v", err)
		return
	}

	signature, err := crypto.Sign(extx.Txid, w.pk)
	if err != nil {
		return nil, fmt.Errorf("sign error: %w", err)
	}
	extx.Transaction.Signature = append(extx.Transaction.Signature, signature)

	ret, err := cli.Broadcast(extx.GetTransaction())
	if err != nil {
		err = fmt.Errorf("broadcast trx error: %v", err)
		return
	}
	if !ret.Result {
		err = fmt.Errorf("broadcast trx fail: %s", ret.String())
		return
	}
	reply = &tron.TransferTrxReply{
		Amount: req.Amount,
		TxHash: hex.EncodeToString(extx.GetTxid()),
	}
	return
}

type tronRentApi struct {
	*Data
	rentCli *weidubot.Client
}

func NewTronRentApi(data *Data, rentCli *weidubot.Client) tron.TronRentApi {
	return &tronRentApi{
		Data:    data,
		rentCli: rentCli,
	}
}

func (t *tronRentApi) GetEnergyPrice(ctx context.Context, req *tron.GetEnergyPriceReq) (price decimal.Decimal, err error) {
	cfg := &model.TronRentConfig{}
	err = t.DB(ctx).
		Model(&model.TronRentConfig{}).
		Where("channel = ?", model.TronRentChannelWeidubot).
		Take(cfg).Error
	if err != nil {
		return
	}
	price = cfg.CustomPrice
	reply, err := t.rentCli.QueryEnergyPrice(ctx, req.PledgeNum.RoundUp(0).IntPart(), "1h")
	if err != nil {
		return
	}
	actualPrice := decimal.NewFromFloat(reply.Price)
	if price.LessThan(actualPrice) {
		price = actualPrice
	}
	return
}

func (t *tronRentApi) BuyEnergy(ctx context.Context, req *tron.BuyEnergyReq) (reply *tron.BuyEnergyReply, err error) {
	res, err := t.rentCli.BuyEnergy(ctx, &weidubot.BuyEnergyReq{
		Address: req.ReceiveAddr,
		Count:   req.PledgeNum.RoundUp(0).IntPart(),
		Period:  "1h",
	})
	if err != nil {
		return nil, fmt.Errorf("invoke buy energy: %w", err)
	}
	return &tron.BuyEnergyReply{
		OrderID: res.OrderSn,
		Amount:  decimal.NewFromFloat(res.Amount).Mul(tron.DecimalsTRX),
		Price:   decimal.NewFromFloat(res.Price),
	}, nil
}
