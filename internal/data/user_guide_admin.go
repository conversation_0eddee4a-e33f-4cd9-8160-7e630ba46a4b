package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
)

// UserGuideAdminRepo 用户指南仓库实现
type UserGuideAdminRepo struct {
	*Data
}

// NewUserGuideAdminRepo 创建用户指南仓库
func NewUserGuideAdminRepo(data *Data) biz.UserGuideAdminRepo {
	return &UserGuideAdminRepo{
		data,
	}
}

// CreateUserGuide 创建用户指南
func (r *UserGuideAdminRepo) CreateUserGuide(ctx context.Context, guide *model.UserGuide) error {
	return r.DB(ctx).Create(guide).Error
}

// UpdateUserGuide 更新用户指南
func (r *UserGuideAdminRepo) UpdateUserGuide(ctx context.Context, guide *model.UserGuide) error {
	return r.DB(ctx).Save(guide).Error
}

// DeleteUserGuide 删除用户指南
func (r *UserGuideAdminRepo) DeleteUserGuide(ctx context.Context, id uint) error {
	m := &model.UserGuide{}
	m.ID = id
	return r.DB(ctx).Delete(m).Error
}

// GetUserGuide 获取用户指南详情
func (r *UserGuideAdminRepo) GetUserGuide(ctx context.Context, id uint) (*model.UserGuide, error) {
	var guide model.UserGuide
	err := r.DB(ctx).
		Preload("Category").
		Preload("Contents").
		Take(&guide, id).Error
	if err != nil {
		return nil, err
	}
	return &guide, nil
}

// ListUserGuide 用户指南列表
func (r *UserGuideAdminRepo) ListUserGuide(ctx context.Context, filter biz.AdminUserGuideFilter) ([]*model.UserGuide, int64, error) {
	var guides []*model.UserGuide
	var total int64

	query := r.DB(ctx).Model(&model.UserGuide{})

	// 分类过滤
	if filter.CategoryID > 0 {
		query = query.Where("category_id = ?", filter.CategoryID)
	}

	// 语言过滤
	if filter.Language != "" {
		query = query.Where("language = ?", filter.Language)
	}

	err := query.Scopes(Paginate(filter.Pagination)).
		Preload("Category").
		Preload("Contents").
		Order("id DESC").
		Find(&guides).Offset(-1).Count(&total).Error

	return guides, total, err
}

// CreateUserGuideCategory 创建用户指南分类
func (r *UserGuideAdminRepo) CreateUserGuideCategory(ctx context.Context, category *model.UserGuideCategory) error {
	return r.DB(ctx).Create(category).Error
}

// UpdateUserGuideCategory 更新用户指南分类
func (r *UserGuideAdminRepo) UpdateUserGuideCategory(ctx context.Context, category *model.UserGuideCategory) error {
	return r.DB(ctx).Save(category).Error
}

// DeleteUserGuideCategory 删除用户指南分类
func (r *UserGuideAdminRepo) DeleteUserGuideCategory(ctx context.Context, id uint) error {
	m := &model.UserGuideCategory{}
	m.ID = id
	return r.DB(ctx).Delete(m).Error
}

// GetUserGuideCategoryView 获取用户指南分类详情
func (r *UserGuideAdminRepo) GetUserGuideCategoryView(ctx context.Context, id uint) (*model.UserGuideCategoryView, error) {
	category, err := r.GetUserGuideCategory(ctx, id)
	if err != nil {
		return nil, err
	}
	var count int64
	if err = r.DB(ctx).Model(&model.UserGuide{}).Where("category_id=?", category.ID).Count(&count).Error; err != nil {
		return nil, err
	}
	return &model.UserGuideCategoryView{
		UserGuideCategory: *category,
		UserGuideCount:    count,
	}, nil
}

// GetUserGuideCategory 获取用户指南分类详情
func (r *UserGuideAdminRepo) GetUserGuideCategory(ctx context.Context, id uint) (*model.UserGuideCategory, error) {
	var category model.UserGuideCategory
	if err := r.DB(ctx).Take(&category, id).Error; err != nil {
		return nil, err
	}
	return &category, nil
}

// ListUserGuideCategory 用户指南分类列表
func (r *UserGuideAdminRepo) ListUserGuideCategory(ctx context.Context, filter biz.AdminUserGuideCategoryFilter) ([]*model.UserGuideCategoryView, int64, error) {
	var categories []*model.UserGuideCategoryView
	var total int64

	query := r.DB(ctx).Model(&model.UserGuideCategory{})

	// 语言过滤
	if filter.Language != "" {
		query = query.Where("language = ?", filter.Language)
	}

	// 使用子查询一次性获取分类和对应的用户指南数量
	err := query.Select("user_guide_categories.*, (SELECT COUNT(*) FROM user_guides WHERE user_guides.category_id = user_guide_categories.id) as user_guide_count").
		Scopes(Paginate(filter.Pagination)).
		Order("id DESC").
		Find(&categories).Offset(-1).Count(&total).Error

	return categories, total, err
}
