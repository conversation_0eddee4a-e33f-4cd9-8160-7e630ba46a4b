package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
)

type userRepo struct {
	*Data
}

func NewUserRepo(data *Data) biz.UserRepo {
	return &userRepo{data}
}

func (repo *userRepo) FindByUsername(ctx context.Context, username string) (*model.User, error) {
	user := &model.User{}
	err := repo.DB(ctx).Model(&model.User{}).
		Where("username=?", username).
		Take(user).Error
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (repo *userRepo) ListByFilter(ctx context.Context, filter *biz.UserFilter) (list []*model.User, totalCount int64, err error) {
	sql := repo.DB(ctx).Model(&model.User{})
	if filter.OrderBy != "" {
		sql = sql.Order(filter.OrderBy)
	}

	if filter.ID > 0 {
		err = sql.Where("id=?", filter.ID).Find(&list).Error
		if err == nil {
			totalCount = int64(len(list))
		}
		return
	}

	if filter.Username != "" {
		err = sql.Where("username=?", filter.Username).Find(&list).Error
		if err == nil {
			totalCount = int64(len(list))
		}
		return
	}

	if err = sql.Count(&totalCount).Error; err != nil {
		return
	}

	offset := (filter.Page - 1) * filter.PageSize
	err = sql.Offset(int(offset)).Limit(int(filter.PageSize)).Find(&list).Error
	return
}
