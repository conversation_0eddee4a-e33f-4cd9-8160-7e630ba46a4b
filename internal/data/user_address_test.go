package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"fmt"
	"testing"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type UserAddressRepoTestSuite struct {
	suite.Suite
	data *Data
	repo *userAddressRepo
	ctx  context.Context
}

func TestUserAddressRepoTestSuite(t *testing.T) {
	suite.Run(t, new(UserAddressRepoTestSuite))
}

func (s *UserAddressRepoTestSuite) SetupSuite() {
	dsn := "host=127.0.0.1 user=root password=root dbname=wallet_test port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	s.NoError(err)
	s.NoError(db.AutoMigrate(
		&model.User{},
		&model.UserAddress{},
	))
	rd := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})

	s.data = NewData(db, rd)
	s.repo = NewUserAddressRepo(s.data).(*userAddressRepo)
	s.ctx = s.T().Context()
}

func (s *UserAddressRepoTestSuite) SetupTest() {
	for _, tb := range []string{
		(&model.UserAddress{}).TableName(),
		(&model.User{}).TableName(),
	} {
		s.data.db.Exec(fmt.Sprintf("delete from public.%s", tb))
	}
	s.data.rd.FlushAll(context.Background())
}

func (s *UserAddressRepoTestSuite) TestFindByChainIndexAndAddress() {
	user := &model.User{
		Username: "user1",
	}
	s.NoError(s.data.db.Create(user).Error)
	userAddr := &model.UserAddress{
		UserID:     user.ID,
		ChainIndex: 1,
		Address:    "0x123",
	}
	s.NoError(s.data.db.Create(userAddr).Error)

	findUA, err := s.repo.FindByChainIndexAndAddress(s.ctx, 1, "0x123")
	s.NoError(err)
	s.Equal(userAddr.UserID, findUA.UserID)
	s.EqualValues(userAddr.ChainIndex, findUA.ChainIndex)
	s.Equal(userAddr.Address, findUA.Address)
}

func (s *UserAddressRepoTestSuite) TestListViewByFilter() {
	user := &model.User{
		Username: "user1",
	}
	s.NoError(s.data.db.Create(user).Error)
	userAddrs := []*model.UserAddress{
		{
			UserID:     user.ID,
			ChainIndex: 1,
			Address:    "0x123",
		},
		{
			UserID:     user.ID,
			ChainIndex: 2,
			Address:    "0x12322",
		},
		{
			UserID:     user.ID,
			ChainIndex: 2,
			Address:    "0x12323332",
		},
	}
	s.NoError(s.data.db.CreateInBatches(userAddrs, 200).Error)

	s.Run("list all", func() {
		list, totalCount, err := s.repo.ListByFilter(s.ctx, &biz.UserAddressFilter{
			Page:       1,
			PageSize:   10,
			ChainIndex: -1,
		})
		s.NoError(err)
		s.EqualValues(3, totalCount)
		s.Len(list, 3)
	})

	s.Run("list by chain index", func() {
		list, totalCount, err := s.repo.ListByFilter(s.ctx, &biz.UserAddressFilter{
			Page:       1,
			PageSize:   10,
			ChainIndex: 1,
		})
		s.NoError(err)
		s.EqualValues(1, totalCount)
		s.Len(list, 1)
		s.EqualValues(userAddrs[0].Address, list[0].Address)
	})

	s.Run("list by address", func() {
		list, totalCount, err := s.repo.ListByFilter(s.ctx, &biz.UserAddressFilter{
			Page:       1,
			PageSize:   10,
			ChainIndex: -1,
			Address:    userAddrs[1].Address,
		})
		s.NoError(err)
		s.EqualValues(1, totalCount)
		s.Len(list, 1)
		s.EqualValues(userAddrs[1].Address, list[0].Address)
	})
}
