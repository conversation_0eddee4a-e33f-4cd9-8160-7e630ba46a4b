package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"

	"gorm.io/gorm/clause"
)

type userHoldTokenRepo struct {
	*Data
}

func NewUserHoldTokenRepo(d *Data) biz.UserHoldTokenRepo {
	return &userHoldTokenRepo{
		Data: d,
	}
}

func (u *userHoldTokenRepo) BatchSave(ctx context.Context, us []*model.UserHoldToken) error {
	return u.DB(ctx).Model(&model.UserHoldToken{}).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "user_address_id"},
				{Name: "token_asset_id"},
			},
			DoNothing: true,
		}).
		CreateInBatches(us, 200).Error
}

func (u *userHoldTokenRepo) ListViewByFilter(ctx context.Context, filter *biz.UserHoldTokenViewFilter) ([]*biz.UserHoldTokenView, error) {
	if len(filter.UserAddrs) == 0 {
		return nil, nil
	}

	sql := u.DB(ctx).Table((&model.UserHoldToken{}).TableName() + " AS uht").
		Joins("LEFT JOIN user_address AS ua ON uht.user_address_id=ua.id").
		Joins("LEFT JOIN token_assets AS ta ON uht.token_asset_id=ta.id").
		Select("ua.address AS wallet_address, ta.*")

	if len(filter.UserAddrs) == 1 {
		ua := filter.UserAddrs[0]
		sql = sql.Where("ua.chain_index=? AND ua.address=?", ua.ChainIndex, ua.Address)
	} else {
		whereIns := make([][]interface{}, 0, len(filter.UserAddrs))
		for _, v := range filter.UserAddrs {
			whereIns = append(whereIns, []interface{}{v.ChainIndex, v.Address})
		}
		sql = sql.Where("(ua.chain_index,ua.address) IN (?)", whereIns)
	}

	list := []*biz.UserHoldTokenView{}
	err := sql.
		Order("uht.id desc").
		Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}
