package data

import (
	v1 "byd_wallet/api/wallet/v1"
	"byd_wallet/internal/biz"
	"byd_wallet/internal/data/okx"
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/dtm-labs/rockscache"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

type tokenAssetRepo struct {
	log *log.Helper

	data   *Data
	okxcli *okx.Client
	okxdto *OKXChainIndexDto
}

func NewTokenAssetRepo(logger log.Logger, data *Data, okxcli *okx.Client, okxdto *OKXChainIndexDto) biz.TokenAssetRepo {
	return &tokenAssetRepo{
		log:    log.NewHelper(logger),
		data:   data,
		okxcli: okxcli,
		okxdto: okxdto,
	}
}

func (repo *tokenAssetRepo) ExistsByChainIndexAndAddress(ctx context.Context, chainIndex int64, address string) (bool, error) {
	opts := rockscache.NewDefaultOptions()
	opts.Context = ctx
	opts.StrongConsistency = false // not need
	dbCache := rockscache.NewClient(repo.data.rd, opts)

	const exists = "1"
	result, err := dbCache.Fetch2(ctx,
		fmt.Sprintf("exists_ta:%d:%s", chainIndex, address),
		24*time.Hour,
		func() (string, error) {
			var id uint
			err := repo.data.DB(ctx).Model(&model.TokenAsset{}).
				Select("id").
				Where("chain_index = ? and address = ?", chainIndex, address).
				Limit(1).
				Scan(&id).Error
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return "", nil
				}
				return "", fmt.Errorf("db find error: %w", err)
			}
			if id == 0 {
				return "", nil
			}
			return exists, nil
		})
	if err != nil {
		return false, fmt.Errorf("db cache error: %v", err)
	}
	return result == exists, nil
}

func (repo *tokenAssetRepo) Save(ctx context.Context, tokenAsset *model.TokenAsset) (*model.TokenAsset, error) {
	err := repo.data.DB(ctx).Create(tokenAsset).Error
	if err != nil {
		return nil, err
	}
	return tokenAsset, nil
}

func (repo *tokenAssetRepo) ListViewByFilter(ctx context.Context, filter *biz.TokenAssetViewFilter) (list []*model.TokenAsset, totalCount int64, err error) {
	sql := repo.data.DB(ctx).
		Table((&model.TokenAsset{}).TableName() + " AS ta").
		Joins("LEFT JOIN token_asset_stars AS tas ON ta.id = tas.token_asset_id").
		Select("ta.*,tas.sort_order").
		Order("CASE WHEN sort_order IS NULL THEN 1 ELSE 0 END,sort_order desc")

	if len(filter.ChainIndexes) == 1 {
		sql = sql.Where("chain_index = ?", filter.ChainIndexes[0])
	} else if len(filter.ChainIndexes) > 1 {
		sql = sql.Where("chain_index in ?", filter.ChainIndexes)
	}

	if filter.Search != "" {
		search := "%" + filter.Search + "%"
		sql = sql.Where("address LIKE ? OR name LIKE ? OR symbol LIKE ?", search, search, search)
	}

	if err = sql.Count(&totalCount).Error; err != nil {
		return
	}

	offset := (filter.Page - 1) * filter.PageSize
	err = sql.Offset(int(offset)).Limit(int(filter.PageSize)).Find(&list).Error
	return
}

func (repo *tokenAssetRepo) tokenAssetInLocalCacheKey(chainIndex int64, address string) string {
	return fmt.Sprintf("%d%s", chainIndex, address)
}

func (repo *tokenAssetRepo) getTokenAssetInLocalCache(ctx context.Context, tokenIDs [][]interface{}) (map[string]*model.TokenAsset, error) {
	// TODO: cache
	var cis []*model.TokenAsset
	err := repo.data.db.WithContext(ctx).Model(&model.TokenAsset{}).Where("(chain_index, address) in (?)", tokenIDs).Find(&cis).Error
	if err != nil {
		return nil, err
	}
	res := make(map[string]*model.TokenAsset)
	for _, ci := range cis {
		res[repo.tokenAssetInLocalCacheKey(ci.ChainIndex, ci.Address)] = ci
	}
	return res, nil
}

func (repo *tokenAssetRepo) ListTokenBalanceByAddress(ctx context.Context, chainIndexes []int64, address string) ([]*v1.TokenBalance, error) {
	idxs := repo.okxdto.ChainIndexReqParam(chainIndexes)
	if len(idxs) == 0 {
		// skip: not supported chain index
		return []*v1.TokenBalance{}, nil
	}

	resp, err := repo.okxcli.AllTokenBalanceByAddress(ctx, &okx.AllTokenBalanceByAddressReq{
		Chains:  idxs,
		Address: address,
		Filter:  "0",
	})
	if err != nil {
		return nil, fmt.Errorf("okx all token balance by address error: %w", err)
	}
	if len(resp.Data) == 0 {
		return []*v1.TokenBalance{}, nil
	}

	tokenIDs := make([][]interface{}, 0, len(resp.Data))
	tabs := resp.Data[0].TokenAssets
	for _, v := range tabs {
		tokenIDs = append(tokenIDs, []interface{}{repo.okxdto.ApiChainIndex(v.ChainIndex), v.TokenAddress})
	}
	tasMap, err := repo.getTokenAssetInLocalCache(ctx, tokenIDs)
	if err != nil {
		return nil, fmt.Errorf("get token asset in local cache error: %w", err)
	}

	res := make([]*v1.TokenBalance, 0, len(resp.Data))
	for _, v := range tabs {
		// 1: token
		// 2: 铭文
		if v.TokenType != "1" {
			continue
		}

		ta := tasMap[repo.tokenAssetInLocalCacheKey(repo.okxdto.okx2Api[v.ChainIndex], v.TokenAddress)]
		if ta == nil {
			continue
		}
		rawB := decimal.Zero
		if v.RawBalance != "" {
			tmp, err := decimal.NewFromString(v.RawBalance)
			if err != nil {
				repo.log.Warnf("parse raw balance error: %s: %s: %s,%s", err.Error(), v.RawBalance, v.ChainIndex, v.TokenAddress)
				continue
			}
			if tmp.IsPositive() {
				rawB = tmp
			}
		}
		if rawB.IsZero() {
			if v.Balance != "" {
				tmp, err := decimal.NewFromString(v.Balance)
				if err != nil {
					repo.log.Warnf("parse balance error: %s: %s: %s,%s", err.Error(), v.Balance, v.ChainIndex, v.TokenAddress)
					continue
				}
				if tmp.IsPositive() {
					if ta.Decimals > 0 {
						rawB = tmp.Mul(decimal.NewFromInt(10).Pow(decimal.NewFromInt(ta.Decimals)))
					} else {
						rawB = tmp
					}
				}
			}
		}
		res = append(res, &v1.TokenBalance{
			ChainIndex: repo.okxdto.ApiChainIndex(v.ChainIndex),
			Address:    v.TokenAddress,
			Symbol:     ta.Symbol,
			Name:       ta.Name,
			Decimals:   ta.Decimals,
			RawBalance: rawB.String(),
			Price:      v.TokenPrice,
			LogoUrl:    ta.LogoUrl,
		})
	}

	return res, nil
}

func (repo *tokenAssetRepo) FindByChainIndexAndAddress(ctx context.Context, chainIndex int64, address string) (*model.TokenAsset, error) {
	m := &model.TokenAsset{}
	err := repo.data.db.WithContext(ctx).
		Model(&model.TokenAsset{}).
		Where("chain_index=? and address=?", chainIndex, address).
		Take(m).Error
	if err != nil {
		return nil, err
	}
	return m, nil
}

func (repo *tokenAssetRepo) ListByFilter(ctx context.Context, filter *biz.TokenAssetFilter) (
	list []*model.TokenAsset, totalCount int64, err error) {
	sql := repo.data.db.WithContext(ctx).Model(&model.TokenAsset{})
	if filter.OrderBy != "" {
		sql = sql.Order(filter.OrderBy)
	}

	if filter.ChainIndex != -1 {
		sql = sql.Where("chain_index=?", filter.ChainIndex)
	}
	if filter.Address != "" {
		sql = sql.Where("address=?", filter.Address)
	}

	if filter.ChainIndex != -1 && filter.Address != "" {
		// unique index
		// chain_index,address
		err = sql.Find(&list).Error
		if err == nil {
			totalCount = int64(len(list))
		}
		return
	}

	if filter.Symbol != "" {
		sql = sql.Where("symbol=?", filter.Symbol)
	}

	if err = sql.Count(&totalCount).Error; err != nil {
		return
	}

	offset := (filter.Page - 1) * filter.PageSize
	err = sql.Offset(int(offset)).Limit(int(filter.PageSize)).Find(&list).Error
	return
}

func (repo *tokenAssetRepo) UpdateByUpdates(ctx context.Context, id uint, updates map[string]interface{}) error {
	return repo.data.db.WithContext(ctx).Model(&model.TokenAsset{}).Where("id=?", id).Updates(updates).Error
}
