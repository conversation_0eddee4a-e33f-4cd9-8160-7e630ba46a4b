package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"fmt"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type MarketRepoTestSuite struct {
	suite.Suite
	data *Data
	repo *marketRepo
}

func TestMarketRepoTestSuite(t *testing.T) {
	suite.Run(t, new(MarketRepoTestSuite))
}

func (s *MarketRepoTestSuite) SetupSuite() {
	dsn := "host=127.0.0.1 user=root password=root dbname=wallet_test port=5432 sslmode=disable"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if !s.NoError(err) {
		return
	}
	if !s.NoError(db.AutoMigrate(
		&model.CoinInfo{},
		&model.TokenAsset{},
		&model.TokenAssetRank{},
	)) {
		return
	}
	rd := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})

	s.data = NewData(db, rd)
	s.repo = NewMarketRepo(s.data, log.DefaultLogger, nil).(*marketRepo)
}

func (s *MarketRepoTestSuite) SetupTest() {
	for _, tb := range []string{
		(&model.TokenAsset{}).TableName(),
		(&model.TokenAssetRank{}).TableName(),
	} {
		s.data.db.Exec(fmt.Sprintf("delete from %s", tb))
	}
	s.data.rd.FlushAll(context.Background())
}

func (s *MarketRepoTestSuite) TestListTokenByPopular_NoKeyword() {
	ta := model.TokenAsset{
		ChainIndex: 1,
		ChainId:    "1",
		Address:    "0xa1111",
		Name:       "pta.Name",
		Symbol:     "pta.Symbol",
		Decimals:   13,
		LogoUrl:    "pta.LogoUrl",
	}
	err := s.data.db.Create(&ta).Error
	if !s.NoError(err) {
		return
	}

	err = s.data.db.Create(&model.TokenAssetRank{
		TokenAssetID: ta.ID,
		Rank:         1,
	}).Error
	if !s.NoError(err) {
		return
	}

	list, totalCount, err := s.repo.ListTokenByPopular(s.T().Context(), &biz.PopularTokenFilter{
		Page:         1,
		PageSize:     10,
		ChainIndexes: []int64{},
	})
	if !s.NoError(err) {
		return
	}
	if s.Len(list, 1) {
		s.T().Logf("list: %+v", list[0])
	}
	s.Equal(int64(1), totalCount)
}
