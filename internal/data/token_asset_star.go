package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type tokenAssetStarRepo struct {
	*Data
	log *log.Helper
}

func NewTokenAssetStarRepo(logger log.Logger, data *Data) biz.TokenAssetStarRepo {
	return &tokenAssetStarRepo{
		Data: data,
		log:  log.NewHelper(logger),
	}
}

func (r *tokenAssetStarRepo) Save(ctx context.Context, tas *model.TokenAssetStar) (*model.TokenAssetStar, error) {
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var maxSortOrder int64
		err := tx.Model(&model.TokenAssetStar{}).
			Select("COALESCE(MIN(sort_order), 0)").Scan(&maxSortOrder).Error
		if err != nil {
			return err
		}
		tas.SortOrder = maxSortOrder - 1
		return tx.Create(tas).Error
	})
	if err != nil {
		return nil, err
	}
	return tas, nil
}

func (r *tokenAssetStarRepo) ListViewByFilter(ctx context.Context, filter *biz.TokenAssetStarViewFilter) (list []*model.TokenAssetWithStar, totalCount int64, err error) {
	db := r.db.WithContext(ctx).
		Table((&model.TokenAssetStar{}).TableName() + " AS tas").
		Joins("LEFT JOIN token_assets AS ta ON tas.token_asset_id=ta.id").
		Select("ta.*, tas.sort_order, tas.created_at AS star_created_at, tas.id AS star_id")
	if filter.OrderBy != "" {
		db = db.Order(filter.OrderBy)
	}

	if filter.Address != "" {
		db = db.Where("address = ?", filter.Address)
	}
	if filter.Symbol != "" {
		db = db.Where("symbol = ?", filter.Symbol)
	}
	if filter.ChainIndex != -1 {
		db = db.Where("chain_index = ?", filter.ChainIndex)
	}

	err = db.Count(&totalCount).Error
	if err != nil {
		return
	}

	offset := (filter.Page - 1) * filter.PageSize
	err = db.Offset(int(offset)).
		Limit(int(filter.PageSize)).
		Scan(&list).Error
	return
}

func (r *tokenAssetStarRepo) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&model.TokenAssetStar{}).Error
}

func (r *tokenAssetStarRepo) UpdateSortOrder(ctx context.Context, id uint, current, target int64) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var ts []*model.TokenAssetStar
		err := tx.Model(&model.TokenAssetStar{}).Where("sort_order = ?", target).Find(&ts).Error
		if err != nil {
			return err
		}
		switch len(ts) {
		case 0:
			return tx.Model(&model.TokenAssetStar{}).Where("id=? and sort_order=?", id, current).Update("sort_order", target).Error
		case 1:
			s := ts[0]
			err = tx.Model(&model.TokenAssetStar{}).Where("id=? and sort_order=?", s.ID, target).Update("sort_order", current).Error
			if err != nil {
				return err
			}
			return tx.Model(&model.TokenAssetStar{}).Where("id=? and sort_order=?", id, current).Update("sort_order", target).Error
		default:
			return fmt.Errorf("too many token asset star with sort order %d", target)
		}
	})
}
