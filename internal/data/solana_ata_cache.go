package data

import (
	"byd_wallet/common"
	"context"
	"time"

	"github.com/gagliardetto/solana-go"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
)

// Solana ATA Owner 缓存相关常量
const (
	// DefaultATAOwnerCacheExpiration ATA owner 缓存默认过期时间（1小时）
	// ATA owner 关系相对稳定，可以设置较长的缓存时间
	DefaultATAOwnerCacheExpiration = time.Hour

	// ShortATAOwnerCacheExpiration ATA owner 缓存短期过期时间（10分钟）
	// 用于可能发生变化的 ATA owner 关系
	ShortATAOwnerCacheExpiration = 10 * time.Minute

	// LongATAOwnerCacheExpiration ATA owner 缓存长期过期时间（24小时）
	// 用于非常稳定的 ATA owner 关系
	LongATAOwnerCacheExpiration = 24 * time.Hour
)

// SolanaATACache 定义 Solana ATA Owner 缓存服务接口
// 提供 ATA owner 地址的缓存操作接口
type SolanaATACache interface {
	// GetOwnerFromCache 从缓存中获取 ATA 对应的 owner 地址
	GetOwnerFromCache(ctx context.Context, ata solana.PublicKey) (solana.PublicKey, bool, error)

	// SetOwnerToCache 将 ATA 和对应的 owner 地址缓存
	SetOwnerToCache(ctx context.Context, ata solana.PublicKey, owner solana.PublicKey, expiration time.Duration) error

	// DeleteOwnerFromCache 从缓存中删除指定 ATA 的 owner 记录
	DeleteOwnerFromCache(ctx context.Context, ata solana.PublicKey) error

	// BatchGetOwnersFromCache 批量从缓存中获取多个 ATA 对应的 owner 地址
	BatchGetOwnersFromCache(ctx context.Context, atas []solana.PublicKey) (map[string]solana.PublicKey, []solana.PublicKey, error)

	// BatchSetOwnersToCache 批量将 ATA 和对应的 owner 地址缓存
	BatchSetOwnersToCache(ctx context.Context, ataOwnerMap map[string]solana.PublicKey, expiration time.Duration) error
}

// SolanaATACacheService Solana ATA Owner 缓存服务
// 提供基于 Redis 的 ATA (Associated Token Account) owner 地址缓存功能
// 用于优化 Solana paymaster 中的 GetOwner 方法性能
type SolanaATACacheService struct {
	rd  redis.UniversalClient
	log *log.Helper
}

// NewSolanaATACacheService 创建新的 Solana ATA 缓存服务实例
// 参数：
//   - rd: Redis 客户端
//   - logger: 日志记录器
//
// 返回：
//   - *SolanaATACacheService: 缓存服务实例
func NewSolanaATACacheService(rd redis.UniversalClient, logger log.Logger) *SolanaATACacheService {
	return &SolanaATACacheService{
		rd:  rd,
		log: log.NewHelper(logger),
	}
}

// GetOwnerFromCache 从 Redis 缓存中获取 ATA 对应的 owner 地址
// 参数：
//   - ctx: 上下文对象
//   - ata: ATA (Associated Token Account) 公钥
//
// 返回：
//   - solana.PublicKey: owner 公钥，如果缓存未命中则返回空值
//   - bool: 是否在缓存中找到
//   - error: 查询过程中的错误
func (s *SolanaATACacheService) GetOwnerFromCache(ctx context.Context, ata solana.PublicKey) (solana.PublicKey, bool, error) {
	// 生成 Redis 缓存键
	cacheKey := common.GetSolOwner(ata.String())

	// 从 Redis 获取缓存的 owner 地址
	ownerStr, err := s.rd.Get(ctx, cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			// 缓存未命中，返回 false 表示需要从 RPC 获取
			s.log.Debugf("ATA owner 缓存未命中: %s", ata.String())
			return solana.PublicKey{}, false, nil
		}
		// Redis 查询错误
		s.log.Errorf("从 Redis 获取 ATA owner 失败: %s, 错误: %v", ata.String(), err)
		return solana.PublicKey{}, false, err
	}

	// 解析 owner 地址字符串为公钥
	owner, err := solana.PublicKeyFromBase58(ownerStr)
	if err != nil {
		s.log.Errorf("解析缓存的 owner 地址失败: %s, 错误: %v", ownerStr, err)
		// 缓存数据损坏，删除无效缓存
		s.rd.Del(ctx, cacheKey)
		return solana.PublicKey{}, false, err
	}

	s.log.Debugf("ATA owner 缓存命中: %s -> %s", ata.String(), owner.String())
	return owner, true, nil
}

// SetOwnerToCache 将 ATA 和对应的 owner 地址缓存到 Redis
// 参数：
//   - ctx: 上下文对象
//   - ata: ATA (Associated Token Account) 公钥
//   - owner: owner 公钥
//   - expiration: 缓存过期时间
//
// 返回：
//   - error: 缓存设置过程中的错误
func (s *SolanaATACacheService) SetOwnerToCache(ctx context.Context, ata solana.PublicKey, owner solana.PublicKey, expiration time.Duration) error {
	// 生成 Redis 缓存键
	cacheKey := common.GetSolOwner(ata.String())

	// 将 owner 地址缓存到 Redis
	err := s.rd.Set(ctx, cacheKey, owner.String(), expiration).Err()
	if err != nil {
		s.log.Errorf("缓存 ATA owner 到 Redis 失败: %s -> %s, 错误: %v", ata.String(), owner.String(), err)
		return err
	}

	s.log.Debugf("ATA owner 已缓存: %s -> %s, 过期时间: %v", ata.String(), owner.String(), expiration)
	return nil
}

// DeleteOwnerFromCache 从 Redis 缓存中删除指定 ATA 的 owner 记录
// 参数：
//   - ctx: 上下文对象
//   - ata: ATA (Associated Token Account) 公钥
//
// 返回：
//   - error: 删除过程中的错误
func (s *SolanaATACacheService) DeleteOwnerFromCache(ctx context.Context, ata solana.PublicKey) error {
	// 生成 Redis 缓存键
	cacheKey := common.GetSolOwner(ata.String())

	// 从 Redis 删除缓存记录
	err := s.rd.Del(ctx, cacheKey).Err()
	if err != nil {
		s.log.Errorf("从 Redis 删除 ATA owner 缓存失败: %s, 错误: %v", ata.String(), err)
		return err
	}

	s.log.Debugf("ATA owner 缓存已删除: %s", ata.String())
	return nil
}

// BatchGetOwnersFromCache 批量从 Redis 缓存中获取多个 ATA 对应的 owner 地址
// 参数：
//   - ctx: 上下文对象
//   - atas: ATA 公钥列表
//
// 返回：
//   - map[string]solana.PublicKey: ATA 地址到 owner 公钥的映射（仅包含缓存命中的记录）
//   - []solana.PublicKey: 缓存未命中的 ATA 列表
//   - error: 查询过程中的错误
func (s *SolanaATACacheService) BatchGetOwnersFromCache(ctx context.Context, atas []solana.PublicKey) (map[string]solana.PublicKey, []solana.PublicKey, error) {
	if len(atas) == 0 {
		return make(map[string]solana.PublicKey), []solana.PublicKey{}, nil
	}

	// 生成所有 ATA 的缓存键
	cacheKeys := make([]string, len(atas))
	ataKeyMap := make(map[string]solana.PublicKey) // 缓存键到 ATA 的映射
	for i, ata := range atas {
		cacheKey := common.GetSolOwner(ata.String())
		cacheKeys[i] = cacheKey
		ataKeyMap[cacheKey] = ata
	}

	// 批量从 Redis 获取缓存数据
	results, err := s.rd.MGet(ctx, cacheKeys...).Result()
	if err != nil {
		s.log.Errorf("批量获取 ATA owner 缓存失败: %v", err)
		return nil, atas, err // 返回所有 ATA 作为未命中列表
	}

	// 处理批量查询结果
	cachedOwners := make(map[string]solana.PublicKey)
	var missedATAs []solana.PublicKey

	for i, result := range results {
		cacheKey := cacheKeys[i]
		ata := ataKeyMap[cacheKey]

		if result == nil {
			// 缓存未命中
			missedATAs = append(missedATAs, ata)
			continue
		}

		ownerStr, ok := result.(string)
		if !ok {
			s.log.Warnf("缓存数据类型错误: %s", cacheKey)
			missedATAs = append(missedATAs, ata)
			continue
		}

		// 解析 owner 地址
		owner, err := solana.PublicKeyFromBase58(ownerStr)
		if err != nil {
			s.log.Errorf("解析缓存的 owner 地址失败: %s, 错误: %v", ownerStr, err)
			// 删除无效缓存
			s.rd.Del(ctx, cacheKey)
			missedATAs = append(missedATAs, ata)
			continue
		}

		cachedOwners[ata.String()] = owner
	}

	s.log.Debugf("批量 ATA owner 缓存查询完成: 命中 %d 个，未命中 %d 个", len(cachedOwners), len(missedATAs))
	return cachedOwners, missedATAs, nil
}

// BatchSetOwnersToCache 批量将 ATA 和对应的 owner 地址缓存到 Redis
// 参数：
//   - ctx: 上下文对象
//   - ataOwnerMap: ATA 地址到 owner 公钥的映射
//   - expiration: 缓存过期时间
//
// 返回：
//   - error: 缓存设置过程中的错误
func (s *SolanaATACacheService) BatchSetOwnersToCache(ctx context.Context, ataOwnerMap map[string]solana.PublicKey, expiration time.Duration) error {
	if len(ataOwnerMap) == 0 {
		return nil
	}

	// 使用 Redis Pipeline 进行批量操作
	pipe := s.rd.Pipeline()

	for ataStr, owner := range ataOwnerMap {
		cacheKey := common.GetSolOwner(ataStr)
		pipe.Set(ctx, cacheKey, owner.String(), expiration)
	}

	// 执行批量操作
	_, err := pipe.Exec(ctx)
	if err != nil {
		s.log.Errorf("批量缓存 ATA owner 到 Redis 失败: %v", err)
		return err
	}

	s.log.Debugf("批量 ATA owner 已缓存: %d 个记录, 过期时间: %v", len(ataOwnerMap), expiration)
	return nil
}
