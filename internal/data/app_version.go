package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
)

type appVersionRepo struct {
	*Data
}

func NewAppVersionRepo(data *Data) biz.AppVersionRepo {
	return &appVersionRepo{Data: data}
}

func (a *appVersionRepo) GetLatestAppVersion(ctx context.Context) (*model.AppVersion, error) {
	var version model.AppVersion
	if err := a.DB(ctx).Model(&model.AppVersion{}).
		Preload("AppVersionI18Ns").
		Order("id DESC").First(&version).Error; err != nil {
		return nil, err
	}
	return &version, nil
}
