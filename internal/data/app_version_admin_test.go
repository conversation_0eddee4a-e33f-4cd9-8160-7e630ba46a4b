package data

import (
	"byd_wallet/internal/biz"
	"byd_wallet/internal/biz/base"
	"byd_wallet/model"
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// newTestAppVersionDB 创建测试数据库
func newTestAppVersionDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	return db
}

// migrateAppVersionTables 迁移表结构
func migrateAppVersionTables(db *gorm.DB) error {
	return db.AutoMigrate(
		&model.AppVersion{},
		&model.AppVersionI18N{},
	)
}

func TestAppVersionAdminRepo_CreateAppVersion(t *testing.T) {
	db := newTestAppVersionDB()
	err := migrateAppVersionTables(db)
	assert.NoError(t, err)

	data := &Data{db: db}
	repo := NewAppVersionAdminRepo(data)

	ctx := context.Background()
	version := &model.AppVersion{
		Version:              "1.0.0",
		AppType:              "ios",
		DownloadURL:          "https://example.com/download",
		ForceUpdate:          true,
		ReminderType:         model.ReminderEveryOpen,
		// MinCompatibleVersion: "0.9.0",
	}

	err = repo.CreateAppVersion(ctx, version)
	assert.NoError(t, err)
	assert.NotZero(t, version.ID)
}

func TestAppVersionAdminRepo_UpdateAppVersion(t *testing.T) {
	db := newTestAppVersionDB()
	err := migrateAppVersionTables(db)
	assert.NoError(t, err)

	data := &Data{db: db}
	repo := NewAppVersionAdminRepo(data)

	ctx := context.Background()
	version := &model.AppVersion{
		Version:              "1.0.0",
		AppType:              "ios",
		DownloadURL:          "https://example.com/download",
		ForceUpdate:          false,
		ReminderType:         model.ReminderEveryOpen,
		// MinCompatibleVersion: "0.9.0",
	}

	// 创建版本
	err = repo.CreateAppVersion(ctx, version)
	assert.NoError(t, err)

	// 更新版本
	version.Version = "1.0.1"
	version.ForceUpdate = true
	version.ReminderType = model.ReminderDaily
	// version.MinCompatibleVersion = "1.0.0"
	err = repo.UpdateAppVersion(ctx, version)
	assert.NoError(t, err)

	// 验证更新
	updated, err := repo.GetAppVersion(ctx, version.ID)
	assert.NoError(t, err)
	assert.Equal(t, "1.0.1", updated.Version)
	assert.True(t, updated.ForceUpdate)
	assert.Equal(t, model.ReminderDaily, updated.ReminderType)
	// assert.Equal(t, "1.0.0", updated.MinCompatibleVersion)
}

func TestAppVersionAdminRepo_DeleteAppVersion(t *testing.T) {
	db := newTestAppVersionDB()
	err := migrateAppVersionTables(db)
	assert.NoError(t, err)

	data := &Data{db: db}
	repo := NewAppVersionAdminRepo(data)

	ctx := context.Background()
	version := &model.AppVersion{
		Version:              "1.0.0",
		AppType:              "ios",
		DownloadURL:          "https://example.com/download",
		ForceUpdate:          false,
		ReminderType:         model.ReminderEveryOpen,
		// MinCompatibleVersion: "0.9.0",
		AppVersionI18Ns: []*model.AppVersionI18N{
			{
				Language:    "zh",
				Description: "中文",
			},
			{
				Language:    "en",
				Description: "English",
			},
			{
				Language:    "zh-TW",
				Description: "中文（台灣）",
			},
		},
	}

	// 创建版本
	err = repo.CreateAppVersion(ctx, version)
	assert.NoError(t, err)

	// 删除版本
	err = repo.DeleteAppVersion(ctx, version.ID)
	assert.NoError(t, err)

	// 验证删除
	_, err = repo.GetAppVersion(ctx, version.ID)
	assert.Error(t, err)
}

func TestAppVersionAdminRepo_GetAppVersion(t *testing.T) {
	db := newTestAppVersionDB()
	err := migrateAppVersionTables(db)
	assert.NoError(t, err)

	data := &Data{db: db}
	repo := NewAppVersionAdminRepo(data)

	ctx := context.Background()
	version := &model.AppVersion{
		Version:              "1.0.0",
		AppType:              "ios",
		DownloadURL:          "https://example.com/download",
		ForceUpdate:          true,
		ReminderType:         model.ReminderDaily,
		// MinCompatibleVersion: "0.9.0",
		AppVersionI18Ns: []*model.AppVersionI18N{
			{
				Language:    "en",
				Description: "English description",
			},
			{
				Language:    "zh",
				Description: "中文描述",
			},
		},
	}

	// 创建版本
	err = repo.CreateAppVersion(ctx, version)
	assert.NoError(t, err)

	// 获取版本
	retrieved, err := repo.GetAppVersion(ctx, version.ID)
	assert.NoError(t, err)
	assert.Equal(t, version.Version, retrieved.Version)
	assert.Equal(t, version.AppType, retrieved.AppType)
	assert.Equal(t, version.DownloadURL, retrieved.DownloadURL)
	assert.Equal(t, version.ForceUpdate, retrieved.ForceUpdate)
	assert.Equal(t, version.ReminderType, retrieved.ReminderType)
	// assert.Equal(t, version.MinCompatibleVersion, retrieved.MinCompatibleVersion)
	assert.Len(t, retrieved.AppVersionI18Ns, 2)
}

func TestAppVersionAdminRepo_ListAppVersion(t *testing.T) {
	db := newTestAppVersionDB()
	err := migrateAppVersionTables(db)
	assert.NoError(t, err)

	data := &Data{db: db}
	repo := NewAppVersionAdminRepo(data)

	ctx := context.Background()

	// 创建测试数据
	versions := []*model.AppVersion{
		{
			Version:              "1.0.0",
			AppType:              "ios",
			DownloadURL:          "https://example.com/ios",
			ForceUpdate:          false,
			ReminderType:         model.ReminderEveryOpen,
			// MinCompatibleVersion: "0.9.0",
		},
		{
			Version:              "1.0.0",
			AppType:              "android",
			DownloadURL:          "https://example.com/android",
			ForceUpdate:          true,
			ReminderType:         model.ReminderDaily,
			// MinCompatibleVersion: "0.8.0",
		},
		{
			Version:              "1.1.0",
			AppType:              "ios",
			DownloadURL:          "https://example.com/ios-new",
			ForceUpdate:          false,
			ReminderType:         model.ReminderWeekly,
			// MinCompatibleVersion: "1.0.0",
		},
	}

	for _, version := range versions {
		err = repo.CreateAppVersion(ctx, version)
		assert.NoError(t, err)
	}

	// 测试列表查询
	filter := biz.AdminAppVersionFilter{
		Pagination: base.Pagination{
			Page:     1,
			PageSize: 10,
		},
	}

	list, total, err := repo.ListAppVersion(ctx, filter)
	assert.NoError(t, err)
	assert.Equal(t, int64(3), total)
	assert.Len(t, list, 3)

	// 测试按应用类型过滤
	filter.AppType = "ios"
	list, total, err = repo.ListAppVersion(ctx, filter)
	assert.NoError(t, err)
	assert.Equal(t, int64(2), total)
	assert.Len(t, list, 2)
}
