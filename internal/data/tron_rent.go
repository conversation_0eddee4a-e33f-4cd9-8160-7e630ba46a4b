package data

import (
	"byd_wallet/internal/biz/rent"
	"byd_wallet/internal/data/tronify"
	"byd_wallet/internal/data/weidubot"
	"byd_wallet/model"
	"context"
	"fmt"

	"gorm.io/datatypes"
)

type tronRentRequesterFactory struct {
	tronifyReq  *tronify.TronRentRequester
	weidubotReq *weidubot.TronRentRequester
}

func NewTronRentRequesterFactory(
	tronifyReq *tronify.TronRentRequester,
	weidubotReq *weidubot.TronRentRequester) rent.TronRentRequesterFactory {
	return &tronRentRequesterFactory{
		tronifyReq:  tronifyReq,
		weidubotReq: weidubotReq,
	}
}

func (t *tronRentRequesterFactory) GetTronRentRequester(channel model.TronRentChannel) (rent.TronRentRequester, error) {
	switch channel {
	case model.TronRentChannelWeidubot:
		return t.weidubotReq, nil
	case model.TronRentChannelTronify:
		return t.tronifyReq, nil
	default:
		return nil, fmt.Errorf("invalid tron rent channel: %s", channel)
	}
}

type TronRentRepo struct {
	*Data
}

func NewTronRentRepo(data *Data) rent.TronRentRepo {
	return &TronRentRepo{Data: data}
}

func (t TronRentRepo) GetTronRentConfig(ctx context.Context) (*model.TronRentConfig, error) {
	cfgs := []*model.TronRentConfig{}
	if err := t.DB(ctx).Model(&model.TronRentConfig{}).
		Where("enable=?", true).
		Find(&cfgs).Error; err != nil {
		return nil, err
	}
	if len(cfgs) != 1 {
		return nil, fmt.Errorf("get enable tron rent config err: %d", len(cfgs))
	}
	return cfgs[0], nil
}

func (t TronRentRepo) CreateTronRentRecord(ctx context.Context, record *model.TronRentRecord) error {
	return t.DB(ctx).Create(record).Error
}

func (t TronRentRepo) UpdateTronRentRecord(ctx context.Context, record *model.TronRentRecord) error {
	return t.DB(ctx).Model(&model.TronRentRecord{}).Where("id=?", record.ID).Updates(map[string]any{
		"tx_hash":                record.TxHash,
		"status":                 record.Status,
		"err_msg":                record.ErrMsg,
		"signed_data":            record.SignedData,
		"upload_hash_reply_data": record.UploadHashReplyData,
		"channel_order_id":       record.ChannelOrderId,
		"actual_channel_price":   record.ActualChannelPrice,
	}).Error
}

func (t TronRentRepo) GetTronRentRecordByOrderId(ctx context.Context, orderId string) (*model.TronRentRecord, error) {
	var record model.TronRentRecord
	if err := t.DB(ctx).Model(&model.TronRentRecord{}).Where("order_id=?", orderId).Take(&record).Error; err != nil {
		return nil, err
	}
	return &record, nil
}

func (t TronRentRepo) GetTronRentRecordByDelegateHash(ctx context.Context, hash string) (*model.TronRentRecord, error) {
	var record model.TronRentRecord
	if err := t.DB(ctx).Model(&model.TronRentRecord{}).
		Where(datatypes.JSONArrayQuery("upload_hash_reply_data").Contains(hash)).
		Last(&record).Error; err != nil {
		return nil, err
	}
	return &record, nil
}
