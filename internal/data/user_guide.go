package data

import (
	"byd_wallet/common/lang"
	"byd_wallet/internal/biz"
	"byd_wallet/model"
	"context"
)

// NewUserGuideRepo 创建用户指南仓库
func NewUserGuideRepo(data *Data) biz.UserGuideRepo {
	return &userGuideRepo{
		data,
	}
}

type userGuideRepo struct {
	*Data
}

func (r *userGuideRepo) ListUserGuideContent(ctx context.Context, id uint) ([]*model.UserGuideContent, error) {
	var list []*model.UserGuideContent
	if err := r.DB(ctx).Model(&model.UserGuideContent{}).Where("user_guide_id=?", id).Order("id ASC").Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

// ListUserGuideCategory 获取用户指南分类列表
func (r *userGuideRepo) ListUserGuideCategory(ctx context.Context) ([]*model.UserGuideCategory, error) {
	var categories []*model.UserGuideCategory
	language := lang.FromContext(ctx)
	err := r.DB(ctx).Model(&model.UserGuideCategory{}).
		Where("display = ?", true).
		Where("language = ?", language).
		Order("id ASC").Find(&categories).Error

	return categories, err
}

// ListUserGuide 根据分类查询用户指南列表
func (r *userGuideRepo) ListUserGuide(ctx context.Context, categoryID uint) ([]*model.UserGuide, error) {
	var guides []*model.UserGuide
	err := r.DB(ctx).Model(&model.UserGuide{}).
		Where("display = ?", true).
		Where("category_id = ?", categoryID).
		Order("id ASC").Find(&guides).Error

	return guides, err
}

// SearchUserGuide 模糊搜索用户指南
func (r *userGuideRepo) SearchUserGuide(ctx context.Context, keyword string) ([]*model.UserGuide, error) {
	var guides []*model.UserGuide
	language := lang.FromContext(ctx)

	query := r.DB(ctx).Model(&model.UserGuide{}).
		Where("display = ?", true).
		Where("language = ?", language).
		Where("(title LIKE ? OR summary LIKE ?)", "%"+keyword+"%", "%"+keyword+"%")

	err := query.Order("id ASC").Find(&guides).Error
	return guides, err
}
