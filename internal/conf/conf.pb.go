// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: internal/conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bootstrap struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Server        *Server                `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Data          *Data                  `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	mi := &file_internal_conf_conf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Bootstrap) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type Server struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Http          *Server_HTTP           `protobuf:"bytes,1,opt,name=http,proto3" json:"http,omitempty"`
	Grpc          *Server_GRPC           `protobuf:"bytes,2,opt,name=grpc,proto3" json:"grpc,omitempty"`
	Mq            *Server_MQ             `protobuf:"bytes,3,opt,name=mq,proto3" json:"mq,omitempty"`
	Syncer        *Server_Syncer         `protobuf:"bytes,4,opt,name=syncer,proto3" json:"syncer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server) Reset() {
	*x = Server{}
	mi := &file_internal_conf_conf_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Server) GetHttp() *Server_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *Server) GetGrpc() *Server_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

func (x *Server) GetMq() *Server_MQ {
	if x != nil {
		return x.Mq
	}
	return nil
}

func (x *Server) GetSyncer() *Server_Syncer {
	if x != nil {
		return x.Syncer
	}
	return nil
}

type Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Database      *Data_Database         `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	Redis         *Data_Redis            `protobuf:"bytes,2,opt,name=redis,proto3" json:"redis,omitempty"`
	Kafka         *Data_Kafka            `protobuf:"bytes,3,opt,name=kafka,proto3" json:"kafka,omitempty"`
	Adminjwt      *Data_Jwt              `protobuf:"bytes,4,opt,name=adminjwt,proto3" json:"adminjwt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data) Reset() {
	*x = Data{}
	mi := &file_internal_conf_conf_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2}
}

func (x *Data) GetDatabase() *Data_Database {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *Data) GetRedis() *Data_Redis {
	if x != nil {
		return x.Redis
	}
	return nil
}

func (x *Data) GetKafka() *Data_Kafka {
	if x != nil {
		return x.Kafka
	}
	return nil
}

func (x *Data) GetAdminjwt() *Data_Jwt {
	if x != nil {
		return x.Adminjwt
	}
	return nil
}

type Server_HTTP struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server_HTTP) Reset() {
	*x = Server_HTTP{}
	mi := &file_internal_conf_conf_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_HTTP) ProtoMessage() {}

func (x *Server_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_HTTP.ProtoReflect.Descriptor instead.
func (*Server_HTTP) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Server_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_HTTP) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_GRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server_GRPC) Reset() {
	*x = Server_GRPC{}
	mi := &file_internal_conf_conf_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_GRPC) ProtoMessage() {}

func (x *Server_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_GRPC.ProtoReflect.Descriptor instead.
func (*Server_GRPC) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Server_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_GRPC) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_MQ struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	MqType            string                 `protobuf:"bytes,1,opt,name=mq_type,json=mqType,proto3" json:"mq_type,omitempty"`
	Addrs             []string               `protobuf:"bytes,2,rep,name=addrs,proto3" json:"addrs,omitempty"`
	TokenTopic        string                 `protobuf:"bytes,3,opt,name=token_topic,json=tokenTopic,proto3" json:"token_topic,omitempty"`
	HoldNewTokenTopic string                 `protobuf:"bytes,4,opt,name=hold_new_token_topic,json=holdNewTokenTopic,proto3" json:"hold_new_token_topic,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Server_MQ) Reset() {
	*x = Server_MQ{}
	mi := &file_internal_conf_conf_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_MQ) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_MQ) ProtoMessage() {}

func (x *Server_MQ) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_MQ.ProtoReflect.Descriptor instead.
func (*Server_MQ) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 2}
}

func (x *Server_MQ) GetMqType() string {
	if x != nil {
		return x.MqType
	}
	return ""
}

func (x *Server_MQ) GetAddrs() []string {
	if x != nil {
		return x.Addrs
	}
	return nil
}

func (x *Server_MQ) GetTokenTopic() string {
	if x != nil {
		return x.TokenTopic
	}
	return ""
}

func (x *Server_MQ) GetHoldNewTokenTopic() string {
	if x != nil {
		return x.HoldNewTokenTopic
	}
	return ""
}

type Server_Syncer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChainIndex    int64                  `protobuf:"varint,1,opt,name=chain_index,json=chainIndex,proto3" json:"chain_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server_Syncer) Reset() {
	*x = Server_Syncer{}
	mi := &file_internal_conf_conf_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_Syncer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_Syncer) ProtoMessage() {}

func (x *Server_Syncer) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_Syncer.ProtoReflect.Descriptor instead.
func (*Server_Syncer) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{1, 3}
}

func (x *Server_Syncer) GetChainIndex() int64 {
	if x != nil {
		return x.ChainIndex
	}
	return 0
}

type Data_Database struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Driver          string                 `protobuf:"bytes,1,opt,name=driver,proto3" json:"driver,omitempty"`
	DbUser          string                 `protobuf:"bytes,2,opt,name=db_user,json=dbUser,proto3" json:"db_user,omitempty"`
	DbPassword      string                 `protobuf:"bytes,3,opt,name=db_password,json=dbPassword,proto3" json:"db_password,omitempty"`
	DbHost          string                 `protobuf:"bytes,4,opt,name=db_host,json=dbHost,proto3" json:"db_host,omitempty"`
	DbPort          int64                  `protobuf:"varint,5,opt,name=db_port,json=dbPort,proto3" json:"db_port,omitempty"`
	DbName          string                 `protobuf:"bytes,6,opt,name=db_name,json=dbName,proto3" json:"db_name,omitempty"`
	DryRun          bool                   `protobuf:"varint,7,opt,name=dry_run,json=dryRun,proto3" json:"dry_run,omitempty"`
	MaxIdleConns    int64                  `protobuf:"varint,8,opt,name=max_idle_conns,json=maxIdleConns,proto3" json:"max_idle_conns,omitempty"`
	MaxOpenConns    int64                  `protobuf:"varint,9,opt,name=max_open_conns,json=maxOpenConns,proto3" json:"max_open_conns,omitempty"`
	ConnMaxLifeTime string                 `protobuf:"bytes,10,opt,name=conn_max_life_time,json=connMaxLifeTime,proto3" json:"conn_max_life_time,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Data_Database) Reset() {
	*x = Data_Database{}
	mi := &file_internal_conf_conf_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Database) ProtoMessage() {}

func (x *Data_Database) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Database.ProtoReflect.Descriptor instead.
func (*Data_Database) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Data_Database) GetDriver() string {
	if x != nil {
		return x.Driver
	}
	return ""
}

func (x *Data_Database) GetDbUser() string {
	if x != nil {
		return x.DbUser
	}
	return ""
}

func (x *Data_Database) GetDbPassword() string {
	if x != nil {
		return x.DbPassword
	}
	return ""
}

func (x *Data_Database) GetDbHost() string {
	if x != nil {
		return x.DbHost
	}
	return ""
}

func (x *Data_Database) GetDbPort() int64 {
	if x != nil {
		return x.DbPort
	}
	return 0
}

func (x *Data_Database) GetDbName() string {
	if x != nil {
		return x.DbName
	}
	return ""
}

func (x *Data_Database) GetDryRun() bool {
	if x != nil {
		return x.DryRun
	}
	return false
}

func (x *Data_Database) GetMaxIdleConns() int64 {
	if x != nil {
		return x.MaxIdleConns
	}
	return 0
}

func (x *Data_Database) GetMaxOpenConns() int64 {
	if x != nil {
		return x.MaxOpenConns
	}
	return 0
}

func (x *Data_Database) GetConnMaxLifeTime() string {
	if x != nil {
		return x.ConnMaxLifeTime
	}
	return ""
}

type Data_Redis struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addrs         []string               `protobuf:"bytes,2,rep,name=addrs,proto3" json:"addrs,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	Db            int32                  `protobuf:"varint,5,opt,name=db,proto3" json:"db,omitempty"`
	DialTimeout   *durationpb.Duration   `protobuf:"bytes,6,opt,name=dial_timeout,json=dialTimeout,proto3" json:"dial_timeout,omitempty"`
	ReadTimeout   *durationpb.Duration   `protobuf:"bytes,7,opt,name=read_timeout,json=readTimeout,proto3" json:"read_timeout,omitempty"`
	WriteTimeout  *durationpb.Duration   `protobuf:"bytes,8,opt,name=write_timeout,json=writeTimeout,proto3" json:"write_timeout,omitempty"`
	IsClusterMode bool                   `protobuf:"varint,9,opt,name=is_cluster_mode,json=isClusterMode,proto3" json:"is_cluster_mode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_Redis) Reset() {
	*x = Data_Redis{}
	mi := &file_internal_conf_conf_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Redis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Redis) ProtoMessage() {}

func (x *Data_Redis) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Redis.ProtoReflect.Descriptor instead.
func (*Data_Redis) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Data_Redis) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Data_Redis) GetAddrs() []string {
	if x != nil {
		return x.Addrs
	}
	return nil
}

func (x *Data_Redis) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Data_Redis) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Data_Redis) GetDb() int32 {
	if x != nil {
		return x.Db
	}
	return 0
}

func (x *Data_Redis) GetDialTimeout() *durationpb.Duration {
	if x != nil {
		return x.DialTimeout
	}
	return nil
}

func (x *Data_Redis) GetReadTimeout() *durationpb.Duration {
	if x != nil {
		return x.ReadTimeout
	}
	return nil
}

func (x *Data_Redis) GetWriteTimeout() *durationpb.Duration {
	if x != nil {
		return x.WriteTimeout
	}
	return nil
}

func (x *Data_Redis) GetIsClusterMode() bool {
	if x != nil {
		return x.IsClusterMode
	}
	return false
}

type Data_Kafka struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Brokers           []string               `protobuf:"bytes,1,rep,name=brokers,proto3" json:"brokers,omitempty"`
	TokenTopic        string                 `protobuf:"bytes,2,opt,name=token_topic,json=tokenTopic,proto3" json:"token_topic,omitempty"`
	HoldNewTokenTopic string                 `protobuf:"bytes,3,opt,name=hold_new_token_topic,json=holdNewTokenTopic,proto3" json:"hold_new_token_topic,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Data_Kafka) Reset() {
	*x = Data_Kafka{}
	mi := &file_internal_conf_conf_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Kafka) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Kafka) ProtoMessage() {}

func (x *Data_Kafka) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Kafka.ProtoReflect.Descriptor instead.
func (*Data_Kafka) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 2}
}

func (x *Data_Kafka) GetBrokers() []string {
	if x != nil {
		return x.Brokers
	}
	return nil
}

func (x *Data_Kafka) GetTokenTopic() string {
	if x != nil {
		return x.TokenTopic
	}
	return ""
}

func (x *Data_Kafka) GetHoldNewTokenTopic() string {
	if x != nil {
		return x.HoldNewTokenTopic
	}
	return ""
}

type Data_Jwt struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ed25519 ex. https://github.com/golang-jwt/jwt/blob/main/test/ed25519-private.pem
	// generate command: openssl genpkey -algorithm ed25519
	PrivKey string `protobuf:"bytes,1,opt,name=priv_key,json=privKey,proto3" json:"priv_key,omitempty"`
	// at least 1 hour
	Expires       string `protobuf:"bytes,2,opt,name=expires,proto3" json:"expires,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_Jwt) Reset() {
	*x = Data_Jwt{}
	mi := &file_internal_conf_conf_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Jwt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Jwt) ProtoMessage() {}

func (x *Data_Jwt) ProtoReflect() protoreflect.Message {
	mi := &file_internal_conf_conf_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Jwt.ProtoReflect.Descriptor instead.
func (*Data_Jwt) Descriptor() ([]byte, []int) {
	return file_internal_conf_conf_proto_rawDescGZIP(), []int{2, 3}
}

func (x *Data_Jwt) GetPrivKey() string {
	if x != nil {
		return x.PrivKey
	}
	return ""
}

func (x *Data_Jwt) GetExpires() string {
	if x != nil {
		return x.Expires
	}
	return ""
}

var File_internal_conf_conf_proto protoreflect.FileDescriptor

const file_internal_conf_conf_proto_rawDesc = "" +
	"\n" +
	"\x18internal/conf/conf.proto\x12\n" +
	"kratos.api\x1a\x1egoogle/protobuf/duration.proto\"]\n" +
	"\tBootstrap\x12*\n" +
	"\x06server\x18\x01 \x01(\v2\x12.kratos.api.ServerR\x06server\x12$\n" +
	"\x04data\x18\x02 \x01(\v2\x10.kratos.api.DataR\x04data\"\xc5\x04\n" +
	"\x06Server\x12+\n" +
	"\x04http\x18\x01 \x01(\v2\x17.kratos.api.Server.HTTPR\x04http\x12+\n" +
	"\x04grpc\x18\x02 \x01(\v2\x17.kratos.api.Server.GRPCR\x04grpc\x12%\n" +
	"\x02mq\x18\x03 \x01(\v2\x15.kratos.api.Server.MQR\x02mq\x121\n" +
	"\x06syncer\x18\x04 \x01(\v2\x19.kratos.api.Server.SyncerR\x06syncer\x1ai\n" +
	"\x04HTTP\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x1ai\n" +
	"\x04GRPC\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x1a\x85\x01\n" +
	"\x02MQ\x12\x17\n" +
	"\amq_type\x18\x01 \x01(\tR\x06mqType\x12\x14\n" +
	"\x05addrs\x18\x02 \x03(\tR\x05addrs\x12\x1f\n" +
	"\vtoken_topic\x18\x03 \x01(\tR\n" +
	"tokenTopic\x12/\n" +
	"\x14hold_new_token_topic\x18\x04 \x01(\tR\x11holdNewTokenTopic\x1a)\n" +
	"\x06Syncer\x12\x1f\n" +
	"\vchain_index\x18\x01 \x01(\x03R\n" +
	"chainIndex\"\x9e\b\n" +
	"\x04Data\x125\n" +
	"\bdatabase\x18\x01 \x01(\v2\x19.kratos.api.Data.DatabaseR\bdatabase\x12,\n" +
	"\x05redis\x18\x02 \x01(\v2\x16.kratos.api.Data.RedisR\x05redis\x12,\n" +
	"\x05kafka\x18\x03 \x01(\v2\x16.kratos.api.Data.KafkaR\x05kafka\x120\n" +
	"\badminjwt\x18\x04 \x01(\v2\x14.kratos.api.Data.JwtR\badminjwt\x1a\xb9\x02\n" +
	"\bDatabase\x12\x16\n" +
	"\x06driver\x18\x01 \x01(\tR\x06driver\x12\x17\n" +
	"\adb_user\x18\x02 \x01(\tR\x06dbUser\x12\x1f\n" +
	"\vdb_password\x18\x03 \x01(\tR\n" +
	"dbPassword\x12\x17\n" +
	"\adb_host\x18\x04 \x01(\tR\x06dbHost\x12\x17\n" +
	"\adb_port\x18\x05 \x01(\x03R\x06dbPort\x12\x17\n" +
	"\adb_name\x18\x06 \x01(\tR\x06dbName\x12\x17\n" +
	"\adry_run\x18\a \x01(\bR\x06dryRun\x12$\n" +
	"\x0emax_idle_conns\x18\b \x01(\x03R\fmaxIdleConns\x12$\n" +
	"\x0emax_open_conns\x18\t \x01(\x03R\fmaxOpenConns\x12+\n" +
	"\x12conn_max_life_time\x18\n" +
	" \x01(\tR\x0fconnMaxLifeTime\x1a\xe3\x02\n" +
	"\x05Redis\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x14\n" +
	"\x05addrs\x18\x02 \x03(\tR\x05addrs\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\x12\x0e\n" +
	"\x02db\x18\x05 \x01(\x05R\x02db\x12<\n" +
	"\fdial_timeout\x18\x06 \x01(\v2\x19.google.protobuf.DurationR\vdialTimeout\x12<\n" +
	"\fread_timeout\x18\a \x01(\v2\x19.google.protobuf.DurationR\vreadTimeout\x12>\n" +
	"\rwrite_timeout\x18\b \x01(\v2\x19.google.protobuf.DurationR\fwriteTimeout\x12&\n" +
	"\x0fis_cluster_mode\x18\t \x01(\bR\risClusterMode\x1as\n" +
	"\x05Kafka\x12\x18\n" +
	"\abrokers\x18\x01 \x03(\tR\abrokers\x12\x1f\n" +
	"\vtoken_topic\x18\x02 \x01(\tR\n" +
	"tokenTopic\x12/\n" +
	"\x14hold_new_token_topic\x18\x03 \x01(\tR\x11holdNewTokenTopic\x1a:\n" +
	"\x03Jwt\x12\x19\n" +
	"\bpriv_key\x18\x01 \x01(\tR\aprivKey\x12\x18\n" +
	"\aexpires\x18\x02 \x01(\tR\aexpiresB\x9b\x01\n" +
	"\x0ecom.kratos.apiB\tConfProtoP\x01Z5github.com/go-kratos/kratos-layout/internal/conf;conf\xa2\x02\x03KAX\xaa\x02\n" +
	"Kratos.Api\xca\x02\n" +
	"Kratos\\Api\xe2\x02\x16Kratos\\Api\\GPBMetadata\xea\x02\vKratos::Apib\x06proto3"

var (
	file_internal_conf_conf_proto_rawDescOnce sync.Once
	file_internal_conf_conf_proto_rawDescData []byte
)

func file_internal_conf_conf_proto_rawDescGZIP() []byte {
	file_internal_conf_conf_proto_rawDescOnce.Do(func() {
		file_internal_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_internal_conf_conf_proto_rawDesc), len(file_internal_conf_conf_proto_rawDesc)))
	})
	return file_internal_conf_conf_proto_rawDescData
}

var file_internal_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_internal_conf_conf_proto_goTypes = []any{
	(*Bootstrap)(nil),           // 0: kratos.api.Bootstrap
	(*Server)(nil),              // 1: kratos.api.Server
	(*Data)(nil),                // 2: kratos.api.Data
	(*Server_HTTP)(nil),         // 3: kratos.api.Server.HTTP
	(*Server_GRPC)(nil),         // 4: kratos.api.Server.GRPC
	(*Server_MQ)(nil),           // 5: kratos.api.Server.MQ
	(*Server_Syncer)(nil),       // 6: kratos.api.Server.Syncer
	(*Data_Database)(nil),       // 7: kratos.api.Data.Database
	(*Data_Redis)(nil),          // 8: kratos.api.Data.Redis
	(*Data_Kafka)(nil),          // 9: kratos.api.Data.Kafka
	(*Data_Jwt)(nil),            // 10: kratos.api.Data.Jwt
	(*durationpb.Duration)(nil), // 11: google.protobuf.Duration
}
var file_internal_conf_conf_proto_depIdxs = []int32{
	1,  // 0: kratos.api.Bootstrap.server:type_name -> kratos.api.Server
	2,  // 1: kratos.api.Bootstrap.data:type_name -> kratos.api.Data
	3,  // 2: kratos.api.Server.http:type_name -> kratos.api.Server.HTTP
	4,  // 3: kratos.api.Server.grpc:type_name -> kratos.api.Server.GRPC
	5,  // 4: kratos.api.Server.mq:type_name -> kratos.api.Server.MQ
	6,  // 5: kratos.api.Server.syncer:type_name -> kratos.api.Server.Syncer
	7,  // 6: kratos.api.Data.database:type_name -> kratos.api.Data.Database
	8,  // 7: kratos.api.Data.redis:type_name -> kratos.api.Data.Redis
	9,  // 8: kratos.api.Data.kafka:type_name -> kratos.api.Data.Kafka
	10, // 9: kratos.api.Data.adminjwt:type_name -> kratos.api.Data.Jwt
	11, // 10: kratos.api.Server.HTTP.timeout:type_name -> google.protobuf.Duration
	11, // 11: kratos.api.Server.GRPC.timeout:type_name -> google.protobuf.Duration
	11, // 12: kratos.api.Data.Redis.dial_timeout:type_name -> google.protobuf.Duration
	11, // 13: kratos.api.Data.Redis.read_timeout:type_name -> google.protobuf.Duration
	11, // 14: kratos.api.Data.Redis.write_timeout:type_name -> google.protobuf.Duration
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_internal_conf_conf_proto_init() }
func file_internal_conf_conf_proto_init() {
	if File_internal_conf_conf_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_internal_conf_conf_proto_rawDesc), len(file_internal_conf_conf_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_conf_conf_proto_goTypes,
		DependencyIndexes: file_internal_conf_conf_proto_depIdxs,
		MessageInfos:      file_internal_conf_conf_proto_msgTypes,
	}.Build()
	File_internal_conf_conf_proto = out.File
	file_internal_conf_conf_proto_goTypes = nil
	file_internal_conf_conf_proto_depIdxs = nil
}
