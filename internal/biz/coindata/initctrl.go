package coindata

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/base"
	"byd_wallet/internal/thirdapi/coingecko"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"fmt"
	"strings"
	"sync"

	"github.com/go-kratos/kratos/v2/log"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type TokenContractAPI interface {
	GetTokenContract(ctx context.Context, chainIndex int64, address string) (*TokenContract, error)
}

type InitCtrl struct {
	log *log.Helper

	api   CoinDataThirdAPI
	db    *gorm.DB
	s3    base.S3Repo
	tcApi TokenContractAPI

	coinPlatform2ChainIndex map[string]int64
	chainIndex2TokenType    map[int64]string
	chainIndex2ChainID      map[int64]string
}

func NewInitCtrl(logger log.Logger, api CoinDataThirdAPI,
	db *gorm.DB,
	s3 base.S3<PERSON><PERSON><PERSON>,
	tcApi <PERSON>ontractAP<PERSON>) *InitCtrl {
	var coinPlatform2ChainIndex = map[string]int64{
		"bitcoin":             constant.BtcChainIndex,
		"ethereum":            constant.EthChainIndex,
		"binance-smart-chain": constant.BscChainIndex,
		"polygon-pos":         constant.PolChainIndex,
		"arbitrum-one":        constant.ArbChainIndex,
		"optimistic-ethereum": constant.OptimismChainIndex,
		"base":                constant.BaseChainIndex,
		"solana":              constant.SolChainIndex,
		"tron":                constant.TronChainIndex,
	}

	var chainIndex2TokenType = map[int64]string{
		constant.EthChainIndex:      constant.ERC20TokenType,
		constant.BscChainIndex:      constant.ERC20TokenType,
		constant.PolChainIndex:      constant.ERC20TokenType,
		constant.ArbChainIndex:      constant.ERC20TokenType,
		constant.OptimismChainIndex: constant.ERC20TokenType,
		constant.BaseChainIndex:     constant.ERC20TokenType,
		constant.SolChainIndex:      constant.SPLTokenType,
		constant.TronChainIndex:     constant.TRC20TokenType,
	}

	var chainIndex2ChainID = map[int64]string{
		constant.EthChainIndex:      "1",
		constant.BscChainIndex:      "56",
		constant.PolChainIndex:      "137",
		constant.ArbChainIndex:      "42161",
		constant.OptimismChainIndex: "10",
		constant.BaseChainIndex:     "8453",
		constant.SolChainIndex:      "501",
		constant.TronChainIndex:     "728126428",
	}

	return &InitCtrl{
		log: log.NewHelper(logger),

		api:   api,
		db:    db,
		s3:    s3,
		tcApi: tcApi,

		coinPlatform2ChainIndex: coinPlatform2ChainIndex,
		chainIndex2TokenType:    chainIndex2TokenType,
		chainIndex2ChainID:      chainIndex2ChainID,
	}
}

func (it *InitCtrl) InitCoinInfos(ctx context.Context) error {
	coinids, err := it.api.ListCoinID()
	if err != nil {
		return fmt.Errorf("ListCoinID request fail: %w", err)
	}
	coininfos := it.toModelCoinInfos(coinids)

	it.log.Infow(log.DefaultMessageKey, "InitCoinInfos", "count", len(coinids))

	err = it.db.WithContext(ctx).Model(&model.CoinInfo{}).Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "chain_index"},
			{Name: "address"},
		},
		DoNothing: true,
		// NOTE: update platforms(coin asset platform and contract address)
	}).CreateInBatches(coininfos, 200).Error
	if err != nil {
		return fmt.Errorf("create coin infos fail: %w", err)
	}
	return nil
}

func (it *InitCtrl) toModelCoinInfos(coinids []*coingecko.CoinID) []*model.CoinInfo {
	specialCoinInfos := []*model.CoinInfo{
		{
			ChainIndex: constant.BscChainIndex,
			CoinID:     "binancecoin",
			Name:       "BNB",
			Symbol:     "bnb",
		},
		{
			ChainIndex: constant.PolChainIndex,
			CoinID:     "polygon-ecosystem-token",
			Name:       "POL (ex-MATIC)",
			Symbol:     "pol",
		},
		{
			ChainIndex: constant.BaseChainIndex,
			CoinID:     "ethereum",
			Name:       "base",
			Symbol:     "eth",
		},
		{
			ChainIndex: constant.ArbChainIndex,
			CoinID:     "ethereum",
			Name:       "arbitrum",
			Symbol:     "eth",
		},
		{
			ChainIndex: constant.OptimismChainIndex,
			CoinID:     "ethereum",
			Name:       "optimism",
			Symbol:     "eth",
		},
	}

	result := make([]*model.CoinInfo, 0, len(coinids)+len(specialCoinInfos))
	result = append(result, specialCoinInfos...)

	for _, v := range coinids {
		if len(v.Platforms) == 0 {
			// native
			chainIndex, ok := it.coinPlatform2ChainIndex[v.ID]
			if !ok {
				continue
			}
			result = append(result, &model.CoinInfo{
				CoinID:     v.ID,
				Name:       v.Name,
				Symbol:     v.Symbol,
				ChainIndex: chainIndex,
			})
			continue
		}

		// tokens
		for p, address := range v.Platforms {
			chainIndex, ok := it.coinPlatform2ChainIndex[p]
			if !ok {
				continue
			}
			if address == "" {
				continue
			}
			result = append(result, &model.CoinInfo{
				CoinID:     v.ID,
				Name:       v.Name,
				Symbol:     v.Symbol,
				ChainIndex: chainIndex,
				Address:    address,
			})
		}
	}
	return result
}

func (s *InitCtrl) InitCoinLogos(ctx context.Context) error {
	var coinIDs []string
	sql := fmt.Sprintf("select distinct(coin_id) from %s where coin_id not in (select coin_id from %s)",
		(&model.CoinInfo{}).TableName(),
		(&model.CoinLogo{}).TableName())
	err := s.db.WithContext(ctx).Raw(sql).Scan(&coinIDs).Error
	if err != nil {
		return fmt.Errorf("query coin infos fail: %w", err)
	}

	s.log.Infow(log.DefaultMessageKey, "InitCoinLogos", "count", len(coinIDs))

	if len(coinIDs) == 0 {
		s.log.Warn("no coin logos need to be initialized")
		return nil
	}

	data, err := s.api.ListCoinLogosByIDs(coinIDs)
	if err != nil {
		return fmt.Errorf("ListCoinLogosByID request fail: %w", err)
	}

	logos := make([]*model.CoinLogo, 0, len(data))
	for _, v := range data {
		if v.Image == "" {
			s.log.Error("coin logo image is empty", zap.String("coinID", v.ID))
			continue
		}

		logos = append(logos, &model.CoinLogo{
			CoinID: v.ID,
			Image:  v.Image,
		})
	}

	err = s.db.WithContext(ctx).Model(&model.CoinLogo{}).Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "coin_id"},
		},
		DoNothing: true,
	}).CreateInBatches(logos, 200).Error
	if err != nil {
		return fmt.Errorf("create coin logos fail: %w", err)
	}
	return nil
}

func (s *InitCtrl) InitTokenAssets(ctx context.Context) error {
	var coinInfos []*model.CoinInfo
	err := s.db.WithContext(ctx).
		Raw(fmt.Sprintf("SELECT * FROM %s ci WHERE NOT EXISTS (SELECT 1 FROM %s ta WHERE ta.chain_index = ci.chain_index AND ta.address = ci.address)",
			(&model.CoinInfo{}).TableName(),
			(&model.TokenAsset{}).TableName())).
		Scan(&coinInfos).Error
	if err != nil {
		return fmt.Errorf("query coin info: %w", err)
	}

	s.log.Infow(log.DefaultMessageKey, "InitTokenAssets", "count", len(coinInfos))

	tokenAssets := make([]*model.TokenAsset, 0, len(coinInfos))

	// native
	tokenAssets = append(tokenAssets, []*model.TokenAsset{
		{
			ChainIndex: constant.BtcChainIndex,
			Symbol:     "BTC",
			Name:       "Bitcoin",
			Decimals:   8,
			TokenType:  constant.NativeTokenType,
			IsDisplay:  true,
			CoinID:     "bitcoin",
		},
		{
			ChainIndex: constant.EthChainIndex,
			ChainId:    s.chainIndex2ChainID[constant.EthChainIndex],
			Symbol:     "ETH",
			Name:       "Ethereum",
			Decimals:   18,
			TokenType:  constant.NativeTokenType,
			IsDisplay:  true,
			CoinID:     "ethereum",
		},
		{
			ChainIndex: constant.BaseChainIndex,
			ChainId:    s.chainIndex2ChainID[constant.BaseChainIndex],
			Symbol:     "ETH",
			Name:       "Base",
			Decimals:   18,
			TokenType:  constant.NativeTokenType,
			IsDisplay:  true,
			CoinID:     "ethereum",
		},
		{
			ChainIndex: constant.ArbChainIndex,
			ChainId:    s.chainIndex2ChainID[constant.ArbChainIndex],
			Symbol:     "ETH",
			Name:       "Arbitrum",
			Decimals:   18,
			TokenType:  constant.NativeTokenType,
			IsDisplay:  true,
			CoinID:     "ethereum",
		},
		{
			ChainIndex: constant.OptimismChainIndex,
			ChainId:    s.chainIndex2ChainID[constant.OptimismChainIndex],
			Symbol:     "ETH",
			Name:       "Optimism",
			Decimals:   18,
			TokenType:  constant.NativeTokenType,
			IsDisplay:  true,
			CoinID:     "ethereum",
		},
		{
			ChainIndex: constant.TronChainIndex,
			ChainId:    s.chainIndex2ChainID[constant.TronChainIndex],
			Symbol:     "TRX",
			Name:       "Tron",
			Decimals:   6,
			TokenType:  constant.NativeTokenType,
			IsDisplay:  true,
			CoinID:     "tron",
		},
		{
			ChainIndex: constant.PolChainIndex,
			ChainId:    s.chainIndex2ChainID[constant.PolChainIndex],
			Symbol:     "POL",
			Name:       "Polygon",
			Decimals:   18,
			TokenType:  constant.NativeTokenType,
			IsDisplay:  true,
			CoinID:     "polygon-pos",
		},
		{
			ChainIndex: constant.BscChainIndex,
			ChainId:    s.chainIndex2ChainID[constant.BscChainIndex],
			Symbol:     "BNB",
			Name:       "Binance",
			Decimals:   18,
			TokenType:  constant.NativeTokenType,
			IsDisplay:  true,
			CoinID:     "binance-smart-chain",
		},
		{
			ChainIndex: constant.SolChainIndex,
			ChainId:    s.chainIndex2ChainID[constant.SolChainIndex],
			Symbol:     "SOL",
			Name:       "Solana",
			Decimals:   9,
			TokenType:  constant.NativeTokenType,
			IsDisplay:  true,
			CoinID:     "solana",
		},
	}...)

	// token
	sw := sync.WaitGroup{}
	const splitSize = 1000
	sender := make(chan *model.TokenAsset, splitSize)

	cCtx, cancel := context.WithCancel(context.Background())
	go func(ctx context.Context) {
		for {
			select {
			case <-ctx.Done():
				return
			case ta := <-sender:
				tokenAssets = append(tokenAssets, ta)
				if len(tokenAssets)%1000 == 0 {
					s.log.Infof("InitTokenAssets: proccessed count: %d", len(tokenAssets))
				}
			}
		}
	}(cCtx)

	for i := 0; i < len(coinInfos); i += splitSize {
		s.log.Infof("InitTokenAssets: token split: %d", i)
		sw.Add(1)
		go func(i int) {
			defer sw.Done()
			for j := i; j < i+splitSize && j < len(coinInfos); j++ {
				v := coinInfos[j]
				if v.Address == "" {
					// skip native
					continue
				}

				tokenType, ok := s.chainIndex2TokenType[v.ChainIndex]
				if !ok {
					s.log.Errorf("chainIndex2TokenType: %d", v.ChainIndex)
					continue
				}

				chainID, ok := s.chainIndex2ChainID[v.ChainIndex]
				if !ok {
					s.log.Errorf("chainIndex2ChainID: %d", v.ChainIndex)
					continue
				}
				tc, err := s.tcApi.GetTokenContract(ctx, v.ChainIndex, v.Address)
				if err != nil {
					s.log.Errorf("GetTokenContract: %s: %d: %s", err.Error(), v.ChainIndex, v.Address)
					continue
				}

				sender <- &model.TokenAsset{
					ChainIndex:  v.ChainIndex,
					ChainId:     chainID,
					Address:     v.Address,
					Symbol:      model.ConvValidSymbol(v.Symbol),
					Name:        v.Name,
					Decimals:    tc.Decimals,
					TokenType:   tokenType,
					TotalSupply: tc.TotalSupply,
					IsDisplay:   true,
					CoinID:      v.CoinID,
				}
			}
		}(i)
	}

	sw.Wait()
	cancel()

	err = s.db.WithContext(ctx).Model(&model.TokenAsset{}).Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "chain_index"},
			{Name: "address"},
		},
		DoNothing: true,
	}).CreateInBatches(tokenAssets, 200).Error
	if err != nil {
		err = fmt.Errorf("create token assets: %w", err)
	}
	return err
}

func (s *InitCtrl) InitTokenAssetsLogo(ctx context.Context) error {
	var coinInfos []*struct {
		CoinID     string
		ChainIndex int64
		Address    string
	}
	sql := "select chain_index,address,coin_id from token_assets where logo_url='' and coin_id<>''"
	err := s.db.WithContext(ctx).
		Raw(sql).
		Scan(&coinInfos).Error
	if err != nil {
		return fmt.Errorf("query coin info: %w", err)
	}

	s.log.Infof("coin logo count: %d", len(coinInfos))

	sw := sync.WaitGroup{}
	const splitSize = 1000
	imageCache := map[string]string{}
	icMtx := sync.RWMutex{}
	logoCache := map[string]string{}
	lcMtx := sync.RWMutex{}
	for i := 0; i < len(coinInfos); i += splitSize {
		s.log.Infof("InitTokenAssetsLogo: split: %d", i)
		sw.Add(1)
		go func(i int) {
			defer sw.Done()
			for j := i; j < i+splitSize && j < len(coinInfos); j++ {
				coinInfo := coinInfos[j]
				icMtx.RLock()
				imageUrl, ok := imageCache[coinInfo.CoinID]
				icMtx.RUnlock()
				if !ok {
					icMtx.Lock()
					imageUrl, ok = imageCache[coinInfo.CoinID]
					if !ok {
						err = s.db.WithContext(ctx).
							Raw("select image from coin_logos where coin_id=?", coinInfo.CoinID).
							Scan(&imageUrl).Error
						if err != nil {
							s.log.Errorf("get coin image url: %w: %s", err, coinInfo.CoinID)
							icMtx.Unlock()
							continue
						}
						imageCache[coinInfo.CoinID] = imageUrl
					}
					icMtx.Unlock()
				}
				if !strings.Contains(imageUrl, "http") {
					continue
				}

				lcMtx.RLock()
				logoUrl, ok := logoCache[coinInfo.CoinID]
				lcMtx.RUnlock()
				if !ok {
					lcMtx.Lock()
					logoUrl, ok = logoCache[coinInfo.CoinID]
					if !ok {
						s3Key := fmt.Sprintf("token/logo/%s", utils.MD5Hash(coinInfo.CoinID))
						resLogoUrl, err := s.s3.UploadImageFileByFileUrl(ctx, imageUrl, s3Key)
						if err != nil {
							s.log.Errorf("UploadFileByFileUrl: %v: %s: %s: (%d,%s)",
								err, imageUrl, s3Key, coinInfo.ChainIndex, coinInfo.Address)
							lcMtx.Unlock()
							continue
						}
						logoCache[coinInfo.CoinID] = resLogoUrl
						logoUrl = resLogoUrl
					}
					lcMtx.Unlock()
				}

				logoUrl = s.s3.ToAppAccessUrl(ctx, logoUrl)

				sqlR := s.db.WithContext(ctx).
					Model(&model.TokenAsset{}).
					Where("chain_index=?", coinInfo.ChainIndex).
					Where("address=?", coinInfo.Address).Update("logo_url", logoUrl)
				if err = sqlR.Error; err != nil {
					s.log.Errorf("update token assets logo url: %v: %s: %s: (%d,%s)",
						err, imageUrl, logoUrl, coinInfo.ChainIndex, coinInfo.Address)
					return
				}
				if sqlR.RowsAffected == 0 {
					s.log.Warnf("update token assets logo url: not hit: %s: %s: (%d,%s)",
						imageUrl, logoUrl, coinInfo.ChainIndex, coinInfo.Address)
				}
			}
		}(i)
	}
	sw.Wait()
	return nil
}
