package coindata

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/thirdapi/btc528"
	"byd_wallet/internal/thirdapi/coingecko"
	"byd_wallet/model"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type SyncerTestConfig struct {
	APIUrl       string `json:"api_url"`
	APIKey       string `json:"api_key"`
	IsLocalDebug bool   `json:"is_local_debug"`
	Socks5Proxy  string `json:"socks_5_proxy"`

	RateAPIUrl  string `json:"rate_api_url"`
	RateAPISign string `json:"rate_api_sign"`
}

var syncerTestConfig SyncerTestConfig

func init() {
	data, err := os.ReadFile("../../../tmp/CoinDataSyncerTestConfig.json")
	if err != nil {
		fmt.Println("warn: read test config json fail:" + err.Error())
		return
	}
	err = json.Unmarshal(data, &syncerTestConfig)
	if err != nil {
		panic("parse test config json fail:" + err.Error())
	}

	cfg := syncerTestConfig
	if cfg.IsLocalDebug {
		os.Setenv("HTTPS_PROXY", cfg.Socks5Proxy)
		os.Setenv("HTTP_PROXY", cfg.Socks5Proxy)
	}
}

type SyncerTestSuite struct {
	suite.Suite
	db     *gorm.DB
	rd     redis.UniversalClient
	syncer *Syncer
	ctx    context.Context

	coinInfos []*model.CoinInfo
	coinIds   []string
	tas       []*model.TokenAsset
}

func (s *SyncerTestSuite) SetupSuite() {
	cfg := syncerTestConfig
	api := coingecko.NewCoingeckoClient(cfg.APIUrl, cfg.APIKey)
	rateApi := btc528.NewBtc528Client(cfg.RateAPIUrl, cfg.RateAPISign)

	dsn := "host=127.0.0.1 user=root password=root dbname=wallet_test port=5432 sslmode=disable"
	db, _ := gorm.Open(postgres.Open(dsn), &gorm.Config{})

	rd := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})

	s.db = db
	s.rd = rd

	syncer := NewSyncer(log.DefaultLogger, api, db, rd, rateApi)
	s.syncer = syncer

	s.ctx = context.Background()

	// init db
	s.db.AutoMigrate(
		&model.CoinInfo{},
		&model.CoinLogo{},
		&model.TokenAsset{},
	)

	// init records
	nt := time.Now()
	s.coinInfos = []*model.CoinInfo{
		{
			Model: gorm.Model{
				ID:        1,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			ChainIndex: 0,
			CoinID:     "bitcoin",
			Name:       "Bitcoin",
			Symbol:     "btc",
		},
		{
			Model: gorm.Model{
				ID:        2,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "ethereum",
			Name:       "Ethereum",
			Symbol:     "eth",
			ChainIndex: 60,
		},
		{
			Model: gorm.Model{
				ID:        3,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "tron",
			Name:       "TRON",
			Symbol:     "trx",
			ChainIndex: 195,
		},
		{
			Model: gorm.Model{
				ID:        4,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "solana",
			Name:       "Solana",
			Symbol:     "sol",
			ChainIndex: 501,
		},
		{
			Model: gorm.Model{
				ID:        5,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "binancecoin",
			Name:       "BNB",
			Symbol:     "bnb",
			ChainIndex: 60,
			Address:    "******************************************",
		},
		{
			Model: gorm.Model{
				ID:        6,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "polygon-ecosystem-token",
			Name:       "POL (ex-MATIC)",
			Symbol:     "pol",
			ChainIndex: 966,
			Address:    "******************************************",
		},
		{
			Model: gorm.Model{
				ID:        7,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "polygon-ecosystem-token",
			Name:       "POL (ex-MATIC)",
			Symbol:     "pol",
			ChainIndex: 60,
			Address:    "0x455e53cbb86018ac2b8092fdcd39d8444affc3f6",
		},
		{
			Model: gorm.Model{
				ID:        8,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "base-protocol",
			Name:       "Base Protocol",
			Symbol:     "base",
			ChainIndex: 60,
			Address:    "0x07150e919b4de5fd6a63de1f9384828396f25fdc",
		},
		{
			Model: gorm.Model{
				ID:        9,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "optimism",
			Name:       "Optimism",
			Symbol:     "op",
			ChainIndex: 10000070,
			Address:    "0x4200000000000000000000000000000000000042",
		},
		{
			Model: gorm.Model{
				ID:        10,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "arbitrum",
			Name:       "Arbitrum",
			Symbol:     "arb",
			ChainIndex: 10042221,
			Address:    "0x912ce59144191c1204e64559fe8253a0e49e6548",
		},
		{
			Model: gorm.Model{
				ID:        11,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "arbitrum",
			Name:       "Arbitrum",
			Symbol:     "arb",
			ChainIndex: 10042170,
			Address:    "0xf823c3cd3cebe0a1fa952ba88dc9eef8e0bf46ad",
		},
		{
			Model: gorm.Model{
				ID:        12,
				CreatedAt: nt,
				UpdatedAt: nt,
			},
			CoinID:     "arbitrum",
			Name:       "Arbitrum",
			Symbol:     "arb",
			ChainIndex: 60,
			Address:    "0xb50721bcf8d664c30412cfbc6cf7a15145234ad1",
		},
	}

	s.coinIds = model.CoinInfos(s.coinInfos).UniqueCoinIDs()
	tas := []*model.TokenAsset{}
	for _, v := range s.coinInfos {
		tas = append(tas, &model.TokenAsset{
			ChainIndex: v.ChainIndex,
			Address:    v.Address,
		})
	}
	s.tas = tas
}

func (s *SyncerTestSuite) SetupTest() {
	for _, tb := range []string{
		(&model.CoinInfo{}).TableName(),
		(&model.CoinLogo{}).TableName(),
		(&model.TokenAsset{}).TableName(),
	} {
		s.db.Exec(fmt.Sprintf("truncate table %s", tb))
	}
	s.rd.FlushAll(context.Background())
}

func (s *SyncerTestSuite) TestSyncCoinOHLCs() {
	if !s.NoError(s.db.Model(&model.TokenAsset{}).CreateInBatches(s.tas, 200).Error) {
		return
	}
	if !s.NoError(s.db.Model(&model.CoinInfo{}).CreateInBatches(s.coinInfos, 200).Error) {
		return
	}

	currency := constant.CurrencyUSD
	s.syncer.SyncCoinOHLCs(s.ctx)

	data, err := s.rd.HGetAll(context.Background(), model.CoinOHLCsCacheKey).Result()
	if !s.NoError(err) {
		return
	}

	if !s.Len(data, len(s.coinIds)) {
		return
	}

	for _, v := range data {
		vv := []*model.CoinOHLC{}
		if !s.NoError(json.Unmarshal([]byte(v), &vv)) {
			return
		}
		s.Len(vv, 24)
		for _, ohlc := range vv {
			s.Equal(currency, ohlc.Currency)
			s.Equal("hourly", ohlc.Interval)
			s.True(ohlc.LastUpdatedAt > 0)
			s.True(ohlc.Open.IsPositive())
			s.True(ohlc.High.IsPositive())
			s.True(ohlc.Low.IsPositive())
			s.True(ohlc.Close.IsPositive())
		}
	}
}

func (s *SyncerTestSuite) TestSyncCoinMarketData() {
	if !s.NoError(s.db.Model(&model.TokenAsset{}).CreateInBatches(s.tas, 200).Error) {
		return
	}
	if !s.NoError(s.db.Model(&model.CoinInfo{}).CreateInBatches(s.coinInfos, 200).Error) {
		return
	}

	currency := constant.CurrencyUSD
	s.syncer.SyncCoinMarketDataByPopular(s.ctx)

	data, err := s.rd.HGetAll(context.Background(), model.CoinMarketDataPopularCacheKey).Result()
	if !s.NoError(err) {
		return
	}

	if !s.Len(data, len(s.coinIds)) {
		return
	}

	for _, v := range data {
		vv := &model.CoinMarketData{}
		if !s.NoError(json.Unmarshal([]byte(v), &vv)) {
			return
		}
		s.Contains(s.coinIds, vv.CoinID)
		s.True(vv.Price.IsPositive())
		s.Equal(currency, vv.Currency)
		s.True(vv.CirculatingSupply.IsPositive())
		s.NotEmpty(vv.LastUpdatedAtStr)
		s.True(vv.LastUpdatedAt > 0)
		s.False(vv.PriceChangePercentage24h.IsZero())
		s.True(vv.TradingVolume24h.IsPositive())
	}
}

func (s *SyncerTestSuite) TestSyncCurrencyUSDRate() {
	s.syncer.SyncCurrencyUSDRate(s.ctx, []string{"cny", "jpy"})

	data, err := s.rd.HGetAll(context.Background(), model.CurrencyRatesCacheKey).Result()
	if !s.NoError(err) {
		return
	}

	if !s.Len(data, 2) {
		return
	}

	s.T().Log(data)
}

func (s *SyncerTestSuite) TestGetSyncedCoinIDs() {
	s.syncer.popularCoinLimit = 5
	ids, err := s.syncer.getPopularCoinIDs(s.ctx)
	if !s.NoError(err) {
		return
	}
	s.Len(ids, 5)
	s.T().Log(ids)

	idsBts, err := s.rd.Get(context.Background(), model.CoinIDRankCacheKey).Bytes()
	if !s.NoError(err) {
		return
	}
	cacheIDs := []string{}
	err = json.Unmarshal(idsBts, &cacheIDs)
	if !s.NoError(err) {
		return
	}
	s.Len(cacheIDs, 5)
	s.T().Log(cacheIDs)

	err = s.rd.Del(context.Background(), model.CoinIDRankCacheKey).Err()
	if !s.NoError(err) {
		return
	}

	s.syncer.popularCoinLimit = 253
	ids, err = s.syncer.getPopularCoinIDs(s.ctx)
	if !s.NoError(err) {
		return
	}
	if s.Len(ids, 253) {
		s.T().Log(ids[:5])
		s.T().Log(ids[len(ids)-6:])
	}

}

func TestCoinDataSyncerTestSuite(t *testing.T) {
	if !syncerTestConfig.IsLocalDebug {
		t.Skip("only local debug")
	}
	suite.Run(t, new(SyncerTestSuite))
}
