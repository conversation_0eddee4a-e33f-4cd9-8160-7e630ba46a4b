package biz

import (
	"byd_wallet/model"
	"context"
)

type AppVersionRepo interface {
	GetLatestAppVersion(ctx context.Context) (*model.AppVersion, error)
}

type AppVersionUsecase struct {
	repo AppVersionRepo
}

func NewAppVersionUsecase(repo AppVersionRepo) *AppVersionUsecase {
	return &AppVersionUsecase{repo: repo}
}

func (a AppVersionUsecase) GetLatestAppVersion(ctx context.Context) (*model.AppVersion, error) {
	return a.repo.GetLatestAppVersion(ctx)
}
