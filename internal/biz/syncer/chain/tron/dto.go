package tron

import (
	"byd_wallet/common/constant"
	"byd_wallet/model"
	"bytes"
	"encoding/hex"
	"fmt"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/api"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/go-kratos/kratos/v2/log"
	"math/big"
)

func toLocalTxs(log *log.Helper, blocks []*Block, chainIndex int64) ([]*model.Transaction, error) {
	var transactions []*model.Transaction
	for _, block := range blocks {
		txs, err := blockToLocalTxs(log, block, chainIndex)
		if err != nil {
			return nil, err
		}
		transactions = append(transactions, txs...)
	}
	return transactions, nil
}

func blockToLocalTxs(log *log.Helper, block *Block, chainIndex int64) ([]*model.Transaction, error) {
	var transactions []*model.Transaction
	for i, tx := range block.BlockExtention.Transactions {
		txInfo := block.TransactionInfo[i]
		transaction, err := toLocalTx(tx, txInfo, chainIndex)
		if err != nil {
			log.Debugf("tron tx failed to convert transaction to local: %v", err)
			continue
		}
		if transaction != nil {
			transaction.BlockNumber = block.BlockExtention.GetBlockHeader().GetRawData().Number
			transaction.Timestamp = block.Unix()
			transactions = append(transactions, transaction)
		}
		logTxs, err := eventLog2LocalTxs(transaction, block, tx, txInfo, chainIndex)
		if err != nil {
			log.Debugf("tron eventLog2LocalTxs failed: %v", err)
			continue
		}
		transactions = append(transactions, logTxs...)
		internalTxs, err := internalTx2LocalTxs(block, tx, txInfo, chainIndex)
		if err != nil {
			log.Debugf("tron internalTx2LocalTxs failed: %v", err)
			continue
		}
		transactions = append(transactions, internalTxs...)

	}
	return transactions, nil
}

func internalTx2LocalTxs(block *Block, tx *api.TransactionExtention, txi *core.TransactionInfo, chainIndex int64) ([]*model.Transaction, error) {
	var transactions []*model.Transaction
	for _, internalTx := range txi.InternalTransactions {
		// 跳过被拒绝的内部交易
		if internalTx.GetRejected() {
			continue
		}

		// 检查是否有转账信息
		callValueInfos := internalTx.GetCallValueInfo()
		if len(callValueInfos) == 0 {
			continue
		}

		// 处理每个转账信息
		for _, callValueInfo := range callValueInfos {
			// 跳过没有转账金额的交易
			if callValueInfo.GetCallValue() == 0 {
				continue
			}

			transaction, err := buildLocalTx(tx, txi, chainIndex)
			if err != nil {
				return nil, err
			}

			// 设置发送方和接收方地址
			transaction.FromAddress = bytes2AddressString(internalTx.GetCallerAddress())
			transaction.ToAddress = bytes2AddressString(internalTx.GetTransferToAddress())

			// 设置转账金额
			transaction.Value = fmt.Sprintf("%d", callValueInfo.GetCallValue())

			// 根据 tokenId 判断是 TRX 还是 TRC10
			tokenId := callValueInfo.GetTokenId()
			if tokenId == "" {
				// TRX 转账
				transaction.Method = constant.TxMethodTransfer
			} else {
				// TRC10 代币转账
				transaction.Method = constant.TxMethodTransfer
				transaction.ProgramID = tokenId
			}

			// 设置区块信息
			transaction.BlockNumber = block.BlockExtention.GetBlockHeader().GetRawData().Number
			transaction.Timestamp = block.Unix()

			transactions = append(transactions, transaction)
		}
	}
	return transactions, nil
}

func eventLog2LocalTxs(existTx *model.Transaction, block *Block, tx *api.TransactionExtention, txi *core.TransactionInfo, chainIndex int64) ([]*model.Transaction, error) {
	var transactions []*model.Transaction
	for _, eventLog := range txi.Log {
		transaction, err := buildLocalTx(tx, txi, chainIndex)
		if err != nil {
			return nil, err
		}
		transaction, err = parseTRC20TransferLog(transaction, eventLog)
		if err != nil {
			continue
		}
		if existTx != nil && existTx.Equal(transaction) {
			continue
		}
		transaction.BlockNumber = block.BlockExtention.GetBlockHeader().GetRawData().Number
		transaction.Timestamp = block.Unix()
		transactions = append(transactions, transaction)
	}
	return transactions, nil
}

func buildLocalTx(tx *api.TransactionExtention, txi *core.TransactionInfo, chainIndex int64) (*model.Transaction, error) {
	if !bytes.Equal(tx.Txid, txi.Id) {
		return nil, fmt.Errorf("tron tx id not match: %s != %s", hex.EncodeToString(tx.Txid), hex.EncodeToString(txi.Id))
	}
	transaction := &model.Transaction{
		TxHash:     hex.EncodeToString(tx.Txid),
		ChainIndex: chainIndex,
		Status:     constant.TransactionStatusFail,
	}
	if len(tx.Transaction.Ret) > 0 {
		ret := tx.Transaction.Ret[0]
		if ret.ContractRet == core.Transaction_Result_SUCCESS {
			transaction.Status = constant.TransactionStatusSuccess
		}
	}
	transaction.Fee = fmt.Sprintf("%d", txi.Fee)
	return transaction, nil
}

func toLocalTx(tx *api.TransactionExtention, txi *core.TransactionInfo, chainIndex int64) (*model.Transaction, error) {
	transaction, err := buildLocalTx(tx, txi, chainIndex)
	if err != nil {
		return nil, err
	}
	contracts := tx.Transaction.GetRawData().GetContract()
	if len(contracts) != 1 {
		return nil, fmt.Errorf("invalid contracts")
	}
	contract := contracts[0]
	// 遍历所有合约调用
	switch contract.Type {
	case core.Transaction_Contract_TransferContract:
		return handleTransferContract(transaction, contract)
	case core.Transaction_Contract_TriggerSmartContract:
		return handleTriggerSmartContract(transaction, contract)
	case core.Transaction_Contract_DelegateResourceContract:
		return handleDelegateResourceContract(transaction, contract)
	case core.Transaction_Contract_CreateSmartContract:
		return handleTRC20Create(transaction, contract, txi)
	default:
		//fmt.Printf("unknown contract type: %s, hash: %s\n", contract.Type, chain.TxHash)
		return nil, nil
	}
}

func handleDelegateResourceContract(transaction *model.Transaction, contract *core.Transaction_Contract) (*model.Transaction, error) {
	var data core.DelegateResourceContract
	if err := contract.GetParameter().UnmarshalTo(&data); err != nil {
		return nil, err
	}
	transaction.FromAddress = bytes2AddressString(data.GetOwnerAddress())
	transaction.ToAddress = bytes2AddressString(data.GetReceiverAddress())
	transaction.Value = "0" // Value 通过关联tron_rent_record获取
	switch data.GetResource() {
	case core.ResourceCode_ENERGY:
		transaction.Method = constant.TxMethodDelegateEnergy
	case core.ResourceCode_BANDWIDTH:
		transaction.Method = constant.TxMethodDelegateBandwidth
	}
	return transaction, nil
}

func handleTRC20Create(transaction *model.Transaction, contract *core.Transaction_Contract, txInfo *core.TransactionInfo) (*model.Transaction, error) {
	var data core.CreateSmartContract
	if err := contract.GetParameter().UnmarshalTo(&data); err != nil {
		return nil, err
	}

	bytecode := data.NewContract.GetBytecode()
	for _, selector := range trc20Selectors {
		if !bytes.Contains(bytecode, selector) {
			// is not trc20
			return nil, nil
		}
	}
	transaction.Method = constant.TxMethodCreated
	transaction.ProgramID = bytes2AddressString(txInfo.ContractAddress)
	return transaction, nil
}

var trc20Selectors = [][]byte{
	{0x70, 0xa0, 0x82, 0x31}, // balanceOf(address)
	{0x18, 0x16, 0x0d, 0xdd}, // totalSupply()
	{0xa9, 0x05, 0x9c, 0xbb}, // transfer(address,uint256)
	{0x23, 0xb8, 0x72, 0xdd}, // transferFrom(address,address,uint256)
}

func handleTransferContract(transaction *model.Transaction, contract *core.Transaction_Contract) (*model.Transaction, error) {
	var data core.TransferContract
	if err := contract.GetParameter().UnmarshalTo(&data); err != nil {
		return nil, err
	}
	transaction.FromAddress = bytes2AddressString(data.GetOwnerAddress())
	transaction.ToAddress = bytes2AddressString(data.GetToAddress())
	transaction.Value = fmt.Sprintf("%d", data.GetAmount())
	transaction.Method = constant.TxMethodTransfer
	return transaction, nil
}

func handleTriggerSmartContract(transaction *model.Transaction, contract *core.Transaction_Contract) (*model.Transaction, error) {
	var data core.TriggerSmartContract
	if err := contract.GetParameter().UnmarshalTo(&data); err != nil {
		return nil, err
	}
	transaction.FromAddress = bytes2AddressString(data.GetOwnerAddress())
	transaction.ProgramID = bytes2AddressString(data.GetContractAddress())

	dataBytes := data.GetData()
	methodID := decodeTRC20MethodID(dataBytes)
	var err error
	switch methodID {
	case trc20TransferMethodSignature:
		if transaction.ToAddress, transaction.Value, err = decodeTRC20TransferData(dataBytes); err != nil {
			return nil, fmt.Errorf("[%s]decodeTRC20TransferData: %w", transaction.TxHash, err)
		}
		transaction.Method = constant.TxMethodTransfer
		return transaction, nil
	case trc20TransferFromMethodSignature:
		if transaction.FromAddress, transaction.ToAddress, transaction.Value, err = decodeTRC20TransferFromData(dataBytes); err != nil {
			return nil, err
		}
		transaction.Method = constant.TxMethodTransfer
		return transaction, nil
	case trc20ApproveMethodSignature:
		if transaction.ToAddress, transaction.Value, err = decodeTRC20ApprovalData(dataBytes); err != nil {
			return nil, fmt.Errorf("[%s]decodeTRC20ApprovalData: %w", transaction.TxHash, err)
		}
		transaction.Method = constant.TxMethodApproval
		return transaction, nil
	default:
		// 如果方法调用失败或者是其他方法，尝试从 event log 中解析 Transfer 事件
		//for _, eventLog := range txi.Log {
		//	if parsedTx, err := parseTRC20TransferLog(transaction, eventLog); err == nil && parsedTx != nil {
		//		return nil
		//	}
		//}
	}

	return nil, nil
}

// parseTRC20TransferLog 解析 TRC20 Transfer event log
func parseTRC20TransferLog(transaction *model.Transaction, eventLog *core.TransactionInfo_Log) (*model.Transaction, error) {
	// 检查是否是 Transfer 事件 (topic[0] 应该是 Transfer 事件的签名)
	if len(eventLog.Topics) < 3 {
		return nil, fmt.Errorf("insufficient topics for Transfer event")
	}

	// 验证第一个 topic 是否是 Transfer 事件签名
	transferEventTopic := hex.EncodeToString(eventLog.Topics[0])
	if transferEventTopic != trc20TransferEventSignature[2:] { // 去掉 "0x" 前缀
		return nil, fmt.Errorf("not a Transfer event")
	}

	// 解析 from 地址 (topic[1])
	if len(eventLog.Topics[1]) != 32 {
		return nil, fmt.Errorf("invalid from address length")
	}
	fromBytes := eventLog.Topics[1][12:32] // 取后20字节
	fromAddress := trc20Bytes2AddressString(fromBytes)

	// 解析 to 地址 (topic[2])
	if len(eventLog.Topics[2]) != 32 {
		return nil, fmt.Errorf("invalid to address length")
	}
	toBytes := eventLog.Topics[2][12:32] // 取后20字节
	toAddress := trc20Bytes2AddressString(toBytes)

	// 解析转账金额 (data 部分)
	if len(eventLog.Data) != 32 {
		return nil, fmt.Errorf("invalid data length for Transfer event")
	}
	value := new(big.Int).SetBytes(eventLog.Data)

	// 更新交易信息
	transaction.FromAddress = fromAddress
	transaction.ToAddress = toAddress
	transaction.Value = value.String()
	transaction.Method = constant.TxMethodTransfer
	transaction.ProgramID = trc20Bytes2AddressString(eventLog.Address) // 合约地址

	return transaction, nil
}
