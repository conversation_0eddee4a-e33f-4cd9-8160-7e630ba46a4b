package gaspool

import (
	"byd_wallet/model"
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
)

type SendTxReq struct {
	ChainIndex int64
	RawTxHex   string
	TxType     model.GasPoolTxType
}

type SendTxReply struct {
	ChainIndex int64
	TxHash     string
}

type UserTx struct {
	ChainIndex    int64
	TxHash        string
	TxHashBytes   []byte
	TxType        model.GasPoolTxType
	From          string
	To            string
	Value         decimal.Decimal
	ValueUSDT     decimal.Decimal
	Price         decimal.Decimal
	PriceTimeUnix int64
	FeeLimit      decimal.Decimal
	Contract      string
	RawTxHex      string
	RawTxBytes    []byte
	UserID        uint
	BroadcastTx   interface{}
}

type UserTxGas struct {
	Gas           decimal.Decimal
	GasUSDT       decimal.Decimal
	Price         decimal.Decimal
	PriceTimeUnix int64
	// only tron
	Bandwidth      decimal.Decimal
	BandwidthPrice decimal.Decimal
	Energy         decimal.Decimal
	EnergyPrice    decimal.Decimal
	ActivateFee    decimal.Decimal
}

type GasPoolDepositTokenViewFilter struct {
	Enable *bool
}

type GasPoolSponsorTxConsumeViewFilter struct {
	Page     int64
	PageSize int64
	OrderBy  string
	UserID   uint
}

type GasPoolSponsorTxCashFlowViewFilter struct {
	Page     int64
	PageSize int64
	OrderBy  string
	UserID   uint
}

type GasPoolSponsorTxFilter struct {
	Page     int64
	PageSize int64
	OrderBy  string
	TxType   []model.GasPoolTxType
	UserID   uint
}

type GasPoolFlowFilter struct {
	Page      int64
	PageSize  int64
	OrderBy   string
	FlowType  []model.GasPoolFlowType
	Direction model.GasPoolFlowDirection
	UserID    uint
}

type UserLocker interface {
	TryLock(ctx context.Context, userID uint, expire time.Duration) error
	UnLock(ctx context.Context, userID uint) error
}

type Paymaster interface {
	DecodeUserTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (*UserTx, error)
	VerifyUserTxSignature(ctx context.Context, tx *UserTx) (bool, error)
	// EstimateGasUSDT estimates gas used USDT
	// NOTE: GasUSDT = Gas / (10 ^ Gas's Decimals) * GasPriceUSDT * (10 ^ USDT's Decimals)
	EstimateGas(ctx context.Context, tx *UserTx) (*UserTxGas, error)
	SendDepositTx(ctx context.Context, tx *model.GasPoolSponsorTx) error
	SendSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) error
}

type PaymasterFactory interface {
	GetPaymaster(ctx context.Context, chainIndex int64) (Paymaster, error)
}

type Repo interface {
	DepositGasPool(ctx context.Context, userID uint, amount decimal.Decimal, chainIndex int64, txHash string) (*model.GasPoolFlow, error)
	ReduceGasPool(ctx context.Context, userID uint, amount decimal.Decimal, chainIndex int64, txHash string) (*model.GasPoolFlow, error)
	RefundGasPool(ctx context.Context, reduceFlowID uint, amount decimal.Decimal) (*model.GasPoolFlow, error)
	FindGasPoolByUserID(ctx context.Context, userID uint) (*model.GasPool, error)
	SaveGasPool(ctx context.Context, gp *model.GasPool) (*model.GasPool, error)

	ListGasPoolFlow(ctx context.Context, filter *GasPoolFlowFilter) (list []*model.GasPoolFlow, totalCount int64, err error)

	SaveGasPoolSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) (*model.GasPoolSponsorTx, error)
	UpdateGasPoolSponsorTx(ctx context.Context, tx *model.GasPoolSponsorTx) error
	ListGasPoolSponsorTx(ctx context.Context, filter *GasPoolSponsorTxFilter) (list []*model.GasPoolSponsorTx, totalCount int64, err error)
	ListGasPoolSponsorTxConsumeView(ctx context.Context, filter *GasPoolSponsorTxConsumeViewFilter) (
		list []*model.GasPoolSponsorTxConsumeView, totalCount int64, err error)
	ListGasPoolSponsorTxCashFlowView(ctx context.Context, filter *GasPoolSponsorTxCashFlowViewFilter) (
		list []*model.GasPoolSponsorTxCashFlowView, totalCount int64, err error)

	FindUserIDByAddress(ctx context.Context, chainIndex int64, address string) (userID uint, err error)

	ExistsDepositReceiverAddress(ctx context.Context, chainIndex int64, address string) (bool, error)

	FindGasPoolDepositTokenByAddress(ctx context.Context, chainIndex int64, address string) (*model.GasPoolDepositToken, error)
	ListGasPoolDepositTokenView(ctx context.Context, filter *GasPoolDepositTokenViewFilter) ([]*model.GasPoolDepositTokenView, error)
}

type Usecase struct {
	log        *log.Helper
	userLocker UserLocker
	pmf        PaymasterFactory
	repo       Repo
}

func NewUsecase(logger log.Logger,
	repo Repo,
	userLocker UserLocker,
	pmf PaymasterFactory) *Usecase {
	return &Usecase{
		pmf:        pmf,
		log:        log.NewHelper(logger),
		repo:       repo,
		userLocker: userLocker,
	}
}

func (uc *Usecase) ListGasPoolSponsorTxCashFlowView(ctx context.Context, filter *GasPoolSponsorTxCashFlowViewFilter) (
	list []*model.GasPoolSponsorTxCashFlowView, totalCount int64, err error) {
	return uc.repo.ListGasPoolSponsorTxCashFlowView(ctx, filter)
}

func (uc *Usecase) ListGasPoolSponsorTxConsumeView(ctx context.Context, filter *GasPoolSponsorTxConsumeViewFilter) (
	list []*model.GasPoolSponsorTxConsumeView, totalCount int64, err error) {
	return uc.repo.ListGasPoolSponsorTxConsumeView(ctx, filter)
}

func (uc *Usecase) ListGasPoolFlow(ctx context.Context, filter *GasPoolFlowFilter) (list []*model.GasPoolFlow, totalCount int64, err error) {
	return uc.repo.ListGasPoolFlow(ctx, filter)
}

func (uc *Usecase) ListGasPoolSponsorTx(ctx context.Context, filter *GasPoolSponsorTxFilter) (list []*model.GasPoolSponsorTx, totalCount int64, err error) {
	return uc.repo.ListGasPoolSponsorTx(ctx, filter)
}

func (uc *Usecase) CreateGasPool(ctx context.Context, gp *model.GasPool) (*model.GasPool, error) {
	return uc.repo.SaveGasPool(ctx, gp)
}

func (uc *Usecase) FindGasPoolByUserID(ctx context.Context, userID uint) (*model.GasPool, error) {
	return uc.repo.FindGasPoolByUserID(ctx, userID)
}

func (uc *Usecase) ListGasPoolDepositTokenView(ctx context.Context, filter *GasPoolDepositTokenViewFilter) ([]*model.GasPoolDepositTokenView, error) {
	list, err := uc.repo.ListGasPoolDepositTokenView(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (uc *Usecase) SendTx(ctx context.Context, req *SendTxReq) (*SendTxReply, error) {
	if err := req.TxType.Valid(); err != nil {
		return nil, err
	}
	paymaster, err := uc.pmf.GetPaymaster(ctx, req.ChainIndex)
	if err != nil {
		return nil, err
	}
	userTx, err := paymaster.DecodeUserTx(ctx, req.RawTxHex, req.TxType)
	if err != nil {
		return nil, err
	}
	ok, err := paymaster.VerifyUserTxSignature(ctx, userTx)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("invalid user tx signature")
	}
	if userTx.TxType.IsDepositGasPool() {
		// pre reduce gas // only token
		if userTx.TxType == model.GasPoolTxTypeDepositPreReduceGas {
			if userTx.Contract == "" {
				return nil, errors.New("not allow deposit gas pool")
			}
		}
		// check deposit token
		token, err := uc.repo.FindGasPoolDepositTokenByAddress(ctx, userTx.ChainIndex, userTx.Contract)
		if err != nil {
			return nil, fmt.Errorf("deposit token not found: %v: %s", err, userTx.Contract)
		}
		// check deposit token enable
		if !token.Enable {
			return nil, fmt.Errorf("deposit token not enable: %s", userTx.Contract)
		}
		// check deposit amount
		if userTx.Value.LessThan(token.MinDepositAmount) {
			return nil, fmt.Errorf("deposit amount is too small: %s: %s", userTx.Value.String(), token.MinDepositAmount.String())
		}
		// check tx receiver
		ok, err := uc.repo.ExistsDepositReceiverAddress(ctx, userTx.ChainIndex, userTx.To)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, fmt.Errorf("deposit receiver not found: %s", userTx.To)
		}
	}
	userID, err := uc.repo.FindUserIDByAddress(ctx, userTx.ChainIndex, userTx.From)
	if err != nil {
		return nil, err
	}

	// TODO: without gas: check value >= gas

	if err = uc.userLocker.TryLock(ctx, userID, 30*time.Minute); err != nil {
		return nil, err
	}
	defer uc.userLocker.UnLock(ctx, userID)

	stx := &model.GasPoolSponsorTx{
		ChainIndex: userTx.ChainIndex,
		UserID:     userID,
		RawTxHex:   userTx.RawTxHex,
		TxHash:     userTx.TxHash,
		TxType:     userTx.TxType,
		From:       userTx.From,
		To:         userTx.To,
		Contract:   userTx.Contract,
		Value:      userTx.Value,
		ValueUSDT:  userTx.ValueUSDT,
		Status:     model.GasPoolTxStatusInit,
		// logic context
		BroadcastTx: userTx.BroadcastTx,
	}

	if userTx.TxType.IsUseGasPool() {
		gpGas, err := paymaster.EstimateGas(ctx, userTx)
		if err != nil {
			return nil, err
		}
		stx.Price = gpGas.Price
		stx.PriceTimeUnix = gpGas.PriceTimeUnix
		stx.Gas = gpGas.Gas
		stx.GasUSDT = gpGas.GasUSDT
		stx.Bandwidth = gpGas.Bandwidth
		stx.BandwidthPrice = gpGas.BandwidthPrice
		stx.ActivateFee = gpGas.ActivateFee
		stx.Energy = gpGas.Energy
		stx.EnergyPrice = gpGas.EnergyPrice
		if !userTx.TxType.IsPreReduceGasPool() {
			flow, err := uc.repo.ReduceGasPool(ctx,
				userID,
				stx.GasUSDT,
				stx.ChainIndex,
				stx.TxHash)
			if err != nil {
				return nil, err
			}
			stx.ReduceFlowID = flow.ID
		}
		stx, err = uc.repo.SaveGasPoolSponsorTx(ctx, stx)
		if err != nil {
			return nil, err
		}
		err = paymaster.SendSponsorTx(ctx, stx)
		if err != nil {
			return nil, err
		}
	} else {
		stx, err = uc.repo.SaveGasPoolSponsorTx(ctx, stx)
		if err != nil {
			return nil, err
		}
		err = paymaster.SendDepositTx(ctx, stx)
		if err != nil {
			return nil, err
		}
	}
	return &SendTxReply{
		ChainIndex: stx.ChainIndex,
		TxHash:     stx.TxHash,
	}, nil
}
