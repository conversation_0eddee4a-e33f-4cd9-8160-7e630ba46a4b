package tron

import (
	"encoding/hex"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestVerifyTxSignature(t *testing.T) {
	txHash, _ := hex.DecodeString("6f99e0e20a39a398ad468977b3bec726ed09ff45ae5f44d27589389134fdc2bf")
	signature, _ := hex.DecodeString("8e13662e12e662154971f06ebf6bbac10264ffb4429096df6b4c026eb36921d36bdd378fabab3f05caadcdcb69049b28f58f1ca28b8308f78a41896cddcd07f400")
	addr := "TMBHCWVLYQYUy688EByoduJnzeVnkwnQRY"
	ok, err := verifyTxSignature(txHash, signature, addr)
	assert.NoError(t, err)
	assert.True(t, ok)
}

func TestParseRawTxJson(t *testing.T) {
	tx, err := parseRawTxJson("{\"txID\":\"c232814568d8f1aadd175abfc026672472afca88b9d5afa874c09accd04a50cc\",\"visible\":true,\"raw_data\":{\"contract\":[{\"parameter\":{\"type_url\":\"type.googleapis.com/protocol.TransferContract\",\"value\":{\"amount\":10000000,\"owner_address\":\"TMBHCWVLYQYUy688EByoduJnzeVnkwnQRY\",\"to_address\":\"TGHvJo6Fprc9tMJpRNqakjcU9pCSZ2aqKj\"}},\"type\":\"TransferContract\"}],\"expiration\":1753784016000,\"ref_block_bytes\":\"dfa4\",\"ref_block_hash\":\"38db83ad453cfee3\",\"timestamp\":1753783957502},\"raw_data_hex\":\"0a02dfa4220838db83ad453cfee34080b9b1ad85335a68080112640a2d747970652e676f6f676c65617069732e636f6d2f70726f746f636f6c2e5472616e73666572436f6e747261637412330a15417af1e1058f1e2b3db4a2a905b9095ebe0d2928b6121541455ab8ecaebcc25d44037f3a95ce074a92235bca1880ade20470feefadad8533\",\"signature\":[\"0x098dd3b7a66d8f4fb2a214b8f7b279a88b1c77595493dd5d28b066675e7908186e0e7693f173a5ef6034900da57129286e15c3e9cd45554478fb0cfdad1c8ccd01\"]}")
	assert.NoError(t, err)
	t.Log(tx)
}
