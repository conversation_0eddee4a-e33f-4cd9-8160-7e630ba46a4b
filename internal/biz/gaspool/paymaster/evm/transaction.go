package evm

import (
	"byd_wallet/common/constant"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"encoding/hex"
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/shopspring/decimal"
)

// transaction.go - EVM paymaster 交易处理相关功能
// 包含交易解码、发送用户交易、gas转账等核心交易处理逻辑

// decodeTransferTx 解码转账交易
// 参数:
//   - ctx: 上下文对象
//   - rawTx: 原始交易数据
//
// 返回值:
//   - *gaspool.UserTx: 解码后的用户交易
//   - error: 错误信息
func (pm *Paymaster) decodeTransferTx(ctx context.Context, rawTxHex string, txType model.GasPoolTxType) (*gaspool.UserTx, error) {
	evmTx, err := utils.RlpDecodeBytes(rawTxHex)
	if err != nil {
		return nil, fmt.Errorf("failed to decode raw tx: %w", err)
	}

	sender, err := utils.GetTxSender(evmTx)
	if err != nil {
		return nil, fmt.Errorf("failed to get tx sender: %w", err)
	}

	chainID := constant.GetChainID(pm.chainIndex)
	if chainID == 0 {
		return nil, fmt.Errorf("invalid chainIndex: %d", pm.chainIndex)
	}

	// 默认使用原生转账的 to 和 value
	toAddr := evmTx.To().Hex()
	value := decimal.NewFromBigInt(evmTx.Value(), 0)
	contract := ""

	// 检查是否是 ERC20 Transfer
	if data := evmTx.Data(); len(data) >= 4 { // 至少包含 methodID（4字节）
		methodID := hex.EncodeToString(data[:4])
		if methodID == constant.ERC20TransferMethodID && len(data) >= 68 { // 4(methodID) + 32(to) + 32(value)
			// 解析 to 地址（跳过前 12 字节的 0 填充）
			to := common.BytesToAddress(data[16:36]).Hex()
			// 解析转账金额
			amount := new(big.Int).SetBytes(data[36:68])
			value = decimal.NewFromBigInt(amount, 0)
			toAddr = to
			contract = evmTx.To().Hex() // 合约地址
		}
	}
	return &gaspool.UserTx{
		From:       sender.Hex(),
		To:         toAddr,
		Value:      value,
		ChainIndex: pm.chainIndex,
		RawTxHex:   rawTxHex,
		Contract:   contract,
		TxType:     txType,
		TxHash:     evmTx.Hash().Hex(),
	}, nil
}

// sendUserTransaction 发送用户交易
// 参数:
//   - ctx: 上下文对象
//   - stx: sponsor交易数据
//
// 返回值:
//   - error: 错误信息
func (pm *Paymaster) sendUserTransaction(ctx context.Context, stx *model.GasPoolSponsorTx) error {
	// 解码EVM交易
	evmTx, err := utils.RlpDecodeBytes(stx.RawTxHex)
	if err != nil {
		return fmt.Errorf("解码EVM交易失败: %w", err)
	}

	// 获取EVM客户端
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		return fmt.Errorf("获取%s链客户端失败: %w", pm.chainName, err)
	}

	// 发送用户交易
	err = client.SendTransaction(ctx, evmTx)
	if err != nil {
		return fmt.Errorf("发送%s链用户交易失败: %w", pm.chainName, err)
	}

	// 更新交易状态为pending
	update := &model.GasPoolSponsorTx{
		Model:  stx.Model,
		Status: model.GasPoolTxStatusPending,
		TxHash: evmTx.Hash().Hex(),
	}

	err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
	if err != nil {
		pm.log.Errorf("更新%s链sponsor交易状态失败: %v: %d: %s",
			pm.chainName, err, stx.ID, update.Status)
	}

	pm.log.Infof("成功发送%s链用户交易，交易ID: %d，交易哈希: %s",
		pm.chainName, stx.ID, evmTx.Hash().Hex())

	return nil
}

// transferGasToSender 从热钱包向交易发送者转账gas费用
// 参数:
//   - ctx: 上下文对象
//   - senderAddr: 交易发送者地址
//   - gasAmount: 需要转账的gas金额（以decimal格式表示）
//
// 返回值:
//   - string: 转账交易的哈希值
//   - error: 错误信息
func (pm *Paymaster) transferGasToSender(ctx context.Context, senderAddr common.Address, gasAmount decimal.Decimal) (string, error) {
	pm.log.Debugf("开始从热钱包向发送者 %s 转账gas费用，金额: %s wei", senderAddr.Hex(), gasAmount.String())

	// 步骤0: 验证gas金额
	gasAmountWei := gasAmount.BigInt()
	if gasAmountWei.Sign() <= 0 {
		pm.log.Errorf("gas金额无效: %s", gasAmount.String())
		return "", fmt.Errorf("invalid gas amount: %s", gasAmount.String())
	}

	// 步骤1: 获取热钱包私钥
	hotWalletPrivateKey, err := pm.hotAccountReader.GetHotAccount(ctx, pm.chainIndex)
	if err != nil {
		pm.log.Errorf("获取%s链热钱包私钥失败: %v", pm.chainName, err)
		return "", fmt.Errorf("failed to get hot wallet private key: %w", err)
	}

	// 步骤2: 从私钥获取热钱包地址
	hotWalletAddr, privateKey, err := utils.GetAddressByPrivateKey(hotWalletPrivateKey)
	if err != nil {
		pm.log.Errorf("从私钥解析热钱包地址失败: %v", err)
		return "", fmt.Errorf("failed to get address from private key: %w", err)
	}

	pm.log.Debugf("热钱包地址: %s，准备向 %s 转账", hotWalletAddr.Hex(), senderAddr.Hex())

	// 步骤3: 获取EVM客户端
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		pm.log.Errorf("获取%s链客户端失败: %v", pm.chainName, err)
		return "", fmt.Errorf("failed to get EVM client: %w", err)
	}

	// 步骤4: 获取链ID
	chainID, err := client.ChainID(ctx)
	if err != nil {
		pm.log.Errorf("获取%s链ID失败: %v", pm.chainName, err)
		return "", fmt.Errorf("failed to get chain ID: %w", err)
	}

	// 步骤5: 获取热钱包的nonce
	nonce, err := client.PendingNonceAt(ctx, hotWalletAddr)
	if err != nil {
		pm.log.Errorf("获取热钱包nonce失败: %v", err)
		return "", fmt.Errorf("failed to get nonce: %w", err)
	}

	// 步骤6: 获取当前网络gas价格并应用1.2x倍数策略
	gasTipCap, err := client.SuggestGasTipCap(ctx)
	if err != nil {
		pm.log.Warnf("获取建议tip失败，使用默认值: %v", err)
		gasTipCap = big.NewInt(constant.DefaultGasTipCap)
	}

	// 计算基础费用（简化处理，实际应该从最新区块获取）
	baseFee := big.NewInt(constant.DefaultBaseFee)

	// 应用1.2x倍数策略以确保更快的交易确认
	gasPriceMultiplier := decimal.NewFromFloat(1.2)

	// 对tip和baseFee分别应用倍数
	adjustedGasTipCap := decimal.NewFromBigInt(gasTipCap, 0).Mul(gasPriceMultiplier).BigInt()
	adjustedBaseFee := decimal.NewFromBigInt(baseFee, 0).Mul(gasPriceMultiplier).BigInt()
	gasFeeCap := new(big.Int).Add(adjustedGasTipCap, adjustedBaseFee)

	pm.log.Debugf("gas价格策略 - 原始tip: %s wei, 调整后tip: %s wei, 原始baseFee: %s wei, 调整后baseFee: %s wei, 最终gasFeeCap: %s wei",
		gasTipCap.String(), adjustedGasTipCap.String(), baseFee.String(), adjustedBaseFee.String(), gasFeeCap.String())

	// 步骤7: 使用EstimateGas进行动态gas limit估算
	estimatedGasLimit, err := client.EstimateGas(ctx, ethereum.CallMsg{
		From:      hotWalletAddr,
		To:        &senderAddr,
		GasFeeCap: gasFeeCap,
		GasTipCap: adjustedGasTipCap,
		Value:     gasAmountWei,
		Data:      nil, // 原生代币转账不需要data
	})
	if err != nil {
		pm.log.Warnf("gas limit估算失败，使用默认值21000: %v", err)
		estimatedGasLimit = 21000 // 回退到标准ETH转账的gas limit
	}

	// 应用安全系数到gas limit（使用链特定的倍数）
	gasLimitMultiplier := pm.getChainSpecificGasMultiplier()
	safeGasLimit := decimal.NewFromInt(int64(estimatedGasLimit)).Mul(gasLimitMultiplier)
	gasLimit := safeGasLimit.BigInt().Uint64()

	pm.log.Debugf("gas limit估算 - 估算值: %d, 安全系数: %s, 最终gas limit: %d",
		estimatedGasLimit, gasLimitMultiplier.String(), gasLimit)

	// 步骤8: gas金额已在方法开始时验证和转换

	pm.log.Debugf("创建转账交易 - From: %s, To: %s, Value: %s wei, Nonce: %d, GasLimit: %d, GasFeeCap: %s wei, GasTipCap: %s wei",
		hotWalletAddr.Hex(), senderAddr.Hex(), gasAmountWei.String(), nonce, gasLimit, gasFeeCap.String(), adjustedGasTipCap.String())

	// 创建EIP-1559交易，使用调整后的gas价格和估算的gas limit
	tx := types.NewTx(&types.DynamicFeeTx{
		ChainID:   chainID,
		Nonce:     nonce,
		GasFeeCap: gasFeeCap,
		GasTipCap: adjustedGasTipCap,
		Gas:       gasLimit,
		To:        &senderAddr,
		Value:     gasAmountWei,
		Data:      nil, // 原生代币转账不需要data
	})

	// 步骤9: 签名交易
	signedTx, err := types.SignTx(tx, types.NewLondonSigner(chainID), privateKey)
	if err != nil {
		pm.log.Errorf("签名转账交易失败: %v", err)
		return "", fmt.Errorf("failed to sign transaction: %w", err)
	}

	// 步骤10: 发送转账交易
	err = client.SendTransaction(ctx, signedTx)
	if err != nil {
		pm.log.Errorf("发送gas转账交易失败: %v", err)
		return "", fmt.Errorf("failed to send gas transfer transaction: %w", err)
	}

	txHash := signedTx.Hash().Hex()

	// 计算实际交易费用用于日志记录
	actualTxFee := new(big.Int).Mul(new(big.Int).SetUint64(gasLimit), gasFeeCap)

	pm.log.Infof("成功发送gas转账交易 - 哈希: %s, 从 %s 向 %s 转账 %s wei, gas策略: 1.2x倍数, gasLimit: %d, gasFeeCap: %s wei, 预估交易费用: %s wei",
		txHash, hotWalletAddr.Hex(), senderAddr.Hex(), gasAmountWei.String(), gasLimit, gasFeeCap.String(), actualTxFee.String())

	return txHash, nil
}

func (pm *Paymaster) sendTransaction(ctx context.Context, tx *types.Transaction) (string, error) {
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		return "", fmt.Errorf("failed to get EVM client: %w", err)
	}
	err = client.SendTransaction(ctx, tx)
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %w", err)
	}
	txHash := tx.Hash().Hex()
	return txHash, nil
}
