package bsc

import (
	"byd_wallet/common/constant"
	"byd_wallet/model"
	"byd_wallet/utils"
	"context"
	"encoding/hex"
	"fmt"
	"math/big"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/node-real/megafuel-go-sdk/pkg/paymasterclient"
	"github.com/shopspring/decimal"
)

// sendTransactionViaMegaFuel 通过MegaFuel服务发送交易（带重试机制）
// 参数:
//   - ctx: 上下文对象
//   - rawTx: 原始交易数据
//
// 返回值:
//   - string: 交易哈希
//   - error: 错误信息
func (pm *Paymaster) sendTransactionViaMegaFuel(ctx context.Context, rawTxHex string) (string, error) {
	pm.log.Debugf("开始通过MegaFuel发送BSC交易")

	// 检查MegaFuel客户端是否已初始化
	if pm.megafuelClient == nil {
		return "", fmt.Errorf("MegaFuel客户端未初始化，请先调用InitMegaFuelClient方法")
	}

	// 解码EVM交易以获取交易参数
	evmTx, err := utils.RlpDecodeBytes(rawTxHex)
	if err != nil {
		return "", fmt.Errorf("解码EVM交易失败: %w", err)
	}

	// 构建MegaFuel交易参数
	transactionArgs, err := pm.buildMegaFuelTransactionArgs(evmTx)
	if err != nil {
		return "", fmt.Errorf("构建MegaFuel交易参数失败: %w", err)
	}

	pm.log.Debugf("MegaFuel交易参数: From=%s, To=%s, Value=%s, Gas=%d",
		transactionArgs.From.Hex(), transactionArgs.To.Hex(),
		transactionArgs.Value.String(), uint64(*transactionArgs.Gas))

	// 带重试机制的MegaFuel调用
	txHash, err := pm.sendTransactionWithRetry(ctx, transactionArgs, rawTxHex)
	if err != nil {
		return "", fmt.Errorf("MegaFuel发送交易失败: %w", err)
	}

	pm.log.Infof("MegaFuel交易发送成功，交易哈希: %s", txHash)
	return txHash, nil
}

// buildMegaFuelTransactionArgs 构建MegaFuel交易参数
// 参数:
//   - evmTx: EVM交易对象
//
// 返回值:
//   - paymasterclient.TransactionArgs: MegaFuel交易参数
//   - error: 错误信息
func (pm *Paymaster) buildMegaFuelTransactionArgs(evmTx *types.Transaction) (paymasterclient.TransactionArgs, error) {
	// 获取交易发送者地址
	sender, err := utils.GetTxSender(evmTx)
	if err != nil {
		return paymasterclient.TransactionArgs{}, fmt.Errorf("get sender error: %w", err)
	}

	// 构建交易参数
	gasLimit := evmTx.Gas()
	value := evmTx.Value()
	data := evmTx.Data()

	args := paymasterclient.TransactionArgs{
		From:  sender,
		To:    evmTx.To(),
		Value: (*hexutil.Big)(value),
		Gas:   (*hexutil.Uint64)(&gasLimit),
		Data:  (*hexutil.Bytes)(&data),
	}

	return args, nil
}

// sendTransactionWithRetry 带重试机制的MegaFuel交易发送
// 参数:
//   - ctx: 上下文对象
//   - transactionArgs: MegaFuel交易参数
//   - rawTxHex: 原始交易十六进制字符串
//
// 返回值:
//   - string: 交易哈希
//   - error: 错误信息
func (pm *Paymaster) sendTransactionWithRetry(ctx context.Context, transactionArgs paymasterclient.TransactionArgs, rawTxHex string) (string, error) {
	var lastErr error

	for attempt := 1; attempt <= pm.retryAttempts; attempt++ {
		pm.log.Debugf("MegaFuel发送交易尝试 %d/%d", attempt, pm.retryAttempts)

		// 创建带超时的上下文
		_, cancel := context.WithTimeout(ctx, time.Duration(pm.megafuelTimeout)*time.Second)
		defer cancel() // 确保上下文被释放

		// 调用MegaFuel服务（注意：当前MegaFuel客户端可能不支持上下文，这里先保留原有调用方式）
		txHash, err := pm.megafuelClient.SendTransaction(transactionArgs, rawTxHex)

		if err == nil {
			pm.log.Infof("MegaFuel交易发送成功，尝试次数: %d, 交易哈希: %s", attempt, txHash)
			return txHash, nil
		}

		lastErr = err
		pm.log.Warnf("MegaFuel发送交易失败，尝试 %d/%d: %v", attempt, pm.retryAttempts, err)

		// 检查是否是可重试的错误
		if !pm.isRetryableError(err) {
			pm.log.Errorf("遇到不可重试的错误，停止重试: %v", err)
			break
		}

		// 如果不是最后一次尝试，等待后重试
		if attempt < pm.retryAttempts {
			retryDelay := time.Duration(pm.retryDelay) * time.Millisecond
			pm.log.Debugf("等待 %v 后重试", retryDelay)

			select {
			case <-ctx.Done():
				return "", fmt.Errorf("上下文已取消: %w", ctx.Err())
			case <-time.After(retryDelay):
				// 继续重试
			}
		}
	}

	return "", fmt.Errorf("MegaFuel发送交易失败，已重试 %d 次，最后错误: %w", pm.retryAttempts, lastErr)
}

// sendTransactionWithGasTransfer 使用传统gas转账方式发送交易
// 参数:
//   - ctx: 上下文对象
//   - tx: sponsor交易数据
//   - evmTx: 解码后的EVM交易
//
// 返回值:
//   - string: 交易哈希
//   - error: 错误信息
func (pm *Paymaster) sendTransactionWithGasTransfer(ctx context.Context, tx *model.GasPoolSponsorTx, evmTx *types.Transaction) (string, error) {
	pm.log.Debugf("使用传统gas转账方式处理BSC交易，交易ID: %d", tx.ID)

	// 获取交易发送者地址
	sender, err := utils.GetTxSender(evmTx)
	if err != nil {
		pm.log.Errorf("获取交易发送者地址失败，交易ID: %d, 错误: %v", tx.ID, err)
		return "", fmt.Errorf("failed to get transaction sender: %w", err)
	}

	pm.log.Debugf("交易发送者地址: %s，开始处理gas费用转账", sender.Hex())

	// 从热钱包向交易发送者转账gas费用
	gasTransferTxHash, err := pm.transferGasToSender(ctx, sender, tx.Gas)
	if err != nil {
		pm.log.Errorf("向交易发送者转账gas费用失败，交易ID: %d, 发送者: %s, 错误: %v",
			tx.ID, sender.Hex(), err)
		return "", fmt.Errorf("failed to transfer gas to sender: %w", err)
	}

	pm.log.Infof("成功向发送者 %s 转账gas费用，转账交易哈希: %s，等待确认后发送用户交易",
		sender.Hex(), gasTransferTxHash)

	// 等待gas转账确认，然后发送用户交易
	// 注意：这里简化处理，直接发送用户交易而不等待确认
	// 在生产环境中，应该实现类似EVM paymaster的异步确认机制
	userTxHash, err := pm.sendUserTransaction(ctx, evmTx)
	if err != nil {
		pm.log.Errorf("发送用户交易失败，交易ID: %d, 错误: %v", tx.ID, err)
		return "", fmt.Errorf("failed to send user transaction: %w", err)
	}

	pm.log.Infof("BSC链用户交易发送成功，交易ID: %d，用户交易哈希: %s", tx.ID, userTxHash)
	return userTxHash, nil
}

// transferGasToSender 从热钱包向交易发送者转账gas费用
// 参数:
//   - ctx: 上下文对象
//   - senderAddr: 交易发送者地址
//   - gasAmount: 需要转账的gas金额（以decimal格式表示）
//
// 返回值:
//   - string: 转账交易的哈希值
//   - error: 错误信息
func (pm *Paymaster) transferGasToSender(ctx context.Context, senderAddr common.Address, gasAmount decimal.Decimal) (string, error) {
	pm.log.Debugf("开始从热钱包向发送者 %s 转账gas费用，金额: %s wei", senderAddr.Hex(), gasAmount.String())

	// 步骤0: 验证gas金额
	gasAmountWei := gasAmount.BigInt()
	if gasAmountWei.Sign() <= 0 {
		pm.log.Errorf("gas金额无效: %s", gasAmount.String())
		return "", fmt.Errorf("invalid gas amount: %s", gasAmount.String())
	}

	// 步骤1: 获取热钱包私钥
	hotWalletPrivateKey, err := pm.hotAccountReader.GetHotAccount(ctx, pm.chainIndex)
	if err != nil {
		pm.log.Errorf("获取BSC链热钱包私钥失败: %v", err)
		return "", fmt.Errorf("failed to get hot wallet private key: %w", err)
	}

	// 步骤2: 从私钥获取热钱包地址
	hotWalletAddr, privateKey, err := utils.GetAddressByPrivateKey(hotWalletPrivateKey)
	if err != nil {
		pm.log.Errorf("从私钥解析热钱包地址失败: %v", err)
		return "", fmt.Errorf("failed to get address from private key: %w", err)
	}

	pm.log.Debugf("热钱包地址: %s，准备向 %s 转账", hotWalletAddr.Hex(), senderAddr.Hex())

	// 步骤3: 获取EVM客户端
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		pm.log.Errorf("获取BSC链客户端失败: %v", err)
		return "", fmt.Errorf("failed to get EVM client: %w", err)
	}

	// 步骤4: 获取链ID
	chainID, err := client.ChainID(ctx)
	if err != nil {
		pm.log.Errorf("获取BSC链ID失败: %v", err)
		return "", fmt.Errorf("failed to get chain ID: %w", err)
	}

	// 步骤5: 获取热钱包的nonce
	nonce, err := client.PendingNonceAt(ctx, hotWalletAddr)
	if err != nil {
		pm.log.Errorf("获取热钱包nonce失败: %v", err)
		return "", fmt.Errorf("failed to get nonce: %w", err)
	}

	// 步骤6: 估算gas价格和费用上限
	gasTipCap, err := client.SuggestGasTipCap(ctx)
	if err != nil {
		pm.log.Warnf("获取建议tip失败，使用默认值: %v", err)
		gasTipCap = big.NewInt(constant.DefaultGasTipCap)
	}

	// 计算基础费用（简化处理，实际应该从最新区块获取）
	baseFee := big.NewInt(constant.DefaultBaseFee)
	gasFeeCap := new(big.Int).Add(gasTipCap, baseFee)

	// 步骤7: 创建原生代币转账交易
	// 使用固定的gas limit进行原生代币转账
	gasLimit := uint64(21000) // 标准BNB转账的gas limit

	pm.log.Debugf("创建转账交易 - From: %s, To: %s, Value: %s wei, Nonce: %d, GasLimit: %d",
		hotWalletAddr.Hex(), senderAddr.Hex(), gasAmountWei.String(), nonce, gasLimit)

	// 创建EIP-1559交易
	tx := types.NewTx(&types.DynamicFeeTx{
		ChainID:   chainID,
		Nonce:     nonce,
		GasFeeCap: gasFeeCap,
		GasTipCap: gasTipCap,
		Gas:       gasLimit,
		To:        &senderAddr,
		Value:     gasAmountWei,
		Data:      nil, // 原生代币转账不需要data
	})

	// 步骤8: 签名交易
	signedTx, err := types.SignTx(tx, types.NewLondonSigner(chainID), privateKey)
	if err != nil {
		pm.log.Errorf("签名转账交易失败: %v", err)
		return "", fmt.Errorf("failed to sign transaction: %w", err)
	}

	// 步骤9: 发送转账交易
	err = client.SendTransaction(ctx, signedTx)
	if err != nil {
		pm.log.Errorf("发送gas转账交易失败: %v", err)
		return "", fmt.Errorf("failed to send gas transfer transaction: %w", err)
	}

	txHash := signedTx.Hash().Hex()
	pm.log.Infof("成功发送gas转账交易 - 哈希: %s, 从 %s 向 %s 转账 %s wei",
		txHash, hotWalletAddr.Hex(), senderAddr.Hex(), gasAmountWei.String())

	return txHash, nil
}

// sendUserTransaction 发送用户交易
// 参数:
//   - ctx: 上下文对象
//   - evmTx: EVM交易对象
//
// 返回值:
//   - string: 交易哈希
//   - error: 错误信息
func (pm *Paymaster) sendUserTransaction(ctx context.Context, evmTx *types.Transaction) (string, error) {
	pm.log.Debugf("开始发送BSC链用户交易")

	// 获取EVM客户端
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		pm.log.Errorf("获取BSC链客户端失败: %v", err)
		return "", fmt.Errorf("failed to get EVM client: %w", err)
	}

	// 发送用户交易
	err = client.SendTransaction(ctx, evmTx)
	if err != nil {
		pm.log.Errorf("发送BSC链用户交易失败: %v", err)
		return "", fmt.Errorf("failed to send user transaction: %w", err)
	}

	txHash := evmTx.Hash().Hex()
	pm.log.Infof("BSC链用户交易发送成功，交易哈希: %s", txHash)

	return txHash, nil
}

// isTokenTransfer 检查交易是否为ERC20 token转账
// 参数:
//   - evmTx: EVM交易对象
//
// 返回值:
//   - bool: 是否为token转账
func (pm *Paymaster) isTokenTransfer(evmTx *types.Transaction) bool {
	// 检查交易是否有data字段且长度足够
	data := evmTx.Data()
	if len(data) < 4 {
		return false // 没有方法签名，不是合约调用
	}

	// 检查方法签名是否为ERC20 transfer
	methodID := hex.EncodeToString(data[:4])
	if methodID != constant.ERC20TransferMethodID {
		return false // 不是transfer方法
	}

	// 检查data长度是否符合transfer方法的要求
	if len(data) < 68 { // 4(methodID) + 32(to) + 32(value)
		return false // data长度不足
	}

	// 检查交易的to地址是否为合约地址（这里简化处理，认为有data的交易都是合约调用）
	if evmTx.To() == nil {
		return false // 合约创建交易，不是transfer
	}

	pm.log.Debugf("检测到ERC20 token转账交易，合约地址: %s", evmTx.To().Hex())
	return true
}

// isRetryableError 判断错误是否可重试
// 参数:
//   - err: 错误对象
//
// 返回值:
//   - bool: 是否可重试
func (pm *Paymaster) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// 网络相关错误通常可重试
	retryableErrors := []string{
		"timeout",
		"connection refused",
		"connection reset",
		"network is unreachable",
		"temporary failure",
		"service unavailable",
		"internal server error",
		"bad gateway",
		"gateway timeout",
		"nonce too high",
		"nonce too low",
	}

	for _, retryableErr := range retryableErrors {
		if contains(errStr, retryableErr) {
			return true
		}
	}

	// 不可重试的错误（如交易格式错误、余额不足等）
	nonRetryableErrors := []string{
		"not sponsorable",
		"invalid transaction",
		"insufficient funds",
		"gas limit exceeded",
		"invalid signature",
	}

	for _, nonRetryableErr := range nonRetryableErrors {
		if contains(errStr, nonRetryableErr) {
			return false
		}
	}

	// 默认情况下，未知错误可重试
	return true
}

// contains 检查字符串是否包含子字符串（不区分大小写）
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					containsSubstring(s, substr)))
}

// containsSubstring 辅助函数：检查字符串中是否包含子字符串
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
