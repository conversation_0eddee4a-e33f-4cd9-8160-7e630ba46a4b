package biz

import (
	"byd_wallet/model"
	"context"
)

// UserGuideRepo 用户指南仓库接口
type UserGuideRepo interface {
	ListUserGuideCategory(ctx context.Context) ([]*model.UserGuideCategory, error)
	ListUserGuide(ctx context.Context, categoryID uint) ([]*model.UserGuide, error)
	ListUserGuideContent(ctx context.Context, id uint) ([]*model.UserGuideContent, error)
	SearchUserGuide(ctx context.Context, keyword string) ([]*model.UserGuide, error)
}

// UserGuideUsecase 用户指南用例
type UserGuideUsecase struct {
	repo UserGuideRepo
}

// NewUserGuideUsecase 创建用户指南用例
func NewUserGuideUsecase(repo UserGuideRepo) *UserGuideUsecase {
	return &UserGuideUsecase{
		repo: repo,
	}
}

// ListUserGuideCategory 获取用户指南分类列表
func (uc *UserGuideUsecase) ListUserGuideCategory(ctx context.Context) ([]*model.UserGuideCategory, error) {
	return uc.repo.ListUserGuideCategory(ctx)
}

// ListUserGuide 根据分类查询用户指南列表
func (uc *UserGuideUsecase) ListUserGuide(ctx context.Context, categoryID uint) ([]*model.UserGuide, error) {
	return uc.repo.ListUserGuide(ctx, categoryID)
}

// ListUserGuideContents 获取用户指南内容列表
func (uc *UserGuideUsecase) ListUserGuideContents(ctx context.Context, id uint) ([]*model.UserGuideContent, error) {
	return uc.repo.ListUserGuideContent(ctx, id)
}

// SearchUserGuide 模糊搜索用户指南
func (uc *UserGuideUsecase) SearchUserGuide(ctx context.Context, keyword string) ([]*model.UserGuide, error) {
	return uc.repo.SearchUserGuide(ctx, keyword)
}
