package biz

import (
	"byd_wallet/internal/biz/base"
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
)

const (
	UploadBizTypeDappLogo  = "dapp_logo"
	UploadBizTypeTokenLogo = "token_logo"
)

type FileUsecase struct {
	repo base.S3Repo
}

func NewFileUsecase(repo base.S3Repo) *FileUsecase {
	return &FileUsecase{repo: repo}
}

func (f FileUsecase) GeneratePresignedRequest(ctx context.Context, bizType string) (*base.PresignedHTTPRequest, error) {
	var key string
	switch bizType {
	case UploadBizTypeDappLogo:
		key = fmt.Sprintf("dapp/logo/%s", uuid.New())
	case UploadBizTypeTokenLogo:
		key = fmt.Sprintf("token/logo/uu/%s", uuid.New())
	default:
		key = fmt.Sprintf("%s/%s", bizType, uuid.New())
	}
	return f.repo.GeneratePresignedRequest4ImagePng(ctx, key, time.Minute*10)
}
