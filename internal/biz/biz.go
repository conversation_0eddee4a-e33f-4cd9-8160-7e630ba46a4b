package biz

import (
	"byd_wallet/internal/biz/coindata"
	"byd_wallet/internal/biz/dapp"
	"byd_wallet/internal/biz/gaspool"
	"byd_wallet/internal/biz/rent"
	"byd_wallet/internal/biz/syncer/chain/bitcoin"
	"byd_wallet/internal/biz/syncer/chain/evm"
	"byd_wallet/internal/biz/syncer/chain/evm/evm_l2"
	"byd_wallet/internal/biz/syncer/chain/solana"
	"byd_wallet/internal/biz/syncer/chain/tron"

	"byd_wallet/internal/biz/gaspool/paymaster"
	pmbsc "byd_wallet/internal/biz/gaspool/paymaster/bsc"
	pmevm "byd_wallet/internal/biz/gaspool/paymaster/evm"
	pmtron "byd_wallet/internal/biz/gaspool/paymaster/tron"

	"github.com/google/wire"
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(
	NewUserUsecase,
	NewWalletUsecase,
	rent.NewTronRentUseCase,
	NewMarketUsecase,
	coindata.NewInitCtrl,
	coindata.NewSyncer,
	NewFileUsecase,
	dapp.NewUsecase,
	dapp.NewAdminUsecase,
	NewUserAddressUsecase,
	NewAdminUsecase,
	NewBlockchainNetworkUsecase,
	NewTokenAssetUsecase,
	NewTransactionUsecase,
	NewChainSyncer, NewBlockFetchers, bitcoin.NewBlockFetcher, tron.NewBlockFetcher,
	evm.NewBlockFetcher, evm_l2.NewBlockFetcher, solana.NewBlockFetcher,
	// Use registry-based relayer usecase (now the only approach)
	NewApprovalUsecase,
	NewEVMInternalTxSyncer,
	NewBlockHeightFetcher,
	NewTokenAssetStarUsecase,
	NewTokenContractUsecase,
	NewUserHoldTokenUsecase,
	NewSwapUsecase, NewSwapAdminUsecase,
	NewTokenCollector,
	NewSpotPriceManager,
	gaspool.NewUsecase,
	paymaster.NewPaymasterFactory,
	pmevm.NewPaymasterBuilder,
	pmbsc.NewPaymaster,
	// pmsolana.NewPaymaster, // 替换为带有Redis缓存的版本
	pmtron.NewPaymaster,
	NewTransactionFetcher, tron.NewTransactionFetcher,
	NewAppVersionAdminUsecase,
	NewAppVersionUsecase,
	NewUserGuideAdminUsecase,
	NewUserGuideUsecase,
)
