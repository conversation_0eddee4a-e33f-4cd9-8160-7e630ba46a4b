package biz

import (
	"byd_wallet/common"
	"byd_wallet/internal/biz/base"
	"byd_wallet/internal/biz/dbtx"
	"byd_wallet/model"
	"context"
)

// AdminUserGuideFilter 用户指南过滤器
type AdminUserGuideFilter struct {
	base.Pagination
	CategoryID uint   // 分类ID
	Language   string // 语言
}

// AdminUserGuideCategoryFilter 用户指南分类过滤器
type AdminUserGuideCategoryFilter struct {
	base.Pagination
	Language string // 语言
}

// UserGuideAdminRepo 用户指南仓库接口
type UserGuideAdminRepo interface {
	CreateUserGuide(ctx context.Context, guide *model.UserGuide) error
	UpdateUserGuide(ctx context.Context, guide *model.UserGuide) error
	DeleteUserGuide(ctx context.Context, id uint) error
	GetUserGuide(ctx context.Context, id uint) (*model.UserGuide, error)
	ListUserGuide(ctx context.Context, filter AdminUserGuideFilter) ([]*model.UserGuide, int64, error)

	CreateUserGuideCategory(ctx context.Context, category *model.UserGuideCategory) error
	UpdateUserGuideCategory(ctx context.Context, category *model.UserGuideCategory) error
	DeleteUserGuideCategory(ctx context.Context, id uint) error
	GetUserGuideCategory(ctx context.Context, id uint) (*model.UserGuideCategory, error)
	GetUserGuideCategoryView(ctx context.Context, id uint) (*model.UserGuideCategoryView, error)
	ListUserGuideCategory(ctx context.Context, filter AdminUserGuideCategoryFilter) ([]*model.UserGuideCategoryView, int64, error)
}

// UserGuideAdminUsecase 用户指南用例
type UserGuideAdminUsecase struct {
	repo UserGuideAdminRepo
	s3   base.S3Repo
	tx   dbtx.DBTx
}

// NewUserGuideAdminUsecase 创建用户指南用例
func NewUserGuideAdminUsecase(repo UserGuideAdminRepo, s3 base.S3Repo, tx dbtx.DBTx) *UserGuideAdminUsecase {
	return &UserGuideAdminUsecase{
		repo: repo,
		s3:   s3,
		tx:   tx,
	}
}

// CreateUserGuide 创建用户指南
func (uc *UserGuideAdminUsecase) CreateUserGuide(ctx context.Context, guide *model.UserGuide) error {
	for _, content := range guide.Contents {
		content.PhotoURL = uc.s3.ToAppAccessUrl(ctx, content.PhotoURL)
	}
	return uc.repo.CreateUserGuide(ctx, guide)
}

// UpdateUserGuide 更新用户指南
func (uc *UserGuideAdminUsecase) UpdateUserGuide(ctx context.Context, guide *model.UserGuide) error {
	// 先获取现有指南
	existing, err := uc.repo.GetUserGuide(ctx, guide.ID)
	if err != nil {
		return err
	}

	// 更新字段
	if guide.CategoryID > 0 {
		existing.CategoryID = guide.CategoryID
	}
	if guide.Language != "" {
		existing.Language = guide.Language
	}
	if guide.Title != "" {
		existing.Title = guide.Title
	}
	if guide.Summary != "" {
		existing.Summary = guide.Summary
	}
	existing.Display = guide.Display

	var deleteContents []*model.UserGuideContent
	// 更新内容
	if len(guide.Contents) > 0 {
		deleteContents = existing.Contents
		existing.Contents = guide.Contents
		for _, content := range existing.Contents {
			content.PhotoURL = uc.s3.ToAppAccessUrl(ctx, content.PhotoURL)
		}
	}

	return uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		if err = uc.repo.UpdateUserGuide(ctx, existing); err != nil {
			return err
		}
		for _, content := range deleteContents {
			if err = uc.s3.DeleteObject(ctx, common.S3ObjectKey(content.PhotoURL)); err != nil {
				return err
			}
		}
		return nil
	})
}

// DeleteUserGuide 删除用户指南
func (uc *UserGuideAdminUsecase) DeleteUserGuide(ctx context.Context, id uint) error {
	// 先获取现有指南
	existing, err := uc.repo.GetUserGuide(ctx, id)
	if err != nil {
		return err
	}
	return uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		if err = uc.repo.DeleteUserGuide(ctx, id); err != nil {
			return err
		}
		for _, content := range existing.Contents {
			if err = uc.s3.DeleteObject(ctx, common.S3ObjectKey(content.PhotoURL)); err != nil {
				return err
			}
		}
		return nil
	})
}

// GetUserGuide 获取用户指南详情
func (uc *UserGuideAdminUsecase) GetUserGuide(ctx context.Context, id uint) (*model.UserGuide, error) {
	return uc.repo.GetUserGuide(ctx, id)
}

// ListUserGuide 用户指南列表
func (uc *UserGuideAdminUsecase) ListUserGuide(ctx context.Context, filter AdminUserGuideFilter) ([]*model.UserGuide, int64, error) {
	return uc.repo.ListUserGuide(ctx, filter)
}

// CreateUserGuideCategory 创建用户指南分类
func (uc *UserGuideAdminUsecase) CreateUserGuideCategory(ctx context.Context, category *model.UserGuideCategory) error {
	category.LogoURL = uc.s3.ToAppAccessUrl(ctx, category.LogoURL)
	return uc.repo.CreateUserGuideCategory(ctx, category)
}

// UpdateUserGuideCategory 更新用户指南分类
func (uc *UserGuideAdminUsecase) UpdateUserGuideCategory(ctx context.Context, category *model.UserGuideCategory) error {
	// 先获取现有分类
	existing, err := uc.repo.GetUserGuideCategory(ctx, category.ID)
	if err != nil {
		return err
	}

	// 更新字段
	if category.Language != "" {
		existing.Language = category.Language
	}
	if category.Name != "" {
		existing.Name = category.Name
	}
	var deleteLogURL string
	if category.LogoURL != "" {
		deleteLogURL = existing.LogoURL
		existing.LogoURL = uc.s3.ToAppAccessUrl(ctx, category.LogoURL)
	}
	existing.Display = category.Display

	return uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		if err = uc.repo.UpdateUserGuideCategory(ctx, existing); err != nil {
			return err
		}
		if deleteLogURL != "" {
			if err = uc.s3.DeleteObject(ctx, common.S3ObjectKey(deleteLogURL)); err != nil {
				return err
			}
		}
		return nil
	})
}

// DeleteUserGuideCategory 删除用户指南分类
func (uc *UserGuideAdminUsecase) DeleteUserGuideCategory(ctx context.Context, id uint) error {
	// 先获取现有分类
	existing, err := uc.repo.GetUserGuideCategory(ctx, id)
	if err != nil {
		return err
	}
	return uc.tx.ExecTx(ctx, func(ctx context.Context) error {
		if err = uc.repo.DeleteUserGuideCategory(ctx, id); err != nil {
			return err
		}
		if existing.LogoURL != "" {
			if err = uc.s3.DeleteObject(ctx, common.S3ObjectKey(existing.LogoURL)); err != nil {
				return err
			}
		}
		return nil
	})
}

// GetUserGuideCategory 获取用户指南分类详情
func (uc *UserGuideAdminUsecase) GetUserGuideCategory(ctx context.Context, id uint) (*model.UserGuideCategoryView, error) {
	return uc.repo.GetUserGuideCategoryView(ctx, id)
}

// ListUserGuideCategory 用户指南分类列表
func (uc *UserGuideAdminUsecase) ListUserGuideCategory(ctx context.Context, filter AdminUserGuideCategoryFilter) ([]*model.UserGuideCategoryView, int64, error) {
	return uc.repo.ListUserGuideCategory(ctx, filter)
}
