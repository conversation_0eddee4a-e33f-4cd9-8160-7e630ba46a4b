# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /v1/app-versions/latest:
        get:
            tags:
                - AppVersionService
            description: 获取最新版本
            operationId: AppVersionService_GetLatestAppVersion
            parameters:
                - name: app_type
                  in: query
                  description: APP类型。android,ios
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.walletadmin.v1.AppVersionInfo'
    /v1/dapp:
        get:
            tags:
                - DappSrv
            description: SearchDapp 搜索dapp
            operationId: DappSrv_SearchDapp
            parameters:
                - name: key
                  in: query
                  description: 搜索关键词
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.SearchDappReply'
    /v1/dapp/address/approval:
        post:
            tags:
                - DappSrv
            description: ListApprovalByUserAddress 查询单个用户钱包地址授权记录
            operationId: DappSrv_ListApprovalByUserAddress
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.ListApprovalByUserAddressReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListApprovalByUserAddressReply'
    /v1/dapp/addresses/approval:
        post:
            tags:
                - DappSrv
            description: ListApprovedDappsByUserAddresses 根据用户地址查询已授权的dapp
            operationId: DappSrv_ListApprovedDappsByUserAddresses
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.ListApprovalByUserAddressesReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListApprovedDappsByUserAddressesReply'
    /v1/dapp/approved_addresses:
        post:
            tags:
                - DappSrv
            description: ListApprovedAddresses 根据用户地址筛选出已授权的地址
            operationId: DappSrv_ListApprovedAddresses
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.ListApprovedAddressesReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListApprovedAddressesReply'
    /v1/dapp/blockchain/approval:
        post:
            tags:
                - DappSrv
            description: ListTokenApprovalsByDapp 根据dapp应用查询授权信息
            operationId: DappSrv_ListTokenApprovalsByDapp
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.ListTokenApprovalsByDappReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListTokenApprovalsByDappReply'
    /v1/dapp/category/{id}:
        get:
            tags:
                - DappSrv
            description: GetDappCategory 获取分类详情
            operationId: DappSrv_GetDappCategory
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
                - name: chain_index
                  in: query
                  schema:
                    type: string
                - name: chain_id
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.GetDappCategoryReply'
    /v1/dapp/index:
        get:
            tags:
                - DappSrv
            description: ListDappIndex 获取dapp首页列表
            operationId: DappSrv_ListDappIndex
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListDappIndexReply'
    /v1/dapp/navigation:
        get:
            tags:
                - DappSrv
            description: ListNavigation 获取dapp导航栏配置
            operationId: DappSrv_ListNavigation
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListNavigationReply'
    /v1/dapp/topic/{id}:
        get:
            tags:
                - DappSrv
            description: GetDappTopic 获取专题详情
            operationId: DappSrv_GetDappTopic
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.GetDappTopicReply'
    /v1/gaspool/balance:
        get:
            tags:
                - GasPoolSrv
            description: 查询GasPool余额
            operationId: GasPoolSrv_GetGasPoolBalance
            parameters:
                - name: wallet_id
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.GetGasPoolBalanceReply'
    /v1/gaspool/cash_flow_records:
        get:
            tags:
                - GasPoolSrv
            description: GasPool财务记录列表
            operationId: GasPoolSrv_ListGasPoolCashFlowRecord
            parameters:
                - name: wallet_id
                  in: query
                  schema:
                    type: string
                - name: page
                  in: query
                  schema:
                    type: string
                - name: limit
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListGasPoolCashFlowRecordReply'
    /v1/gaspool/consume_records:
        get:
            tags:
                - GasPoolSrv
            description: GasPool消费记录列表
            operationId: GasPoolSrv_ListGasPoolConsumeRecord
            parameters:
                - name: wallet_id
                  in: query
                  schema:
                    type: string
                - name: page
                  in: query
                  schema:
                    type: string
                - name: limit
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListGasPoolConsumeRecordReply'
    /v1/gaspool/deposit_tokens:
        get:
            tags:
                - GasPoolSrv
            description: 可充值币种列表
            operationId: GasPoolSrv_ListGasPoolDepositToken
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListGasPoolDepositTokenReply'
    /v1/gaspool/paymaster:
        get:
            tags:
                - GasPoolSrv
            description: 获取paymaster address
            operationId: GasPoolSrv_GetPaymaster
            parameters:
                - name: chain_index
                  in: query
                  description: 链索引
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.GetPaymasterReply'
    /v1/gaspool/send_tx:
        post:
            tags:
                - GasPoolSrv
            description: 发送交易
            operationId: GasPoolSrv_SendTx
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.SendTxReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.SendTxReply'
    /v1/gaspool/token_price:
        get:
            tags:
                - GasPoolSrv
            description: 获取GasPool币种价格
            operationId: GasPoolSrv_GetGasPoolTokenPrice
            parameters:
                - name: chain_index
                  in: query
                  schema:
                    type: string
                - name: address
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.GetGasPoolTokenPriceReply'
    /v1/getToken:
        get:
            tags:
                - WalletSrv
            description: 获取指定token
            operationId: WalletSrv_GetToken
            parameters:
                - name: chain_index
                  in: query
                  description: 链索引
                  schema:
                    type: string
                - name: address
                  in: query
                  description: 合约地址
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.Token'
    /v1/listTokenBalance:
        post:
            tags:
                - WalletSrv
            description: 获取地址的资产明细
            operationId: WalletSrv_ListTokenBalance
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.ListTokenBalanceReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListTokenBalanceReply'
    /v1/market/list_popular_token:
        get:
            tags:
                - MarketSrv
            description: 获取热门代币列表
            operationId: MarketSrv_ListPopularToken
            parameters:
                - name: page
                  in: query
                  description: 页码
                  schema:
                    type: string
                - name: limit
                  in: query
                  description: 页容量
                  schema:
                    type: string
                - name: keyword
                  in: query
                  description: 关键字(搜索范围 代币符号 和 合约地址)
                  schema:
                    type: string
                - name: chain_indexes
                  in: query
                  description: 链索引(0,60,...支持查询多个链,查询全部就传空或不传)
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListPopularTokenReply'
    /v1/market/query_coin_price:
        post:
            tags:
                - MarketSrv
            description: 查询币种价格
            operationId: MarketSrv_QueryCoinPrice
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.QueryCoinPriceReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.QueryCoinPriceReply'
    /v1/market/query_currency_usdrate:
        get:
            tags:
                - MarketSrv
            description: 查询USD汇率
            operationId: MarketSrv_QueryCurrencyUSDRate
            parameters:
                - name: currencies
                  in: query
                  description: 法币 // cny,jpy,...
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.CurrencyUSDRate'
    /v1/market/query_simple_kline:
        post:
            tags:
                - MarketSrv
            description: 查询24小时简易k线(仅收盘价)
            operationId: MarketSrv_QuerySimpleKline
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.QuerySimpleKlineReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.QuerySimpleKlineReply'
    /v1/networkList:
        get:
            tags:
                - WalletSrv
            description: 链列表
            operationId: WalletSrv_NetworkList
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.NetworkListReply'
    /v1/queryTxLatestToken:
        post:
            tags:
                - WalletSrv
            description: 获取地址接受转账的最新币种信息
            operationId: WalletSrv_QueryTxLatestToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.QueryTxLatestTokenReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.QueryTxLatestTokenReply'
    /v1/reportInternalTxn:
        post:
            tags:
                - WalletSrv
            description: 上报并更新一笔内部交易
            operationId: WalletSrv_ReportInternalTxn
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.ReportInternalTxnReq'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/swap:
        post:
            tags:
                - SwapService
            description: 兑换
            operationId: SwapService_Swap
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.SwapRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.SwapReply'
    /v1/swap/blockchain_network:
        get:
            tags:
                - SwapService
            description: 查询支持的链
            operationId: SwapService_ListBlockchainNetwork
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListBlockchainNetworkReply'
    /v1/swap/multi_quote:
        post:
            tags:
                - SwapService
            description: 兑换询价
            operationId: SwapService_MultiQuote
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.MultiQuoteRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.MultiQuoteReply'
    /v1/swap/record:
        post:
            tags:
                - SwapService
            description: 添加兑换记录
            operationId: SwapService_AddSwapRecord
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.AddSwapRecordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.SwapRecord'
    /v1/swap/record/list:
        post:
            tags:
                - SwapService
            description: 查询兑换记录列表
            operationId: SwapService_ListSwapRecord
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.ListSwapRecordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListSwapRecordReply'
    /v1/swap/record/{hash}:
        get:
            tags:
                - SwapService
            description: 查询兑换记录
            operationId: SwapService_GetSwapRecord
            parameters:
                - name: hash
                  in: path
                  description: 兑换记录hash
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.SwapRecord'
    /v1/swap/token:
        get:
            tags:
                - SwapService
            description: 查询兑换的币种列表
            operationId: SwapService_ListToken
            parameters:
                - name: chain_index
                  in: query
                  description: 传-1 获取所有
                  schema:
                    type: string
                - name: search_key
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListTokenReply'
    /v1/swap/token/hot:
        get:
            tags:
                - SwapService
            description: 查询热门代币
            operationId: SwapService_ListHotToken
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListHotTokenReply'
    /v1/tokenList:
        get:
            tags:
                - WalletSrv
            description: 获取查询token
            operationId: WalletSrv_TokenList
            parameters:
                - name: chain_indexes
                  in: query
                  description: 链索引(0,60,...支持查询多个链,查询全部就传空或不传)
                  schema:
                    type: string
                - name: query
                  in: query
                  description: 支持名字或者合约地址
                  schema:
                    type: string
                - name: page
                  in: query
                  description: 查询页
                  schema:
                    type: string
                - name: limit
                  in: query
                  description: 每页条数，限制200条
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.TokenListReply'
    /v1/transactionInfo:
        get:
            tags:
                - WalletSrv
            description: 获取交易详情信息
            operationId: WalletSrv_TransactionInfo
            parameters:
                - name: chain_index
                  in: query
                  description: 链索引
                  schema:
                    type: string
                - name: txn
                  in: query
                  description: 交易hash
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.Transaction'
    /v1/transactionList:
        get:
            tags:
                - WalletSrv
            description: 获取交易信息
            operationId: WalletSrv_Transactions
            parameters:
                - name: chain_index
                  in: query
                  description: 链索引
                  schema:
                    type: string
                - name: address
                  in: query
                  description: 查询地址，多个用逗号(,)隔开
                  schema:
                    type: string
                - name: program_id
                  in: query
                  description: 查询合约地址，多个用逗号(,)隔开,不区分合约查询all
                  schema:
                    type: string
                - name: page
                  in: query
                  schema:
                    type: string
                - name: limit
                  in: query
                  description: 每页展示条数
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.TransactionsReply'
    /v1/transactionsByAddress:
        get:
            tags:
                - WalletSrv
            description: 获取交易信息
            operationId: WalletSrv_TransactionsByAddress
            parameters:
                - name: chain_index
                  in: query
                  description: 链索引
                  schema:
                    type: string
                - name: from_address
                  in: query
                  description: 查询from地址，多个用逗号(,)隔开
                  schema:
                    type: string
                - name: to_address
                  in: query
                  description: 查询to地址，多个用逗号(,)隔开
                  schema:
                    type: string
                - name: program_id
                  in: query
                  description: 查询合约地址，多个用逗号(,)隔开
                  schema:
                    type: string
                - name: page
                  in: query
                  schema:
                    type: string
                - name: limit
                  in: query
                  description: 每页展示条数
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.TransactionsReply'
    /v1/tron/rent/add:
        post:
            tags:
                - TronSrv
            description: AddTronRentRecord 创建能量买单
            operationId: TronSrv_AddTronRentRecord
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.AddTronRentRecordReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.AddTronRentRecordReply'
    /v1/tron/rent/preorder:
        post:
            tags:
                - TronSrv
            description: QueryPreorderInfo 查询预订单信息，用于预估租赁费用
            operationId: TronSrv_QueryPreorderInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.QueryPreorderInfoReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.QueryPreorderInfoReply'
    /v1/tron/rent/upload_hash:
        post:
            tags:
                - TronSrv
            description: UploadHash 上传买单哈希
            operationId: TronSrv_UploadHash
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.UploadHashReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.UploadHashReply'
    /v1/user-guide-categories:
        get:
            tags:
                - UserGuideSrv
            description: 获取用户指南分类列表
            operationId: UserGuideSrv_ListUserGuideCategories
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListUserGuideCategoriesReply'
    /v1/user-guides:
        get:
            tags:
                - UserGuideSrv
            description: 根据分类查询用户指南列表
            operationId: UserGuideSrv_ListUserGuides
            parameters:
                - name: category_id
                  in: query
                  description: 分类ID
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListUserGuidesReply'
    /v1/user-guides/search:
        get:
            tags:
                - UserGuideSrv
            description: 模糊搜索用户指南
            operationId: UserGuideSrv_SearchUserGuides
            parameters:
                - name: keyword
                  in: query
                  description: 搜索关键词
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.SearchUserGuidesReply'
    /v1/user-guides/{id}/contents:
        get:
            tags:
                - UserGuideSrv
            description: 获取用户指南内容列表
            operationId: UserGuideSrv_ListUserGuideContent
            parameters:
                - name: id
                  in: path
                  description: 用户指南ID
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.ListUserGuideContentReply'
    /v1/wallet/ids:
        get:
            tags:
                - UserSrv
            description: 获取wallet Id
            operationId: UserSrv_GetWalletIds
            parameters:
                - name: addresses
                  in: query
                  description: 用户地址，多个地址以逗号隔开，如：0x123...23,0x23...434
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.GeWalletIdsReply'
    /v1/wallet/update_wallet_id:
        post:
            tags:
                - UserSrv
            description: 更新wallet Id
            operationId: UserSrv_UpdateWalletID
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.UpdateWalletIDReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.UpdateWalletIDReply'
    /v1/wallet/user_register:
        post:
            tags:
                - UserSrv
            description: 生成wallet Id
            operationId: UserSrv_UserRegister
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.wallet.v1.UserRegisterReq'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.wallet.v1.UserRegisterReply'
components:
    schemas:
        api.wallet.v1.AddSwapRecordRequest:
            type: object
            properties:
                from:
                    $ref: '#/components/schemas/api.wallet.v1.SwapToken'
                to:
                    $ref: '#/components/schemas/api.wallet.v1.SwapToken'
                path:
                    type: string
                dex:
                    type: string
                slippage:
                    type: string
                    description: 滑点百分比，比如传1代表滑点1%
                hash:
                    type: string
                    description: 客户端上传的交易hash
                approval_hash:
                    type: string
                    description: 授权hash
                swap_price:
                    type: string
                    description: 汇率
                fee_rate:
                    type: string
                    description: 费率
                estimated_time:
                    type: string
                    description: 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
                gas_fee:
                    type: string
                    description: 矿工费
                order_id:
                    type: string
        api.wallet.v1.AddTronRentRecordReply:
            type: object
            properties:
                order_id:
                    type: string
                transaction:
                    type: string
                pledge_trx_num:
                    type: string
        api.wallet.v1.AddTronRentRecordReq:
            type: object
            properties:
                from_address:
                    type: string
                    description: 用户地址（创建买单的钱包地址）
                pledge_address:
                    type: string
                    description: 能量/带宽接收地址
                pledge_num:
                    type: string
                    description: 能量数量（需大于32000）
        api.wallet.v1.Approval:
            type: object
            properties:
                chain_index:
                    type: string
                owner_address:
                    type: string
                spender_address:
                    type: string
                token_address:
                    type: string
                value:
                    type: string
        api.wallet.v1.BaseSignPayload:
            type: object
            properties:
                sign:
                    type: string
                    description: 签名数据
                message:
                    type: string
                    description: 签名消息 如注册walletId:"[{\"chainIndex\":60,\"address\":\"******************************************\"}]" UpDataWalletId:"{\"oldWalletId\":\"888888\",\"newWalletId\":\"99999999\"}"
                address:
                    type: string
                    description: 签名地址
                chain_index:
                    type: string
                    description: chain_index
        api.wallet.v1.BlockchainNetwork:
            type: object
            properties:
                chain_id:
                    type: string
        api.wallet.v1.CoinPrice:
            type: object
            properties:
                chain_index:
                    type: string
                    description: 链索引
                address:
                    type: string
                    description: 合约地址
                price:
                    type: string
                    description: 价格(USD)
                last_updated_at:
                    type: string
                    description: 更新时间（时间戳，单位秒）
        api.wallet.v1.CurrencyUSDRate:
            type: object
            properties:
                value:
                    type: object
                    additionalProperties:
                        type: string
                    description: key:法币(cny,jpy等小写) value:汇率
        api.wallet.v1.Dapp:
            type: object
            properties:
                id:
                    type: string
                logo:
                    type: string
                link:
                    type: string
                tags:
                    type: string
                hot:
                    type: boolean
                i18ns:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.DappI18N'
                networks:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.BlockchainNetwork'
        api.wallet.v1.DappCategory:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                summary:
                    type: string
                language:
                    type: string
                dapps:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.Dapp'
        api.wallet.v1.DappI18N:
            type: object
            properties:
                name:
                    type: string
                summary:
                    type: string
        api.wallet.v1.DappIndex:
            type: object
            properties:
                topic:
                    $ref: '#/components/schemas/api.wallet.v1.DappTopic'
                category:
                    $ref: '#/components/schemas/api.wallet.v1.DappCategory'
        api.wallet.v1.DappNavigation:
            type: object
            properties:
                dapp_category_id:
                    type: string
                name:
                    type: string
        api.wallet.v1.DappTopic:
            type: object
            properties:
                id:
                    type: string
                background_url:
                    type: string
                name:
                    type: string
                summary:
                    type: string
                language:
                    type: string
                title:
                    type: string
                top_title:
                    type: string
                bottom_title:
                    type: string
                dapps:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.Dapp'
        api.wallet.v1.GasPoolCashFlowRecord:
            type: object
            properties:
                created_at:
                    type: string
                    description: 创建时间(时间戳,秒)
                chain_index:
                    type: string
                    description: 链索引
                token_address:
                    type: string
                    description: 币合约地址(矿币为空)
                tx_hash:
                    type: string
                    description: 用户交易hash
                amount:
                    type: string
                    description: GasPool出入金额(USDT,不带精度)
                record_type:
                    type: string
                    description: 记录类型(deposit:充值, refund:退回)
                token_amount:
                    type: string
                    description: GasPool出入币种数量(不带精度)
                status:
                    type: string
                    description: 记录状态(success:执行成功, fail:执行失败, pending:执行中)
        api.wallet.v1.GasPoolConsumeRecord:
            type: object
            properties:
                created_at:
                    type: string
                    description: 创建时间(时间戳,秒)
                chain_index:
                    type: string
                    description: 链索引
                status:
                    type: string
                    description: 记录状态(success:执行成功, fail:执行失败, pending:执行中)
                tx_hash:
                    type: string
                    description: 用户交易hash
                amount:
                    type: string
                    description: GasPool扣款金额(USDT,不带精度)
                record_type:
                    type: string
                    description: 记录类型(transfer:转账, swap:兑换, deposit_without_gas:充值预扣)
        api.wallet.v1.GasPoolDepositToken:
            type: object
            properties:
                name:
                    type: string
                    description: 币名称
                symbol:
                    type: string
                    description: 币符号
                decimals:
                    type: string
                    description: 币精度
                logo_url:
                    type: string
                    description: 币图标
                address:
                    type: string
                    description: 币地址
                chain_index:
                    type: string
                    description: 链索引
                chain_id:
                    type: string
                    description: 链ID
                min_deposit_amount:
                    type: string
                    description: 最小充值金额(不带精度)
                deposit_address:
                    type: string
                    description: 充值地址
        api.wallet.v1.GasPoolUserStats:
            type: object
            properties:
                total_deposit_amount:
                    type: string
                    description: 充值额度
                total_reduce_amount:
                    type: string
                    description: 累计消耗
                total_credit_amount:
                    type: string
                    description: 积分额度 // 暂无
                total_reduce_count:
                    type: string
                    description: 累计使用次数
                chain_count:
                    type: string
                    description: 支持公链数
        api.wallet.v1.GeWalletIdsReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.GetWalletId'
                    description: walletId 列表
        api.wallet.v1.GetDappCategoryReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.Dapp'
        api.wallet.v1.GetDappTopicReply:
            type: object
            properties:
                topic:
                    $ref: '#/components/schemas/api.wallet.v1.DappTopic'
        api.wallet.v1.GetGasPoolBalanceReply:
            type: object
            properties:
                balance:
                    type: string
                    description: 余额(USDT,不带精度)
                stats:
                    allOf:
                        - $ref: '#/components/schemas/api.wallet.v1.GasPoolUserStats'
                    description: 统计数据
        api.wallet.v1.GetGasPoolTokenPriceReply:
            type: object
            properties:
                price:
                    type: string
                    description: 价格(USDT,带精度)
                timestamp:
                    type: string
                    description: 时间戳(秒)
        api.wallet.v1.GetPaymasterReply:
            type: object
            properties:
                address:
                    type: string
        api.wallet.v1.GetWalletId:
            type: object
            properties:
                wallet_id:
                    type: string
                address:
                    type: string
        api.wallet.v1.ListApprovalByUserAddressReply:
            type: object
            properties:
                address:
                    $ref: '#/components/schemas/api.wallet.v1.UserAddress'
                network:
                    $ref: '#/components/schemas/api.wallet.v1.BlockchainNetwork'
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.TokenDappApproval'
        api.wallet.v1.ListApprovalByUserAddressReq:
            type: object
            properties:
                address:
                    type: string
                    description: 用户钱包地址
                chain_index:
                    type: string
        api.wallet.v1.ListApprovalByUserAddressesReq:
            type: object
            properties:
                addresses:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.UserAddress'
        api.wallet.v1.ListApprovedAddressesReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.UserAddress'
        api.wallet.v1.ListApprovedAddressesReq:
            type: object
            properties:
                addresses:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.UserAddress'
        api.wallet.v1.ListApprovedDappsByUserAddressesReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.UserApprovedDapp'
        api.wallet.v1.ListBlockchainNetworkReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.Network'
        api.wallet.v1.ListDappIndexReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.DappIndex'
        api.wallet.v1.ListGasPoolCashFlowRecordReply:
            type: object
            properties:
                total_count:
                    type: string
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.GasPoolCashFlowRecord'
                token_list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.GasPoolDepositToken'
        api.wallet.v1.ListGasPoolConsumeRecordReply:
            type: object
            properties:
                total_count:
                    type: string
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.GasPoolConsumeRecord'
        api.wallet.v1.ListGasPoolDepositTokenReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.GasPoolDepositToken'
        api.wallet.v1.ListHotTokenReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.SwappableHotToken'
        api.wallet.v1.ListNavigationReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.DappNavigation'
        api.wallet.v1.ListPopularTokenReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.TokenWithMarket'
                total_count:
                    type: string
                    description: 总记录数
        api.wallet.v1.ListSwapRecordReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.SwapRecord'
                count:
                    type: string
                    description: 总条数
        api.wallet.v1.ListSwapRecordRequest:
            type: object
            properties:
                page:
                    type: string
                    description: 页码 从1开始
                limit:
                    type: string
                    description: 每页展示条数
                addresses:
                    type: array
                    items:
                        type: string
                    description: 用户钱包地址
        api.wallet.v1.ListTokenApprovalsByDappReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.TokenApproval'
        api.wallet.v1.ListTokenApprovalsByDappReq:
            type: object
            properties:
                spender_address:
                    type: string
                chain_index:
                    type: string
                addresses:
                    type: array
                    items:
                        type: string
                    description: 用户钱包地址
        api.wallet.v1.ListTokenBalanceReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.WalletBalance'
        api.wallet.v1.ListTokenBalanceReq:
            type: object
            properties:
                addresses:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.WalletAddress'
        api.wallet.v1.ListTokenReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.Token'
        api.wallet.v1.ListUserGuideCategoriesReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.UserGuideCategoryInfo'
                    description: 分类列表
        api.wallet.v1.ListUserGuideContentReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.UserGuideContentInfo'
                    description: 内容列表
        api.wallet.v1.ListUserGuidesReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.UserGuideInfo'
                    description: 用户指南列表
        api.wallet.v1.MultiQuoteReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.QuoteInfo'
        api.wallet.v1.MultiQuoteRequest:
            type: object
            properties:
                from:
                    $ref: '#/components/schemas/api.wallet.v1.SwapToken'
                to:
                    $ref: '#/components/schemas/api.wallet.v1.SwapToken'
                slippage:
                    type: string
                    description: 滑点百分比，比如传1代表滑点1%
        api.wallet.v1.Network:
            type: object
            properties:
                name:
                    type: string
                    description: chain名称
                symbol:
                    type: string
                    description: symbol 标识
                chain_index:
                    type: string
                    description: chainIndex
                chain_id:
                    type: string
                    description: 每条链的标记
                decimals:
                    type: string
                    description: 代币精度
                blockchain_url:
                    type: string
                    description: 链图标
                token_url:
                    type: string
                    description: token图标
                explorer_url:
                    type: string
                    description: 区块浏览器 地址
                gas_token_symbol:
                    type: string
                    description: 支付gas的 symbol
                chain_type:
                    type: string
                    description: chain 类型
                handle:
                    type: string
                    description: handle
                sort_order:
                    type: string
                    description: 排序
                chain_name:
                    type: string
                    description: 链名称
        api.wallet.v1.NetworkListReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.Network'
                    description: 链列表
        api.wallet.v1.QueryCoinPriceReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.CoinPrice'
        api.wallet.v1.QueryCoinPriceReq:
            type: object
            properties:
                token_ids:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.TokenID'
                    description: 查询指定的token
        api.wallet.v1.QueryPreorderInfoReply:
            type: object
            properties:
                order_price:
                    type: string
                pledge_num:
                    type: string
                pledge_trx_num:
                    type: string
        api.wallet.v1.QueryPreorderInfoReq:
            type: object
            properties:
                from_address:
                    type: string
                    description: 用户地址（创建买单的钱包地址）
                pledge_address:
                    type: string
                    description: 能量/带宽接收地址
                pledge_num:
                    type: string
                    description: 能量数量（需大于32000）
        api.wallet.v1.QuerySimpleKlineReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.SimpleKline'
        api.wallet.v1.QuerySimpleKlineReq:
            type: object
            properties:
                token_ids:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.TokenID'
                    description: 查询指定的token
        api.wallet.v1.QueryTxLatestTokenReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.TokenWithWallet'
        api.wallet.v1.QueryTxLatestTokenReq:
            type: object
            properties:
                chains:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.WalletAddress4Chain'
        api.wallet.v1.QuoteInfo:
            type: object
            properties:
                receive_token_amount:
                    type: string
                    description: 预估目标代币接收数量
                min_receive_token_amount:
                    type: string
                    description: 最小目标代币接收数量
                slippage:
                    type: string
                    description: 滑点百分比，比如1代表滑点1%
                dex:
                    type: string
                    description: 兑换平台
                dex_logo:
                    type: string
                    description: 兑换平台logo
                fee_rate:
                    type: string
                    description: 交易费率
                estimated_time:
                    type: string
                    description: 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
                path:
                    type: string
                    description: 用于传给Swap接口
                max_from_token_amount:
                    type: string
                    description: 最大兑换数量
                min_from_token_amount:
                    type: string
                    description: 最小兑换数量
        api.wallet.v1.ReportInternalTxnReq:
            type: object
            properties:
                chain_index:
                    type: string
                    description: chainIndex
                txn:
                    type: string
                    description: hash
        api.wallet.v1.SearchDappReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.Dapp'
        api.wallet.v1.SearchUserGuidesReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.UserGuideInfo'
                    description: 用户指南列表
        api.wallet.v1.SendTxReply:
            type: object
            properties:
                tx_hash:
                    type: string
        api.wallet.v1.SendTxReq:
            type: object
            properties:
                chain_index:
                    type: string
                    description: 链索引
                raw_tx:
                    type: string
                    description: 原始交易
                tx_type:
                    type: string
                    description: |-
                        交易类型 (
                         deposit:充值,deposit_pre_reduce_gas:预扣GasPool充值,
                         transfer:转账,swap:兑换
                         )
        api.wallet.v1.SimpleKline:
            type: object
            properties:
                chain_index:
                    type: string
                    description: 链索引
                address:
                    type: string
                    description: 合约地址
                times:
                    type: array
                    items:
                        type: string
                    description: k线时间（时间戳，单位秒） // 升序
                close_prices:
                    type: array
                    items:
                        type: string
                    description: 收盘价(USD) // 按时间升序
        api.wallet.v1.SwapDetail:
            type: object
            properties:
                chain_index:
                    type: string
                    description: 链索引
                explorer_url:
                    type: string
                    description: 区块浏览器url
                hash:
                    type: string
                    description: 交易hash
                status:
                    type: string
                    description: 交易状态
        api.wallet.v1.SwapRecord:
            type: object
            properties:
                id:
                    type: string
                    description: 兑换记录id
                swapped_at:
                    type: string
                    description: 兑换时间(unix时间戳)
                status:
                    type: string
                    description: |-
                        兑换状态
                         pending(处理中), success(兑换成功), fail(兑换失败)
                from:
                    $ref: '#/components/schemas/api.wallet.v1.SwapToken'
                to:
                    $ref: '#/components/schemas/api.wallet.v1.SwapToken'
                gas_fee:
                    type: string
                    description: 用户兑换消耗的GasFee
                fee_rate:
                    type: string
                    description: 兑换手续费率
                hash:
                    type: string
                    description: 用户发起的交易hash
                approval_hash:
                    type: string
                    description: 授权hash
                height:
                    type: string
                    description: 区块高度
                dex:
                    type: string
                    description: 兑换平台名称
                dex_logo:
                    type: string
                    description: 兑换平台logo url
                swap_price:
                    type: string
                    description: 兑换价格
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.SwapDetail'
                    description: 兑换详情列表
                finished_at:
                    type: string
                    description: 结束时间(unix时间戳)
                estimated_time:
                    type: string
                    description: 预估时间：1(1-3分钟)，2(1-10分钟)，3(5-30分钟)
        api.wallet.v1.SwapReply:
            type: object
            properties:
                from:
                    type: string
                    description: 调用合约请求地址
                to:
                    type: string
                    description: 被调用合约地址
                value:
                    type: string
                    description: 调用传入金额
                gas_limit:
                    type: string
                gas_price:
                    type: string
                data:
                    type: string
                    description: 合约调用数据
                approve_address:
                    type: string
                    description: 授权地址
                to_type:
                    type: string
                    description: Deposit coin type,0 (common address, no call data);1 (common address,have call data);2(contract address,have call data )
                platform_addr:
                    type: string
                    description: Only the SWFT channel,deposit address
                swap_price:
                    type: string
                    description: 兑换汇率
                raw_data:
                    type: string
                    description: 第三方原始数据
                order_id:
                    type: string
        api.wallet.v1.SwapRequest:
            type: object
            properties:
                from:
                    $ref: '#/components/schemas/api.wallet.v1.SwapToken'
                to:
                    $ref: '#/components/schemas/api.wallet.v1.SwapToken'
                path:
                    type: string
                dex:
                    type: string
                slippage:
                    type: string
                    description: 滑点百分比，比如传1代表滑点1%
        api.wallet.v1.SwapToken:
            type: object
            properties:
                token_address:
                    type: string
                    description: 代币合约地址
                address:
                    type: string
                    description: 用户地址
                chain_index:
                    type: string
                    description: 链
                amount:
                    type: string
                    description: 数量
                token_logo:
                    type: string
                    description: token logo url
                symbol:
                    type: string
                    description: 标的
                decimals:
                    type: string
                    description: 精度
        api.wallet.v1.SwappableHotToken:
            type: object
            properties:
                chain_index:
                    type: string
                name:
                    type: string
                symbol:
                    type: string
                decimals:
                    type: string
                logo_url:
                    type: string
                address:
                    type: string
                chain_id:
                    type: string
                sort_order:
                    type: string
                is_all:
                    type: boolean
                    description: 是否是全部的分组
        api.wallet.v1.Token:
            type: object
            properties:
                chain_index:
                    type: string
                name:
                    type: string
                symbol:
                    type: string
                decimals:
                    type: string
                logo_url:
                    type: string
                address:
                    type: string
                chain_id:
                    type: string
        api.wallet.v1.TokenApproval:
            type: object
            properties:
                token:
                    $ref: '#/components/schemas/api.wallet.v1.Token'
                approval:
                    $ref: '#/components/schemas/api.wallet.v1.Approval'
        api.wallet.v1.TokenBalance:
            type: object
            properties:
                chain_index:
                    type: string
                    description: 链索引
                address:
                    type: string
                    description: token地址
                name:
                    type: string
                    description: token名称
                symbol:
                    type: string
                    description: token符号
                decimals:
                    type: string
                    description: token精度
                raw_balance:
                    type: string
                    description: token原始数量
                price:
                    type: string
                    description: token价格(USD)
                logo_url:
                    type: string
                    description: token图标
        api.wallet.v1.TokenDappApproval:
            type: object
            properties:
                token:
                    $ref: '#/components/schemas/api.wallet.v1.Token'
                dapp:
                    $ref: '#/components/schemas/api.wallet.v1.Dapp'
                approval:
                    $ref: '#/components/schemas/api.wallet.v1.Approval'
        api.wallet.v1.TokenID:
            type: object
            properties:
                chain_index:
                    type: string
                    description: 链索引
                address:
                    type: string
                    description: 合约地址
        api.wallet.v1.TokenListReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.Token'
                count:
                    type: string
        api.wallet.v1.TokenWithMarket:
            type: object
            properties:
                chain_index:
                    type: string
                    description: 链索引
                address:
                    type: string
                    description: 合约地址
                name:
                    type: string
                    description: 币名称
                symbol:
                    type: string
                    description: 币符号
                decimals:
                    type: string
                    description: 币精度位数
                circulating_supply:
                    type: string
                    description: 流动性
                trading_volume_24h:
                    type: string
                    description: 24小时成交额
                price_change_percentage_24h:
                    type: string
                    description: 24小时涨跌幅（百分比）
                logo_url:
                    type: string
                    description: 币图标
                price:
                    type: string
                    description: 币价格(usd)
        api.wallet.v1.TokenWithWallet:
            type: object
            properties:
                wallet_address:
                    type: string
                chain_index:
                    type: string
                name:
                    type: string
                symbol:
                    type: string
                decimals:
                    type: string
                logo_url:
                    type: string
                address:
                    type: string
                chain_id:
                    type: string
        api.wallet.v1.Transaction:
            type: object
            properties:
                txn:
                    type: string
                    description: 交易hash
                from_address:
                    type: string
                    description: 来源地址
                to_address:
                    type: string
                    description: 接收地址
                value:
                    type: string
                    description: 转账余额
                fee:
                    type: string
                    description: 交易手续费
                method:
                    type: string
                    description: 交易方法
                program_id:
                    type: string
                    description: 合约地址
                timestamp:
                    type: string
                    description: 链上完成时间
                status:
                    type: string
                    description: 上链状态 success、fail
                block_number:
                    type: string
                    description: 所在高度
                chain_index:
                    type: string
                    description: chain_index
        api.wallet.v1.TransactionsReply:
            type: object
            properties:
                count:
                    type: string
                    description: 总条数
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.Transaction'
                    description: transaction
        api.wallet.v1.UpdateWalletIDReply:
            type: object
            properties:
                wallet_id:
                    type: string
        api.wallet.v1.UpdateWalletIDReq:
            type: object
            properties:
                base:
                    $ref: '#/components/schemas/api.wallet.v1.BaseSignPayload'
        api.wallet.v1.UploadHashReply:
            type: object
            properties: {}
        api.wallet.v1.UploadHashReq:
            type: object
            properties:
                order_id:
                    type: string
                from_hash:
                    type: string
                signed_data:
                    type: string
        api.wallet.v1.UserAddress:
            type: object
            properties:
                address:
                    type: string
                chain_index:
                    type: string
        api.wallet.v1.UserApprovedDapp:
            type: object
            properties:
                addresses:
                    type: array
                    items:
                        type: string
                dapp:
                    $ref: '#/components/schemas/api.wallet.v1.Dapp'
                network:
                    $ref: '#/components/schemas/api.wallet.v1.BlockchainNetwork'
                spender:
                    type: string
        api.wallet.v1.UserGuideCategoryInfo:
            type: object
            properties:
                id:
                    type: string
                    description: 分类ID
                language:
                    type: string
                    description: 语言
                name:
                    type: string
                    description: 分类名称
                logo_url:
                    type: string
                    description: 图标URL
        api.wallet.v1.UserGuideContentInfo:
            type: object
            properties:
                id:
                    type: string
                    description: 内容ID
                content:
                    type: string
                    description: 正文文本
                photo_url:
                    type: string
                    description: 图片URL
        api.wallet.v1.UserGuideInfo:
            type: object
            properties:
                id:
                    type: string
                    description: 用户指南ID
                category_id:
                    type: string
                    description: 分类ID
                language:
                    type: string
                    description: 语言
                title:
                    type: string
                    description: 标题
                summary:
                    type: string
                    description: 简介
        api.wallet.v1.UserRegisterReply:
            type: object
            properties:
                wallet_id:
                    type: string
        api.wallet.v1.UserRegisterReq:
            type: object
            properties:
                base:
                    $ref: '#/components/schemas/api.wallet.v1.BaseSignPayload'
        api.wallet.v1.WalletAddress:
            type: object
            properties:
                address:
                    type: string
                    description: 钱包地址
                chain_indexes:
                    type: array
                    items:
                        type: string
                    description: 链索引
        api.wallet.v1.WalletAddress4Chain:
            type: object
            properties:
                addresses:
                    type: array
                    items:
                        type: string
                    description: 钱包地址
                chain_index:
                    type: string
        api.wallet.v1.WalletBalance:
            type: object
            properties:
                address:
                    type: string
                    description: 钱包地址
                balances:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.wallet.v1.TokenBalance'
        api.walletadmin.v1.AppVersionI18N:
            type: object
            properties:
                language:
                    type: string
                    description: 语言代码(zh,en,ja,es)
                description:
                    type: string
                    description: 版本描述
            description: 多语言描述
        api.walletadmin.v1.AppVersionInfo:
            type: object
            properties:
                id:
                    type: string
                    description: 版本ID
                version:
                    type: string
                    description: 版本号
                app_type:
                    type: string
                    description: APP类型
                download_url:
                    type: string
                    description: 下载地址
                force_update:
                    type: boolean
                    description: 强制更新
                i18n_descriptions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.walletadmin.v1.AppVersionI18N'
                    description: 多语言描述
                created_at:
                    type: string
                    description: 创建时间
                updated_at:
                    type: string
                    description: 更新时间
                reminder_type:
                    type: integer
                    description: 提醒类型
                    format: enum
                official_download_url:
                    type: string
                    description: 官方下载地址
            description: 版本信息
tags:
    - name: AppVersionService
    - name: DappSrv
    - name: GasPoolSrv
    - name: MarketSrv
    - name: SwapService
    - name: TronSrv
    - name: UserGuideSrv
    - name: UserSrv
    - name: WalletSrv
